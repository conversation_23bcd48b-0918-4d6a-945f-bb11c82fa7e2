#!/bin/bash

# 套保策略 Celery Worker 启动脚本
# 用于启动独立的套保策略服务进程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 配置文件路径
WORKER_CONFIG="$SCRIPT_DIR/worker_config.py"
PID_FILE="$PROJECT_ROOT/logs/hedging_worker.pid"
LOG_FILE="$PROJECT_ROOT/logs/hedging_worker.log"

# 确保日志目录存在
mkdir -p "$PROJECT_ROOT/logs"

log_info "套保策略 Celery Worker 管理脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "PID文件: $PID_FILE"
log_info "日志文件: $LOG_FILE"

# 检查 conda 环境
check_conda_env() {
    if [ -z "$CONDA_DEFAULT_ENV" ]; then
        log_error "未检测到 conda 环境，请先激活 conda 环境："
        log_info "conda activate crypt_carry312"
        exit 1
    fi
    log_success "当前 conda 环境: $CONDA_DEFAULT_ENV"
}

# 检查 Redis 连接
check_redis() {
    log_info "检查 Redis 连接..."
    
    # 尝试连接 Redis
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            log_success "Redis 连接正常"
        else
            log_warning "Redis 连接失败，请确保 Redis 服务已启动"
            log_info "启动 Redis: redis-server"
            return 1
        fi
    else
        log_warning "未找到 redis-cli，无法检查 Redis 连接"
        log_info "请确保 Redis 已安装并启动"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查 Python 依赖..."
    
    # 检查 celery
    if ! python -c "import celery" 2>/dev/null; then
        log_error "未找到 celery，请安装："
        log_info "pip install celery[redis]"
        exit 1
    fi
    
    # 检查 redis
    if ! python -c "import redis" 2>/dev/null; then
        log_error "未找到 redis-py，请安装："
        log_info "pip install redis"
        exit 1
    fi
    
    log_success "Python 依赖检查通过"
}

# 启动 Worker
start_worker() {
    log_info "启动套保策略 Celery Worker..."
    
    # 检查环境
    check_conda_env
    check_dependencies
    check_redis
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_warning "Worker 已在运行 (PID: $PID)"
            return 0
        else
            log_info "清理过期的 PID 文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 设置环境变量
    export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"
    export CELERY_CONFIG_MODULE="crypt_carry.strategies.hedging.tasks.celery_app"
    
    # 启动 Celery Worker
    cd "$PROJECT_ROOT"
    
    log_info "启动命令: celery -A crypt_carry.strategies.hedging.tasks.celery_app worker"
    log_info "队列: hedging_orders,single_orders,monitoring,status"
    log_info "并发数: 4"
    log_info "日志级别: INFO"
    
    nohup celery -A crypt_carry.strategies.hedging.tasks.celery_app worker \
        --loglevel=INFO \
        --concurrency=4 \
        --queues=hedging_orders,single_orders,monitoring,status \
        --hostname=hedging_worker@%h \
        --pidfile="$PID_FILE" \
        --logfile="$LOG_FILE" \
        --detach
    
    # 等待启动
    sleep 3
    
    # 检查是否启动成功
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_success "套保策略 Worker 启动成功 (PID: $PID)"
            log_info "日志文件: $LOG_FILE"
            log_info "使用 '$0 logs' 查看实时日志"
        else
            log_error "Worker 启动失败"
            exit 1
        fi
    else
        log_error "Worker 启动失败，未找到 PID 文件"
        exit 1
    fi
}

# 停止 Worker
stop_worker() {
    log_info "停止套保策略 Celery Worker..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "停止 Worker (PID: $PID)..."
            kill "$PID"
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    break
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p "$PID" > /dev/null 2>&1; then
                log_warning "强制停止 Worker..."
                kill -9 "$PID"
            fi
            
            rm -f "$PID_FILE"
            log_success "Worker 已停止"
        else
            log_warning "Worker 未运行"
            rm -f "$PID_FILE"
        fi
    else
        log_warning "未找到 PID 文件，Worker 可能未运行"
    fi
}

# 检查状态
check_status() {
    log_info "检查套保策略 Worker 状态..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_success "Worker 正在运行 (PID: $PID)"
            
            # 显示进程信息
            ps -p "$PID" -o pid,ppid,cmd,etime,pcpu,pmem
            
            # 检查 Celery 状态
            if command -v celery >/dev/null 2>&1; then
                log_info "Celery Worker 状态:"
                export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"
                cd "$PROJECT_ROOT"
                celery -A crypt_carry.strategies.hedging.tasks.celery_app inspect active 2>/dev/null || true
            fi
        else
            log_error "Worker 未运行 (PID 文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        log_warning "Worker 未运行 (未找到 PID 文件)"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示 Worker 日志 (按 Ctrl+C 退出):"
        tail -f "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 重启 Worker
restart_worker() {
    log_info "重启套保策略 Worker..."
    stop_worker
    sleep 2
    start_worker
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_worker
        ;;
    "stop")
        stop_worker
        ;;
    "restart")
        restart_worker
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start      启动 Worker (默认)"
        echo "  stop       停止 Worker"
        echo "  restart    重启 Worker"
        echo "  status     检查 Worker 状态"
        echo "  logs       查看 Worker 日志"
        echo "  help       显示此帮助信息"
        echo ""
        echo "环境要求:"
        echo "  - 激活的 conda 环境"
        echo "  - Redis 服务运行中"
        echo "  - Python 依赖已安装"
        echo ""
        echo "示例:"
        echo "  conda activate crypt_carry312"
        echo "  $0 start"
        echo "  $0 status"
        echo "  $0 logs"
        ;;
    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
