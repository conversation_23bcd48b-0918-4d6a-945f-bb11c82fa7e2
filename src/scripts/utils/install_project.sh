#!/bin/bash

# Crypt Carry 项目安装脚本
# 用于初始化项目环境和依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

log_info "Crypt Carry 项目安装脚本"
log_info "项目根目录: $PROJECT_ROOT"

# 检查必要的命令
log_info "检查系统依赖..."
MISSING_DEPS=()

if ! check_command "conda"; then
    MISSING_DEPS+=("conda")
fi

if ! check_command "node"; then
    MISSING_DEPS+=("node")
fi

if ! check_command "yarn"; then
    MISSING_DEPS+=("yarn")
fi

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    log_error "缺少以下依赖: ${MISSING_DEPS[*]}"
    log_info "请先安装缺少的依赖："
    for dep in "${MISSING_DEPS[@]}"; do
        case $dep in
            "conda")
                log_info "  - Conda: https://docs.conda.io/en/latest/miniconda.html"
                ;;
            "node")
                log_info "  - Node.js: https://nodejs.org/"
                ;;
            "yarn")
                log_info "  - Yarn: npm install -g yarn"
                ;;
        esac
    done
    exit 1
fi

log_success "系统依赖检查通过"

# 创建conda环境
log_info "设置Python环境..."
cd "$PROJECT_ROOT"

ENV_NAME="crypt_carry312"
if conda env list | grep -q "^$ENV_NAME "; then
    log_warning "Conda环境 $ENV_NAME 已存在"
    read -p "是否要重新创建环境? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除现有环境..."
        conda env remove -n $ENV_NAME -y
        log_info "创建新的conda环境..."
        conda create -n $ENV_NAME python=3.12 -y
    else
        log_info "使用现有环境"
    fi
else
    log_info "创建conda环境: $ENV_NAME"
    conda create -n $ENV_NAME python=3.12 -y
fi

log_success "Python环境设置完成"

# 激活环境并安装Python依赖
log_info "安装Python依赖..."
eval "$(conda shell.bash hook)"
conda activate $ENV_NAME

if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
    log_success "Python依赖安装完成"
else
    log_warning "requirements.txt 文件不存在"
fi

# 安装前端依赖
log_info "安装前端依赖..."
FRONTEND_DIR="$PROJECT_ROOT/src/crypt_carry/web/frontend"
if [ -d "$FRONTEND_DIR" ]; then
    cd "$FRONTEND_DIR"
    if [ -f "package.json" ]; then
        yarn install
        log_success "前端依赖安装完成"
    else
        log_warning "package.json 文件不存在"
    fi
else
    log_warning "前端目录不存在: $FRONTEND_DIR"
fi

# 创建必要的目录
log_info "创建项目目录..."
cd "$PROJECT_ROOT"
mkdir -p logs/hedging logs/funding_rate logs/web
mkdir -p data/cache data/config
log_success "项目目录创建完成"

# 设置脚本执行权限
log_info "设置脚本执行权限..."
find src/scripts -name "*.sh" -exec chmod +x {} \;
log_success "脚本权限设置完成"

# 创建配置文件示例
log_info "创建配置文件..."
CONFIG_DIR="$PROJECT_ROOT/src/crypt_carry/config"
if [ ! -f "$CONFIG_DIR/config.yaml" ] && [ -f "$CONFIG_DIR/config.yaml.example" ]; then
    cp "$CONFIG_DIR/config.yaml.example" "$CONFIG_DIR/config.yaml"
    log_info "已创建配置文件: $CONFIG_DIR/config.yaml"
fi

log_success "项目安装完成!"
log_info ""
log_info "下一步操作："
log_info "1. 激活conda环境: conda activate $ENV_NAME"
log_info "2. 配置交易所API密钥（如需要）"
log_info "3. 启动Web服务: ./src/scripts/web/start_web_services.sh"
log_info "4. 访问前端界面: http://localhost:3000"
log_info ""
log_info "可用的脚本："
log_info "  - Web服务: ./src/scripts/web/start_web_services.sh"
log_info "  - 后端服务: ./src/scripts/web/start_backend.sh"
log_info "  - 前端服务: ./src/scripts/web/start_frontend.sh"
log_info "  - 套保策略: ./src/scripts/strategies/start_hedging_strategy.sh"
log_info "  - 资金费率策略: ./src/scripts/strategies/start_funding_rate_strategy.sh"
