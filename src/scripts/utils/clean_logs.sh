#!/bin/bash

# Crypt Carry 日志清理脚本
# 用于清理过期的日志文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
LOGS_DIR="$PROJECT_ROOT/logs"

log_info "Crypt Carry 日志清理脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "日志目录: $LOGS_DIR"

# 默认保留天数
DEFAULT_DAYS=7

# 解析命令行参数
DAYS=${1:-$DEFAULT_DAYS}
DRY_RUN=false

if [[ "$1" == "--dry-run" ]] || [[ "$2" == "--dry-run" ]]; then
    DRY_RUN=true
    log_info "执行模拟清理（不会实际删除文件）"
fi

if [[ "$1" =~ ^[0-9]+$ ]]; then
    DAYS=$1
elif [[ "$2" =~ ^[0-9]+$ ]]; then
    DAYS=$2
fi

log_info "清理 $DAYS 天前的日志文件"

# 检查日志目录是否存在
if [ ! -d "$LOGS_DIR" ]; then
    log_warning "日志目录不存在: $LOGS_DIR"
    exit 0
fi

# 清理函数
clean_logs_in_dir() {
    local dir=$1
    local category=$2
    
    if [ ! -d "$dir" ]; then
        return
    fi
    
    log_info "清理 $category 日志..."
    
    # 查找过期的日志文件
    local files_to_delete=$(find "$dir" -name "*.log*" -type f -mtime +$DAYS 2>/dev/null || true)
    
    if [ -z "$files_to_delete" ]; then
        log_info "  没有找到需要清理的 $category 日志文件"
        return
    fi
    
    local count=0
    local total_size=0
    
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
            total_size=$((total_size + size))
            count=$((count + 1))
            
            if [ "$DRY_RUN" = true ]; then
                log_info "  [模拟] 将删除: $file ($(numfmt --to=iec $size))"
            else
                log_info "  删除: $file ($(numfmt --to=iec $size))"
                rm -f "$file"
            fi
        fi
    done <<< "$files_to_delete"
    
    if [ $count -gt 0 ]; then
        local size_human=$(numfmt --to=iec $total_size)
        if [ "$DRY_RUN" = true ]; then
            log_success "  [模拟] $category: 将清理 $count 个文件，释放 $size_human 空间"
        else
            log_success "  $category: 已清理 $count 个文件，释放 $size_human 空间"
        fi
    fi
}

# 清理各类日志
clean_logs_in_dir "$LOGS_DIR/hedging" "套保策略"
clean_logs_in_dir "$LOGS_DIR/funding_rate" "资金费率策略"
clean_logs_in_dir "$LOGS_DIR/web" "Web服务"
clean_logs_in_dir "$LOGS_DIR" "通用"

# 清理空目录
log_info "清理空目录..."
if [ "$DRY_RUN" = true ]; then
    find "$LOGS_DIR" -type d -empty 2>/dev/null | while read dir; do
        log_info "  [模拟] 将删除空目录: $dir"
    done
else
    find "$LOGS_DIR" -type d -empty -delete 2>/dev/null || true
    log_success "空目录清理完成"
fi

# 显示当前日志目录状态
log_info "当前日志目录状态:"
if command -v tree >/dev/null 2>&1; then
    tree "$LOGS_DIR" 2>/dev/null || ls -la "$LOGS_DIR"
else
    find "$LOGS_DIR" -type f -name "*.log*" 2>/dev/null | head -20 | while read file; do
        local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        local size_human=$(numfmt --to=iec $size)
        local mtime=$(stat -f%Sm -t%Y-%m-%d "$file" 2>/dev/null || stat -c%y "$file" 2>/dev/null | cut -d' ' -f1)
        echo "  $file ($size_human, $mtime)"
    done
fi

if [ "$DRY_RUN" = true ]; then
    log_info ""
    log_info "这是模拟运行，没有实际删除任何文件"
    log_info "要执行实际清理，请运行: $0 $DAYS"
else
    log_success "日志清理完成!"
fi

log_info ""
log_info "用法: $0 [天数] [--dry-run]"
log_info "示例:"
log_info "  $0 7           # 清理7天前的日志"
log_info "  $0 30          # 清理30天前的日志"
log_info "  $0 7 --dry-run # 模拟清理7天前的日志"
