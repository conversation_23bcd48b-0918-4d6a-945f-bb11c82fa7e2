#!/bin/bash

# 定义Python脚本的路径
SCRIPT_PATH="main.py"
# 定义日志文件的路径
LOG_PATH="output.log"
# 用于存储Python脚本进程ID的文件
PID_PATH="main.pid"

start() {
    if [ -f "$PID_PATH" ] && kill -0 $(cat "$PID_PATH"); then
        echo 'Service already running' >&2
        return 1
    fi
    echo 'Starting service…' >&2
    nohup python "$SCRIPT_PATH" > "$LOG_PATH" 2>&1 &
    echo $! > "$PID_PATH"
    echo 'Service started' >&2
}

stop() {
    if [ ! -f "$PID_PATH" ] || ! kill -0 $(cat "$PID_PATH"); then
        echo 'Service not running' >&2
        return 1
    fi
    echo 'Stopping service…' >&2
    kill -15 $(cat "$PID_PATH") && rm -f "$PID_PATH"
    echo 'Service stopped' >&2
}

restart() {
    echo 'Restarting service…' >&2
    stop
    start
}

case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    restart
    ;;
  *)
    echo "Usage: $0 {start|stop|restart}"
esac
