#!/bin/bash

# Crypt Carry Web Services 统一启动脚本
# 用于同时启动前后端服务（调用分离的脚本）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 脚本路径
BACKEND_SCRIPT="$SCRIPT_DIR/start_backend.sh"
FRONTEND_SCRIPT="$SCRIPT_DIR/start_frontend.sh"

log_info "Crypt Carry Web Services 统一启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "脚本目录: $SCRIPT_DIR"

# 检查脚本是否存在
if [ ! -f "$BACKEND_SCRIPT" ]; then
    log_error "后端启动脚本不存在: $BACKEND_SCRIPT"
    exit 1
fi

if [ ! -f "$FRONTEND_SCRIPT" ]; then
    log_error "前端启动脚本不存在: $FRONTEND_SCRIPT"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$BACKEND_SCRIPT" "$FRONTEND_SCRIPT"

# 停止所有服务的函数
stop_all_services() {
    log_info "停止所有服务..."
    "$BACKEND_SCRIPT" stop 2>/dev/null || true
    "$FRONTEND_SCRIPT" stop 2>/dev/null || true
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        log_info "启动所有服务..."
        log_info "使用分离的脚本启动前后端服务"
        
        # 启动后端服务（后台运行）
        log_info "启动后端服务..."
        "$BACKEND_SCRIPT" start &
        BACKEND_SCRIPT_PID=$!
        
        # 等待后端启动
        sleep 5
        
        # 启动前端服务（后台运行）
        log_info "启动前端服务..."
        "$FRONTEND_SCRIPT" start &
        FRONTEND_SCRIPT_PID=$!
        
        # 等待前端启动
        sleep 5

        log_success "所有服务启动完成!"
        log_info "后端服务: http://localhost:5001"
        log_info "前端服务: http://localhost:3000"
        log_info "API文档: http://localhost:5001/api/docs"
        log_info ""
        log_info "日志目录: $PROJECT_ROOT/logs/"
        log_info "  - Web服务日志: logs/web/"
        log_info "  - 套保策略日志: logs/hedging/"
        log_info "  - 资金费率日志: logs/funding_rate/"
        log_info ""
        log_info "按 Ctrl+C 停止所有服务"

        # 信号处理：停止子进程
        trap 'kill $BACKEND_SCRIPT_PID $FRONTEND_SCRIPT_PID 2>/dev/null || true; stop_all_services' EXIT INT TERM

        # 等待用户中断
        while true; do
            sleep 1
        done
        ;;

    "stop")
        stop_all_services
        log_success "所有服务已停止"
        ;;

    "restart")
        log_info "重启所有服务..."
        stop_all_services
        sleep 2
        "$BACKEND_SCRIPT" start &
        sleep 3
        "$FRONTEND_SCRIPT" start &
        sleep 3
        log_success "服务重启完成!"
        ;;

    "status")
        log_info "检查服务状态..."
        "$BACKEND_SCRIPT" status
        "$FRONTEND_SCRIPT" status
        ;;

    "logs")
        log_info "查看服务日志..."
        echo "=== 后端日志 ==="
        "$BACKEND_SCRIPT" logs &
        BACKEND_LOG_PID=$!
        
        echo ""
        echo "=== 前端日志 ==="
        "$FRONTEND_SCRIPT" logs &
        FRONTEND_LOG_PID=$!
        
        # 等待用户中断
        trap 'kill $BACKEND_LOG_PID $FRONTEND_LOG_PID 2>/dev/null || true' EXIT INT TERM
        wait
        ;;

    "backend")
        log_info "管理后端服务..."
        shift
        "$BACKEND_SCRIPT" "$@"
        ;;

    "frontend")
        log_info "管理前端服务..."
        shift
        "$FRONTEND_SCRIPT" "$@"
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start      启动所有服务 (默认)"
        echo "  stop       停止所有服务"
        echo "  restart    重启所有服务"
        echo "  status     检查服务状态"
        echo "  logs       查看服务日志"
        echo "  backend    管理后端服务 (传递参数给 start_backend.sh)"
        echo "  frontend   管理前端服务 (传递参数给 start_frontend.sh)"
        echo "  help       显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start              # 启动所有服务"
        echo "  $0 status             # 检查所有服务状态"
        echo "  $0 logs               # 查看所有服务日志"
        echo "  $0 backend start      # 只启动后端服务"
        echo "  $0 frontend build     # 构建前端应用"
        echo ""
        echo "分离的脚本:"
        echo "  $BACKEND_SCRIPT    # 后端服务管理"
        echo "  $FRONTEND_SCRIPT   # 前端服务管理"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
