#!/bin/bash

# Crypt Carry Web Frontend 启动脚本
# 专门用于启动前端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2

    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用 ($service)"
        read -p "是否要杀死占用进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
            kill -9 $pid 2>/dev/null || true
            log_success "已杀死进程 $pid"
        else
            log_error "端口冲突，退出启动"
            exit 1
        fi
    fi
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
WEB_DIR="$PROJECT_ROOT/src/crypt_carry/web"
FRONTEND_DIR="$WEB_DIR/frontend"

# 创建日志目录
WEB_LOG_DIR="$PROJECT_ROOT/logs/web"
mkdir -p "$WEB_LOG_DIR"

log_info "Crypt Carry Web Frontend 启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "Web目录: $WEB_DIR"
log_info "前端目录: $FRONTEND_DIR"
log_info "日志目录: $WEB_LOG_DIR"

# 检查必要的命令
log_info "检查依赖..."
check_command "node"
check_command "yarn"

# 检查端口
log_info "检查端口占用..."
check_port 3000 "前端服务"

# 检查前端依赖
log_info "检查前端依赖..."
cd "$FRONTEND_DIR"
if [ ! -f "package.json" ]; then
    log_error "package.json 不存在"
    exit 1
fi

if [ ! -d "node_modules" ]; then
    log_info "安装前端依赖..."
    yarn install
    log_success "前端依赖安装完成"
else
    log_info "前端依赖已安装"
fi

# 启动服务的函数
start_frontend() {
    log_info "启动前端服务..."
    cd "$FRONTEND_DIR"

    # 启动React开发服务器
    yarn start > "$WEB_LOG_DIR/frontend.log" 2>&1 &
    FRONTEND_PID=$!

    # 等待服务启动
    sleep 5

    # 检查服务是否启动成功 (React Scripts默认端口是3000)
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > "$WEB_LOG_DIR/frontend.pid"
        log_info "前端服务: http://localhost:3000"
        log_info "日志文件: $WEB_LOG_DIR/frontend.log"
    else
        log_error "前端服务启动失败"
        kill $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 停止服务的函数
stop_frontend() {
    log_info "正在停止前端服务..."

    # 停止前端服务
    if [ -f "$WEB_LOG_DIR/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$WEB_LOG_DIR/frontend.pid")
        kill $FRONTEND_PID 2>/dev/null || true
        rm -f "$WEB_LOG_DIR/frontend.pid"
        log_success "前端服务已停止"
    fi

    # 杀死可能残留的进程
    pkill -f "node.*react-scripts" 2>/dev/null || true
    pkill -f "yarn.*start" 2>/dev/null || true
}

# 信号处理
trap stop_frontend EXIT INT TERM

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_frontend
        log_info ""
        log_info "按 Ctrl+C 停止前端服务"

        # 等待用户中断
        while true; do
            sleep 1
        done
        ;;

    "stop")
        stop_frontend
        ;;

    "restart")
        stop_frontend
        sleep 2
        start_frontend
        log_success "前端服务重启完成!"
        ;;

    "status")
        log_info "检查前端服务状态..."
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            log_success "前端服务运行中 (http://localhost:3000)"
        else
            log_error "前端服务未运行"
        fi
        ;;

    "logs")
        log_info "查看前端服务日志..."
        if [ -f "$WEB_LOG_DIR/frontend.log" ]; then
            tail -f "$WEB_LOG_DIR/frontend.log"
        else
            log_error "日志文件不存在: $WEB_LOG_DIR/frontend.log"
        fi
        ;;

    "build")
        log_info "构建前端应用..."
        cd "$FRONTEND_DIR"
        yarn build
        log_success "前端构建完成"
        ;;

    "install")
        log_info "安装前端依赖..."
        cd "$FRONTEND_DIR"
        yarn install
        log_success "前端依赖安装完成"
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start    启动前端服务 (默认)"
        echo "  stop     停止前端服务"
        echo "  restart  重启前端服务"
        echo "  status   检查服务状态"
        echo "  logs     查看服务日志"
        echo "  build    构建前端应用"
        echo "  install  安装前端依赖"
        echo "  help     显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start    # 启动服务"
        echo "  $0 status   # 检查状态"
        echo "  $0 logs     # 查看日志"
        echo "  $0 build    # 构建应用"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
