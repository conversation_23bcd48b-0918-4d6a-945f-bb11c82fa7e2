# Crypt Carry 脚本管理

本目录包含了 Crypt Carry 项目的所有管理脚本，按功能和归属进行分类组织。

## 📁 目录结构

```
src/scripts/
├── web/                    # Web服务相关脚本
│   ├── start_backend.sh    # 后端服务启动脚本
│   ├── start_frontend.sh   # 前端服务启动脚本
│   └── start_web_services.sh # Web服务统一启动脚本
├── strategies/             # 策略相关脚本
│   ├── start_hedging_strategy.sh      # 套保策略启动脚本
│   └── start_funding_rate_strategy.sh # 资金费率策略启动脚本
├── utils/                  # 工具脚本
│   ├── install_project.sh  # 项目安装脚本
│   └── clean_logs.sh       # 日志清理脚本
├── start_all_services.sh   # 全服务启动脚本
└── README.md               # 本文档
```

## 🚀 快速开始

### 1. 项目初始化

```bash
# 安装项目依赖和环境
./src/scripts/utils/install_project.sh
```

### 2. 启动所有服务

```bash
# 启动所有服务（Web + 策略）
./src/scripts/start_all_services.sh

# 或者分别启动
./src/scripts/web/start_web_services.sh        # 启动Web服务
./src/scripts/strategies/start_hedging_strategy.sh  # 启动套保策略
```

### 3. 管理服务

```bash
# 检查服务状态
./src/scripts/start_all_services.sh status

# 查看服务日志
./src/scripts/start_all_services.sh logs

# 停止所有服务
./src/scripts/start_all_services.sh stop
```

## 📋 脚本详细说明

### Web服务脚本

#### `web/start_backend.sh` - 后端服务管理
```bash
./src/scripts/web/start_backend.sh [命令]

命令:
  start    启动后端服务
  stop     停止后端服务
  restart  重启后端服务
  status   检查服务状态
  logs     查看服务日志
```

#### `web/start_frontend.sh` - 前端服务管理
```bash
./src/scripts/web/start_frontend.sh [命令]

命令:
  start    启动前端服务
  stop     停止前端服务
  restart  重启前端服务
  status   检查服务状态
  logs     查看服务日志
  build    构建前端应用
  install  安装前端依赖
```

#### `web/start_web_services.sh` - Web服务统一管理
```bash
./src/scripts/web/start_web_services.sh [命令]

命令:
  start      启动所有Web服务
  stop       停止所有Web服务
  restart    重启所有Web服务
  status     检查服务状态
  logs       查看服务日志
  backend    管理后端服务
  frontend   管理前端服务
```

### 策略脚本

#### `strategies/start_hedging_strategy.sh` - 套保策略管理
```bash
./src/scripts/strategies/start_hedging_strategy.sh [命令]

命令:
  start    启动套保策略服务
  stop     停止套保策略服务
  restart  重启套保策略服务
  status   检查服务状态
  logs     查看服务日志
```

#### `strategies/start_funding_rate_strategy.sh` - 资金费率策略管理
```bash
./src/scripts/strategies/start_funding_rate_strategy.sh [命令]

命令:
  start    启动资金费率策略服务
  stop     停止资金费率策略服务
  restart  重启资金费率策略服务
  status   检查服务状态
  logs     查看服务日志
```

### 工具脚本

#### `utils/install_project.sh` - 项目安装
```bash
./src/scripts/utils/install_project.sh

功能:
- 检查系统依赖（conda, node, yarn）
- 创建conda环境
- 安装Python依赖
- 安装前端依赖
- 创建必要的目录
- 设置脚本权限
```

#### `utils/clean_logs.sh` - 日志清理
```bash
./src/scripts/utils/clean_logs.sh [天数] [--dry-run]

示例:
  ./src/scripts/utils/clean_logs.sh 7           # 清理7天前的日志
  ./src/scripts/utils/clean_logs.sh 30          # 清理30天前的日志
  ./src/scripts/utils/clean_logs.sh 7 --dry-run # 模拟清理7天前的日志
```

### 主启动脚本

#### `start_all_services.sh` - 全服务管理
```bash
./src/scripts/start_all_services.sh [命令]

命令:
  start        启动所有服务
  stop         停止所有服务
  restart      重启所有服务
  status       检查所有服务状态
  logs         查看所有服务日志
  web          管理Web服务
  hedging      管理套保策略
  funding      管理资金费率策略
  install      安装项目依赖
  clean-logs   清理日志文件
```

## 📊 日志管理

### 日志目录结构
```
logs/
├── hedging/           # 套保策略日志
│   ├── app.log        # 按大小轮转的日志文件
│   └── app.daily.log  # 按时间轮转的日志文件
├── funding_rate/      # 资金费率策略日志
│   ├── app.log
│   └── app.daily.log
└── web/              # Web服务日志
    ├── backend.log    # 后端服务日志
    └── frontend.log   # 前端服务日志
```

### 日志查看
```bash
# 查看特定服务日志
./src/scripts/web/start_backend.sh logs
./src/scripts/strategies/start_hedging_strategy.sh logs

# 查看所有服务日志
./src/scripts/start_all_services.sh logs

# 直接查看日志文件
tail -f logs/web/backend.log
tail -f logs/hedging/app.log
```

### 日志清理
```bash
# 清理7天前的日志
./src/scripts/utils/clean_logs.sh 7

# 模拟清理（不实际删除）
./src/scripts/utils/clean_logs.sh 7 --dry-run
```

## 🔧 环境要求

### 系统依赖
- **Conda**: Python环境管理
- **Node.js**: 前端运行环境
- **Yarn**: 前端包管理器

### Python环境
- **Python 3.12**: 推荐版本
- **Conda环境**: crypt_carry312

### 端口使用
- **5001**: 后端API服务
- **3000**: 前端开发服务器

## 🔄 兼容性

### 旧脚本重定向
原有的 `src/crypt_carry/web/start_web_services.sh` 已被重构为兼容性脚本，会自动重定向到新位置：
```bash
# 旧路径（仍然可用）
./src/crypt_carry/web/start_web_services.sh

# 新路径（推荐使用）
./src/scripts/web/start_web_services.sh
```

## 🛠️ 故障排除

### 常见问题

1. **脚本没有执行权限**
   ```bash
   chmod +x src/scripts/**/*.sh
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :5001
   lsof -i :3000
   
   # 脚本会自动提示处理
   ```

3. **Conda环境未激活**
   ```bash
   conda activate crypt_carry312
   ```

4. **依赖未安装**
   ```bash
   ./src/scripts/utils/install_project.sh
   ```

### 调试模式
大部分脚本支持详细的日志输出，可以通过查看日志文件来诊断问题：
```bash
# 查看启动日志
./src/scripts/web/start_backend.sh logs

# 查看错误信息
tail -f logs/web/backend.log
```

## 📝 开发指南

### 添加新脚本
1. 根据功能选择合适的目录（web/strategies/utils）
2. 使用统一的脚本模板和日志函数
3. 添加执行权限：`chmod +x script.sh`
4. 更新本文档

### 脚本规范
- 使用 `#!/bin/bash` 开头
- 设置 `set -e` 错误时退出
- 使用统一的颜色和日志函数
- 提供 help 命令
- 支持常用的 start/stop/restart/status/logs 命令

## 🔗 相关文档

- [日志配置指南](../../docs/logging_guide.md)
- [项目安装指南](../../README.md)
- [Web服务文档](../crypt_carry/web/README.md)
