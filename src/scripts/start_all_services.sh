#!/bin/bash

# Crypt Carry 全服务启动脚本
# 用于启动所有相关服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 脚本路径
WEB_SCRIPT="$SCRIPT_DIR/web/start_web_services.sh"
HEDGING_WORKER_SCRIPT="$SCRIPT_DIR/hedging/start_hedging_worker.sh"
FUNDING_RATE_SCRIPT="$SCRIPT_DIR/strategies/start_funding_rate_strategy.sh"

log_info "Crypt Carry 全服务启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "脚本目录: $SCRIPT_DIR"

# 检查 Redis 服务
check_redis() {
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 启动 Redis 服务
start_redis() {
    if check_redis; then
        log_info "Redis 已在运行"
        return 0
    fi

    log_info "启动 Redis 服务..."
    if command -v brew >/dev/null 2>&1; then
        brew services start redis
    elif command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start redis
    else
        redis-server --daemonize yes
    fi

    sleep 3
    if check_redis; then
        log_success "Redis 启动成功"
    else
        log_error "Redis 启动失败"
        exit 1
    fi
}

# 检查脚本是否存在并设置执行权限
for script in "$WEB_SCRIPT" "$HEDGING_WORKER_SCRIPT" "$FUNDING_RATE_SCRIPT"; do
    if [ ! -f "$script" ]; then
        log_warning "脚本不存在: $script"
        # 不退出，继续检查其他脚本
    else
        chmod +x "$script"
    fi
done

# 停止所有服务的函数
stop_all_services() {
    log_info "停止所有服务..."
    [ -f "$WEB_SCRIPT" ] && "$WEB_SCRIPT" stop 2>/dev/null || true
    [ -f "$HEDGING_WORKER_SCRIPT" ] && "$HEDGING_WORKER_SCRIPT" stop 2>/dev/null || true
    [ -f "$FUNDING_RATE_SCRIPT" ] && "$FUNDING_RATE_SCRIPT" stop 2>/dev/null || true
    log_success "所有服务已停止"
}

# 检查所有服务状态的函数
check_all_status() {
    log_info "检查所有服务状态..."
    echo ""
    echo "=== Web服务状态 ==="
    "$WEB_SCRIPT" status
    echo ""
    echo "=== 套保策略状态 ==="
    "$HEDGING_SCRIPT" status
    echo ""
    echo "=== 资金费率策略状态 ==="
    "$FUNDING_RATE_SCRIPT" status
}

# 查看所有服务日志的函数
view_all_logs() {
    log_info "查看所有服务日志..."
    echo ""
    echo "=== Web服务日志 ==="
    "$WEB_SCRIPT" logs &
    WEB_LOG_PID=$!
    
    echo ""
    echo "=== 套保策略日志 ==="
    "$HEDGING_SCRIPT" logs &
    HEDGING_LOG_PID=$!
    
    echo ""
    echo "=== 资金费率策略日志 ==="
    "$FUNDING_RATE_SCRIPT" logs &
    FUNDING_LOG_PID=$!
    
    # 等待用户中断
    trap 'kill $WEB_LOG_PID $HEDGING_LOG_PID $FUNDING_LOG_PID 2>/dev/null || true' EXIT INT TERM
    wait
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        log_info "启动所有服务..."
        
        # 启动Web服务
        log_info "启动Web服务..."
        "$WEB_SCRIPT" start &
        WEB_PID=$!
        
        # 等待Web服务启动
        sleep 8
        
        # 启动策略服务
        log_info "启动套保策略..."
        "$HEDGING_SCRIPT" start &
        HEDGING_PID=$!
        
        log_info "启动资金费率策略..."
        "$FUNDING_RATE_SCRIPT" start &
        FUNDING_RATE_PID=$!
        
        # 等待策略服务启动
        sleep 3

        log_success "所有服务启动完成!"
        log_info ""
        log_info "服务访问地址:"
        log_info "  - 前端界面: http://localhost:3000"
        log_info "  - 后端API: http://localhost:5001"
        log_info "  - API文档: http://localhost:5001/api/docs"
        log_info ""
        log_info "日志目录: $PROJECT_ROOT/logs/"
        log_info "  - Web服务: logs/web/"
        log_info "  - 套保策略: logs/hedging/"
        log_info "  - 资金费率: logs/funding_rate/"
        log_info ""
        log_info "按 Ctrl+C 停止所有服务"

        # 信号处理：停止所有子进程
        trap 'kill $WEB_PID $HEDGING_PID $FUNDING_RATE_PID 2>/dev/null || true; stop_all_services' EXIT INT TERM

        # 等待用户中断
        while true; do
            sleep 1
        done
        ;;

    "stop")
        stop_all_services
        ;;

    "restart")
        log_info "重启所有服务..."
        stop_all_services
        sleep 3
        "$0" start
        ;;

    "status")
        check_all_status
        ;;

    "logs")
        view_all_logs
        ;;

    "web")
        log_info "管理Web服务..."
        shift
        "$WEB_SCRIPT" "$@"
        ;;

    "hedging")
        log_info "管理套保策略..."
        shift
        "$HEDGING_SCRIPT" "$@"
        ;;

    "funding")
        log_info "管理资金费率策略..."
        shift
        "$FUNDING_RATE_SCRIPT" "$@"
        ;;

    "install")
        log_info "安装项目依赖..."
        "$SCRIPT_DIR/utils/install_project.sh"
        ;;

    "clean-logs")
        log_info "清理日志文件..."
        shift
        "$SCRIPT_DIR/utils/clean_logs.sh" "$@"
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start        启动所有服务 (默认)"
        echo "  stop         停止所有服务"
        echo "  restart      重启所有服务"
        echo "  status       检查所有服务状态"
        echo "  logs         查看所有服务日志"
        echo "  web          管理Web服务 (传递参数给Web脚本)"
        echo "  hedging      管理套保策略 (传递参数给套保脚本)"
        echo "  funding      管理资金费率策略 (传递参数给资金费率脚本)"
        echo "  install      安装项目依赖"
        echo "  clean-logs   清理日志文件"
        echo "  help         显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start              # 启动所有服务"
        echo "  $0 status             # 检查所有服务状态"
        echo "  $0 web backend start  # 只启动Web后端"
        echo "  $0 hedging logs       # 查看套保策略日志"
        echo "  $0 clean-logs 7       # 清理7天前的日志"
        echo ""
        echo "独立脚本:"
        echo "  $WEB_SCRIPT"
        echo "  $HEDGING_SCRIPT"
        echo "  $FUNDING_RATE_SCRIPT"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
