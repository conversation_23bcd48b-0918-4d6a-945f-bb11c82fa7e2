#!/bin/bash

# Crypt Carry 资金费率策略启动脚本
# 专门用于启动资金费率策略服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 创建日志目录
FUNDING_RATE_LOG_DIR="$PROJECT_ROOT/logs/funding_rate"
mkdir -p "$FUNDING_RATE_LOG_DIR"

log_info "Crypt Carry 资金费率策略启动脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "日志目录: $FUNDING_RATE_LOG_DIR"

# 检查conda环境
log_info "检查conda环境..."
if [ -z "$CONDA_DEFAULT_ENV" ]; then
    log_error "未激活conda环境"
    log_info "请先激活conda环境："
    log_info "  conda activate your-env-name"
    exit 1
else
    log_success "检测到conda环境: $CONDA_DEFAULT_ENV"
fi

# 启动资金费率策略的函数
start_funding_rate_strategy() {
    log_info "启动资金费率策略服务..."
    cd "$PROJECT_ROOT"

    # 设置环境变量
    export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"
    export FUNDING_RATE_LOG_DIR="$FUNDING_RATE_LOG_DIR"

    # 启动资金费率策略（这里需要根据实际的启动方式调整）
    # python -m src.crypt_carry.strategies.funding_rate.main > "$FUNDING_RATE_LOG_DIR/strategy.log" 2>&1 &
    log_info "资金费率策略启动命令需要根据实际情况配置"
    log_info "请在此脚本中添加具体的启动命令"
    
    # STRATEGY_PID=$!
    # echo $STRATEGY_PID > "$FUNDING_RATE_LOG_DIR/strategy.pid"
    # log_success "资金费率策略启动成功 (PID: $STRATEGY_PID)"
}

# 停止资金费率策略的函数
stop_funding_rate_strategy() {
    log_info "正在停止资金费率策略服务..."

    if [ -f "$FUNDING_RATE_LOG_DIR/strategy.pid" ]; then
        STRATEGY_PID=$(cat "$FUNDING_RATE_LOG_DIR/strategy.pid")
        kill $STRATEGY_PID 2>/dev/null || true
        rm -f "$FUNDING_RATE_LOG_DIR/strategy.pid"
        log_success "资金费率策略服务已停止"
    else
        log_warning "未找到资金费率策略进程ID文件"
    fi

    # 杀死可能残留的进程
    pkill -f "funding_rate.*strategy" 2>/dev/null || true
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_funding_rate_strategy
        log_info "资金费率策略服务启动完成"
        ;;

    "stop")
        stop_funding_rate_strategy
        ;;

    "restart")
        stop_funding_rate_strategy
        sleep 2
        start_funding_rate_strategy
        log_success "资金费率策略服务重启完成!"
        ;;

    "status")
        log_info "检查资金费率策略服务状态..."
        if [ -f "$FUNDING_RATE_LOG_DIR/strategy.pid" ]; then
            STRATEGY_PID=$(cat "$FUNDING_RATE_LOG_DIR/strategy.pid")
            if ps -p $STRATEGY_PID > /dev/null 2>&1; then
                log_success "资金费率策略服务运行中 (PID: $STRATEGY_PID)"
            else
                log_error "资金费率策略服务未运行"
            fi
        else
            log_error "资金费率策略服务未运行"
        fi
        ;;

    "logs")
        log_info "查看资金费率策略服务日志..."
        if [ -f "$FUNDING_RATE_LOG_DIR/strategy.log" ]; then
            tail -f "$FUNDING_RATE_LOG_DIR/strategy.log"
        else
            log_error "日志文件不存在: $FUNDING_RATE_LOG_DIR/strategy.log"
        fi
        ;;

    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start    启动资金费率策略服务 (默认)"
        echo "  stop     停止资金费率策略服务"
        echo "  restart  重启资金费率策略服务"
        echo "  status   检查服务状态"
        echo "  logs     查看服务日志"
        echo "  help     显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start    # 启动服务"
        echo "  $0 status   # 检查状态"
        echo "  $0 logs     # 查看日志"
        ;;

    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
