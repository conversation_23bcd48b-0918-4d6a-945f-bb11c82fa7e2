"""
跨交易所套保策略模块

重构后的模块结构：
- core/: 核心策略逻辑
- orders/: 订单处理（缓存、拆单、执行）
- risk/: 风险管理
- positions/: 持仓管理
- coordination/: 交易协调
- examples/: 示例和配置
"""

# 核心策略模块
from .core import (
    HedgingStrategy,
    HedgingCondition,
    CombinedHedgingCondition,
    TriggerType,
    HedgingStrategyManager
)

# 订单处理模块
from .orders import (
    OrderCacheManager,
    HedgingOrder,
    SplitTask,
    OrderStatus,
    ExecutionType,
    get_order_cache_manager,
    OrderSplitter,
    SplitConfig,
    get_order_splitter,
    TaskExecutor,
    get_task_executor,
    OrderProcessor,
    ProcessingStage,
    get_order_processor
)

# 风险管理模块
from .risk import (
    RiskManager,
    RiskLimit
)

# 持仓管理模块
from .positions import (
    HedgingPositionManager,
    HedgingPair
)

# 交易协调模块
from .coordination import (
    CrossExchangeCoordinator
)

__all__ = [
    # 核心策略
    'HedgingStrategy',
    'HedgingCondition',
    'CombinedHedgingCondition',
    'TriggerType',
    'HedgingStrategyManager',

    # 订单处理
    'OrderCacheManager',
    'HedgingOrder',
    'SplitTask',
    'OrderStatus',
    'ExecutionType',
    'get_order_cache_manager',
    'OrderSplitter',
    'SplitConfig',
    'get_order_splitter',
    'TaskExecutor',
    'get_task_executor',
    'OrderProcessor',
    'ProcessingStage',
    'get_order_processor',

    # 风险管理
    'RiskManager',
    'RiskLimit',

    # 持仓管理
    'HedgingPositionManager',
    'HedgingPair',

    # 交易协调
    'CrossExchangeCoordinator'
]
