"""
风险管理器 - 监控跨交易所持仓风险，处理异常情况，实施止损和风控措施
"""
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, TYPE_CHECKING
from dataclasses import dataclass
from enum import Enum

from crypt_carry.utils.ding_talk_manager import DingTalkManager
from crypt_carry.utils.logger_config import get_hedging_strategy_logger

if TYPE_CHECKING:
    from crypt_carry.strategies.hedging.core.hedging_strategy import HedgingCondition

logger = get_hedging_strategy_logger(__name__)


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """风险指标"""
    total_exposure: float  # 总敞口(USDT)
    max_single_position: float  # 单笔最大持仓(USDT)
    concentration_risk: float  # 集中度风险(0-1)
    correlation_risk: float  # 相关性风险(0-1)
    liquidity_risk: float  # 流动性风险(0-1)
    overall_risk_score: float  # 综合风险评分(0-100)
    risk_level: RiskLevel  # 风险等级
    timestamp: datetime


@dataclass
class RiskLimit:
    """风险限制"""
    max_total_exposure: float = 100000  # 最大总敞口(USDT)
    max_single_position: float = 10000  # 单笔最大持仓(USDT)
    max_daily_trades: int = 50  # 每日最大交易次数
    max_concentration: float = 0.3  # 最大集中度(30%)
    max_correlation: float = 0.8  # 最大相关性(80%)
    stop_loss_threshold: float = 0.05  # 止损阈值(5%)
    max_drawdown: float = 0.1  # 最大回撤(10%)


class RiskManager:
    """风险管理器"""

    def __init__(self):
        """初始化风险管理器"""
        self.risk_limits = RiskLimit()
        self.current_metrics: Optional[RiskMetrics] = None
        self.daily_trade_count = 0
        self.daily_reset_time = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

        # 历史风险数据
        self.risk_history: List[RiskMetrics] = []
        self.max_history_size = 1000

        # 钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 风险监控状态
        self.is_monitoring = False
        self._stop_event = asyncio.Event()

    async def initialize(self):
        """初始化风险管理器"""
        try:
            logger.info("初始化风险管理器...")

            # 重置每日计数器
            await self._reset_daily_counters()

            logger.info("风险管理器初始化完成")

        except Exception as e:
            logger.error(f"初始化风险管理器失败: {str(e)}")
            raise

    async def start_monitoring(self):
        """启动风险监控"""
        try:
            self.is_monitoring = True
            logger.info("启动风险监控")

            while self.is_monitoring:
                try:
                    # 更新风险指标
                    await self._update_risk_metrics()

                    # 检查风险限制
                    await self._check_risk_limits()

                    # 检查是否需要重置每日计数器
                    await self._check_daily_reset()

                    # 等待下次检查
                    await asyncio.sleep(30)  # 每30秒检查一次

                except Exception as e:
                    logger.error(f"风险监控异常: {str(e)}")
                    await asyncio.sleep(10)

        except Exception as e:
            logger.error(f"风险监控启动失败: {str(e)}")
        finally:
            self.is_monitoring = False

    async def stop_monitoring(self):
        """停止风险监控"""
        logger.info("停止风险监控")
        self.is_monitoring = False
        self._stop_event.set()

    async def check_risk_limits(self, condition: 'HedgingCondition') -> bool:
        """检查风险限制

        Args:
            condition: 套保条件

        Returns:
            bool: 是否通过风险检查
        """
        try:
            # 检查每日交易次数限制
            if self.daily_trade_count >= self.risk_limits.max_daily_trades:
                logger.warning(f"已达到每日最大交易次数限制: {self.daily_trade_count}/{self.risk_limits.max_daily_trades}")
                return False

            # 转换交易金额为USDT等价值
            amount_usdt = condition.amount
            if condition.amount_currency != 'USDT':
                if condition.amount_currency == 'USDC':
                    amount_usdt = condition.amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {condition.amount_currency}")
                    return False

            # 检查单笔交易金额限制
            if amount_usdt > self.risk_limits.max_single_position:
                logger.warning(f"单笔交易金额超出限制: {amount_usdt} > {self.risk_limits.max_single_position}")
                return False

            # 检查总敞口限制
            current_exposure = await self._calculate_total_exposure()
            if current_exposure + amount_usdt > self.risk_limits.max_total_exposure:
                logger.warning(f"总敞口将超出限制: {current_exposure + amount_usdt} > {self.risk_limits.max_total_exposure}")
                return False

            # 检查集中度风险
            concentration_risk = await self._calculate_concentration_risk(condition)
            if concentration_risk > self.risk_limits.max_concentration:
                logger.warning(f"集中度风险过高: {concentration_risk:.2%} > {self.risk_limits.max_concentration:.2%}")
                return False

            # 检查相关性风险
            correlation_risk = await self._calculate_correlation_risk(condition)
            if correlation_risk > self.risk_limits.max_correlation:
                logger.warning(f"相关性风险过高: {correlation_risk:.2%} > {self.risk_limits.max_correlation:.2%}")
                return False

            return True

        except Exception as e:
            logger.error(f"检查风险限制失败: {str(e)}")
            return False

    async def check_combined_risk_limits(self, combined_condition) -> bool:
        """检查组合条件的风险限制

        Args:
            combined_condition: 组合套保条件

        Returns:
            bool: 是否通过风险检查
        """
        try:
            # 检查每日交易次数限制
            if self.daily_trade_count >= self.risk_limits.max_daily_trades:
                logger.warning(f"已达到每日最大交易次数限制: {self.daily_trade_count}/{self.risk_limits.max_daily_trades}")
                return False

            # 转换交易金额为USDT等价值
            amount_usdt = combined_condition.amount
            if combined_condition.amount_currency != 'USDT':
                if combined_condition.amount_currency == 'USDC':
                    amount_usdt = combined_condition.amount  # 1:1假设
                else:
                    logger.warning(f"暂不支持的交易币种: {combined_condition.amount_currency}")
                    return False

            # 检查单笔交易金额限制
            if amount_usdt > self.risk_limits.max_single_position:
                logger.warning(f"组合条件交易金额超出限制: {amount_usdt} > {self.risk_limits.max_single_position}")
                return False

            # 检查总敞口限制
            current_exposure = await self._calculate_total_exposure()
            if current_exposure + amount_usdt > self.risk_limits.max_total_exposure:
                logger.warning(f"组合条件总敞口将超出限制: {current_exposure + amount_usdt} > {self.risk_limits.max_total_exposure}")
                return False

            # 检查集中度风险（组合条件可能涉及多个子条件，风险更高）
            concentration_risk = await self._calculate_combined_concentration_risk(combined_condition)
            if concentration_risk > self.risk_limits.max_concentration:
                logger.warning(f"组合条件集中度风险过高: {concentration_risk:.2%} > {self.risk_limits.max_concentration:.2%}")
                return False

            # 检查相关性风险
            correlation_risk = await self._calculate_combined_correlation_risk(combined_condition)
            if correlation_risk > self.risk_limits.max_correlation:
                logger.warning(f"组合条件相关性风险过高: {correlation_risk:.2%} > {self.risk_limits.max_correlation:.2%}")
                return False

            return True

        except Exception as e:
            logger.error(f"检查组合条件风险限制失败: {str(e)}")
            return False

    async def _update_risk_metrics(self):
        """更新风险指标"""
        try:
            # 计算各项风险指标
            total_exposure = await self._calculate_total_exposure()
            max_single_position = await self._calculate_max_single_position()
            concentration_risk = await self._calculate_concentration_risk()
            correlation_risk = await self._calculate_correlation_risk()
            liquidity_risk = await self._calculate_liquidity_risk()

            # 计算综合风险评分
            overall_risk_score = await self._calculate_overall_risk_score(
                total_exposure, concentration_risk, correlation_risk, liquidity_risk
            )

            # 确定风险等级
            risk_level = self._determine_risk_level(overall_risk_score)

            # 创建风险指标对象
            self.current_metrics = RiskMetrics(
                total_exposure=total_exposure,
                max_single_position=max_single_position,
                concentration_risk=concentration_risk,
                correlation_risk=correlation_risk,
                liquidity_risk=liquidity_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                timestamp=datetime.now(timezone.utc)
            )

            # 添加到历史记录
            self.risk_history.append(self.current_metrics)

            # 保持历史记录大小
            if len(self.risk_history) > self.max_history_size:
                self.risk_history = self.risk_history[-self.max_history_size:]

        except Exception as e:
            logger.error(f"更新风险指标失败: {str(e)}")

    async def _check_risk_limits(self):
        """检查风险限制"""
        try:
            if not self.current_metrics:
                return

            # 检查总敞口
            if self.current_metrics.total_exposure > self.risk_limits.max_total_exposure:
                await self._handle_risk_breach("总敞口超限",
                    f"当前: {self.current_metrics.total_exposure:.2f}, 限制: {self.risk_limits.max_total_exposure:.2f}")

            # 检查集中度风险
            if self.current_metrics.concentration_risk > self.risk_limits.max_concentration:
                await self._handle_risk_breach("集中度风险过高",
                    f"当前: {self.current_metrics.concentration_risk:.2%}, 限制: {self.risk_limits.max_concentration:.2%}")

            # 检查相关性风险
            if self.current_metrics.correlation_risk > self.risk_limits.max_correlation:
                await self._handle_risk_breach("相关性风险过高",
                    f"当前: {self.current_metrics.correlation_risk:.2%}, 限制: {self.risk_limits.max_correlation:.2%}")

            # 检查风险等级
            if self.current_metrics.risk_level == RiskLevel.CRITICAL:
                await self._handle_critical_risk()

        except Exception as e:
            logger.error(f"检查风险限制失败: {str(e)}")

    async def _calculate_total_exposure(self) -> float:
        """计算总敞口"""
        try:
            # TODO: 从持仓管理器获取所有活跃持仓并计算总敞口
            # 这里先返回模拟值
            return 0.0

        except Exception as e:
            logger.error(f"计算总敞口失败: {str(e)}")
            return 0.0

    async def _calculate_max_single_position(self) -> float:
        """计算单笔最大持仓"""
        try:
            # TODO: 从持仓管理器获取最大单笔持仓
            # 这里先返回模拟值
            return 0.0

        except Exception as e:
            logger.error(f"计算单笔最大持仓失败: {str(e)}")
            return 0.0

    async def _calculate_concentration_risk(self, condition: Optional['HedgingCondition'] = None) -> float:
        """计算集中度风险"""
        try:
            # TODO: 计算持仓在不同交易所、交易对的集中度
            # 这里先返回模拟值
            return 0.1

        except Exception as e:
            logger.error(f"计算集中度风险失败: {str(e)}")
            return 0.0

    async def _calculate_correlation_risk(self, condition: Optional['HedgingCondition'] = None) -> float:
        """计算相关性风险"""
        try:
            # TODO: 计算不同持仓之间的相关性风险
            # 这里先返回模拟值
            return 0.2

        except Exception as e:
            logger.error(f"计算相关性风险失败: {str(e)}")
            return 0.0

    async def _calculate_liquidity_risk(self) -> float:
        """计算流动性风险"""
        try:
            # TODO: 计算市场流动性风险
            # 这里先返回模拟值
            return 0.15

        except Exception as e:
            logger.error(f"计算流动性风险失败: {str(e)}")
            return 0.0

    async def _calculate_overall_risk_score(self, total_exposure: float, concentration_risk: float,
                                          correlation_risk: float, liquidity_risk: float) -> float:
        """计算综合风险评分"""
        try:
            # 权重配置
            exposure_weight = 0.3
            concentration_weight = 0.25
            correlation_weight = 0.25
            liquidity_weight = 0.2

            # 标准化各项指标到0-100分
            exposure_score = min(100, (total_exposure / self.risk_limits.max_total_exposure) * 100)
            concentration_score = concentration_risk * 100
            correlation_score = correlation_risk * 100
            liquidity_score = liquidity_risk * 100

            # 计算加权平均
            overall_score = (
                exposure_score * exposure_weight +
                concentration_score * concentration_weight +
                correlation_score * correlation_weight +
                liquidity_score * liquidity_weight
            )

            return min(100, max(0, overall_score))

        except Exception as e:
            logger.error(f"计算综合风险评分失败: {str(e)}")
            return 0.0

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score >= 80:
            return RiskLevel.CRITICAL
        elif risk_score >= 60:
            return RiskLevel.HIGH
        elif risk_score >= 40:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    async def _handle_risk_breach(self, risk_type: str, details: str):
        """处理风险违规"""
        try:
            logger.warning(f"风险违规: {risk_type} - {details}")

            # 发送警告消息
            message = (
                f"风险管理警告\n"
                f"类型: {risk_type}\n"
                f"详情: {details}\n"
                f"时间: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )
            self.ding_talk_manager.send_message(message, prefix="风险警告")

        except Exception as e:
            logger.error(f"处理风险违规失败: {str(e)}")

    async def _handle_critical_risk(self):
        """处理严重风险"""
        try:
            logger.critical("检测到严重风险，启动紧急处理程序")

            # 发送紧急通知
            message = (
                f"严重风险警报\n"
                f"风险评分: {self.current_metrics.overall_risk_score:.2f}\n"
                f"风险等级: {self.current_metrics.risk_level.value}\n"
                f"建议立即检查持仓并考虑减仓\n"
                f"时间: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )
            self.ding_talk_manager.send_message(message, prefix="严重风险")

            # TODO: 实现自动风险处理逻辑
            # 例如：自动减仓、暂停新交易等

        except Exception as e:
            logger.error(f"处理严重风险失败: {str(e)}")

    async def _check_daily_reset(self):
        """检查是否需要重置每日计数器"""
        try:
            now = datetime.now(timezone.utc)
            if now.date() > self.daily_reset_time.date():
                await self._reset_daily_counters()

        except Exception as e:
            logger.error(f"检查每日重置失败: {str(e)}")

    async def _reset_daily_counters(self):
        """重置每日计数器"""
        try:
            self.daily_trade_count = 0
            self.daily_reset_time = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            logger.info("每日计数器已重置")

        except Exception as e:
            logger.error(f"重置每日计数器失败: {str(e)}")

    def increment_daily_trade_count(self):
        """增加每日交易计数"""
        self.daily_trade_count += 1
        logger.debug(f"每日交易计数: {self.daily_trade_count}/{self.risk_limits.max_daily_trades}")

    def get_current_metrics(self) -> Optional[RiskMetrics]:
        """获取当前风险指标"""
        return self.current_metrics

    def get_risk_history(self, hours: int = 24) -> List[RiskMetrics]:
        """获取风险历史数据

        Args:
            hours: 获取最近多少小时的数据

        Returns:
            List[RiskMetrics]: 风险历史数据
        """
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            return [metrics for metrics in self.risk_history if metrics.timestamp >= cutoff_time]

        except Exception as e:
            logger.error(f"获取风险历史数据失败: {str(e)}")
            return []

    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        try:
            if not self.current_metrics:
                return {
                    'status': 'no_data',
                    'message': '暂无风险数据'
                }

            return {
                'status': 'ok',
                'current_risk_level': self.current_metrics.risk_level.value,
                'overall_risk_score': self.current_metrics.overall_risk_score,
                'total_exposure': self.current_metrics.total_exposure,
                'max_single_position': self.current_metrics.max_single_position,
                'concentration_risk': self.current_metrics.concentration_risk,
                'correlation_risk': self.current_metrics.correlation_risk,
                'liquidity_risk': self.current_metrics.liquidity_risk,
                'daily_trade_count': self.daily_trade_count,
                'daily_trade_limit': self.risk_limits.max_daily_trades,
                'last_update': self.current_metrics.timestamp.isoformat(),
                'limits': {
                    'max_total_exposure': self.risk_limits.max_total_exposure,
                    'max_single_position': self.risk_limits.max_single_position,
                    'max_concentration': self.risk_limits.max_concentration,
                    'max_correlation': self.risk_limits.max_correlation
                }
            }

        except Exception as e:
            logger.error(f"获取风险摘要失败: {str(e)}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def update_risk_limits(self, **kwargs):
        """更新风险限制

        Args:
            **kwargs: 风险限制参数
        """
        try:
            for key, value in kwargs.items():
                if hasattr(self.risk_limits, key):
                    setattr(self.risk_limits, key, value)
                    logger.info(f"更新风险限制: {key} = {value}")
                else:
                    logger.warning(f"未知的风险限制参数: {key}")

        except Exception as e:
            logger.error(f"更新风险限制失败: {str(e)}")

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止监控
            await self.stop_monitoring()

            # 清空历史数据
            self.risk_history.clear()
            self.current_metrics = None

            logger.info("风险管理器清理完成")

        except Exception as e:
            logger.error(f"清理风险管理器失败: {str(e)}")

    async def _calculate_combined_concentration_risk(self, combined_condition) -> float:
        """计算组合条件的集中度风险

        Args:
            combined_condition: 组合套保条件

        Returns:
            float: 集中度风险值(0-1)
        """
        try:
            # 组合条件涉及多个触发条件，可能增加集中度风险
            base_risk = await self._calculate_concentration_risk()

            # 根据子条件数量调整风险
            condition_count = len(combined_condition.conditions)
            risk_multiplier = 1.0 + (condition_count - 1) * 0.1  # 每增加一个条件，风险增加10%

            adjusted_risk = min(1.0, base_risk * risk_multiplier)

            logger.debug(f"组合条件集中度风险: 基础={base_risk:.3f}, 调整后={adjusted_risk:.3f}")
            return adjusted_risk

        except Exception as e:
            logger.error(f"计算组合条件集中度风险失败: {str(e)}")
            return 0.2  # 返回保守的风险值

    async def _calculate_combined_correlation_risk(self, combined_condition) -> float:
        """计算组合条件的相关性风险

        Args:
            combined_condition: 组合套保条件

        Returns:
            float: 相关性风险值(0-1)
        """
        try:
            # 组合条件中的多个子条件可能存在相关性
            base_risk = await self._calculate_correlation_risk()

            # 分析子条件类型的相关性
            condition_types = [c['type'] for c in combined_condition.conditions]
            unique_types = set(condition_types)

            # 如果包含多种不同类型的条件，相关性风险可能降低
            # 如果都是相同类型的条件，相关性风险增加
            if len(unique_types) == len(condition_types):
                # 所有条件类型都不同，风险降低
                risk_multiplier = 0.8
            elif len(unique_types) == 1:
                # 所有条件类型相同，风险增加
                risk_multiplier = 1.3
            else:
                # 部分条件类型相同，风险适中增加
                risk_multiplier = 1.1

            adjusted_risk = min(1.0, base_risk * risk_multiplier)

            logger.debug(f"组合条件相关性风险: 基础={base_risk:.3f}, 调整后={adjusted_risk:.3f}")
            return adjusted_risk

        except Exception as e:
            logger.error(f"计算组合条件相关性风险失败: {str(e)}")
            return 0.3  # 返回保守的风险值
