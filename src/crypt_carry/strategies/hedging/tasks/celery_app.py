"""
Celery 应用配置

基于 Redis 的消息队列配置
"""
import os
from celery import Celery
from kombu import Queue

# Redis 配置
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
REDIS_DB = int(os.getenv('REDIS_DB', 0))
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', 'crypt_carry_redis_2024')  # 默认 Docker 密码

# 构建 Redis URL
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# 创建 Celery 应用
celery_app = Celery('crypt_carry_hedging')

# Celery 配置
celery_app.conf.update(
    # 消息代理配置
    broker_url=REDIS_URL,
    result_backend=REDIS_URL,
    
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # 任务路由配置
    task_routes={
        'crypt_carry.strategies.hedging.tasks.hedging_tasks.create_hedging_order_task': {
            'queue': 'hedging_orders'
        },
        'crypt_carry.strategies.hedging.tasks.hedging_tasks.create_single_order_task': {
            'queue': 'single_orders'
        },
        'crypt_carry.strategies.hedging.tasks.hedging_tasks.start_condition_monitoring_task': {
            'queue': 'monitoring'
        },
        'crypt_carry.strategies.hedging.tasks.hedging_tasks.stop_condition_monitoring_task': {
            'queue': 'monitoring'
        },
        'crypt_carry.strategies.hedging.tasks.hedging_tasks.get_hedging_status_task': {
            'queue': 'status'
        },
    },
    
    # 队列配置
    task_default_queue='default',
    task_queues=(
        Queue('hedging_orders', routing_key='hedging_orders'),
        Queue('single_orders', routing_key='single_orders'),
        Queue('monitoring', routing_key='monitoring'),
        Queue('status', routing_key='status'),
        Queue('default', routing_key='default'),
    ),
    
    # 任务执行配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,
    
    # 结果过期时间（秒）
    result_expires=3600,
    
    # 任务超时配置
    task_soft_time_limit=300,  # 5分钟软超时
    task_time_limit=600,       # 10分钟硬超时
    
    # 重试配置
    task_default_retry_delay=60,    # 重试延迟60秒
    task_max_retries=3,             # 最大重试3次
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 自动发现任务
celery_app.autodiscover_tasks([
    'crypt_carry.strategies.hedging.tasks'
])

# 健康检查任务
@celery_app.task(bind=True)
def health_check(self):
    """健康检查任务"""
    return {
        'status': 'healthy',
        'worker_id': self.request.id,
        'timestamp': self.request.utc,
    }
