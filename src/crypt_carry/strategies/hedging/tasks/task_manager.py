"""
套保任务管理器

提供统一的任务提交和状态查询接口，供 Web API 调用
"""
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from .celery_app import celery_app
from .hedging_tasks import (
    create_hedging_order_task,
    create_single_order_task,
    start_condition_monitoring_task,
    stop_condition_monitoring_task,
    get_hedging_status_task
)

# 添加日志配置
try:
    from crypt_carry.utils.logger_config import get_web_service_logger
    logger = get_web_service_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class HedgingTaskManager:
    """套保任务管理器"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.celery_app = celery_app
    
    def submit_hedging_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交套保订单任务
        
        Args:
            order_data: 订单数据
            
        Returns:
            Dict: 任务提交结果
        """
        try:
            logger.info(f"提交套保订单任务: {order_data.get('order_id')}")
            
            # 提交异步任务
            task = create_hedging_order_task.delay(order_data)
            
            return {
                'success': True,
                'message': '套保订单任务已提交',
                'task_id': task.id,
                'order_id': order_data.get('order_id'),
                'submitted_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"提交套保订单任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'提交套保订单任务失败: {str(e)}'
            }
    
    def submit_single_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交单独订单任务
        
        Args:
            order_data: 订单数据
            
        Returns:
            Dict: 任务提交结果
        """
        try:
            logger.info(f"提交单独订单任务: {order_data.get('order_id')}")
            
            # 提交异步任务
            task = create_single_order_task.delay(order_data)
            
            return {
                'success': True,
                'message': '单独订单任务已提交',
                'task_id': task.id,
                'order_id': order_data.get('order_id'),
                'submitted_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"提交单独订单任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'提交单独订单任务失败: {str(e)}'
            }
    
    def submit_condition_monitoring(self, condition_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交条件监控任务
        
        Args:
            condition_data: 条件数据
            
        Returns:
            Dict: 任务提交结果
        """
        try:
            logger.info(f"提交条件监控任务: {condition_data.get('condition_id')}")
            
            # 提交异步任务
            task = start_condition_monitoring_task.delay(condition_data)
            
            return {
                'success': True,
                'message': '条件监控任务已提交',
                'task_id': task.id,
                'condition_id': condition_data.get('condition_id'),
                'submitted_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"提交条件监控任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'提交条件监控任务失败: {str(e)}'
            }
    
    def stop_condition_monitoring(self, condition_id: str) -> Dict[str, Any]:
        """
        停止条件监控任务
        
        Args:
            condition_id: 条件ID
            
        Returns:
            Dict: 任务提交结果
        """
        try:
            logger.info(f"提交停止条件监控任务: {condition_id}")
            
            # 提交异步任务
            task = stop_condition_monitoring_task.delay(condition_id)
            
            return {
                'success': True,
                'message': '停止条件监控任务已提交',
                'task_id': task.id,
                'condition_id': condition_id,
                'submitted_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"提交停止条件监控任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'提交停止条件监控任务失败: {str(e)}'
            }
    
    def get_hedging_status(self) -> Dict[str, Any]:
        """
        获取套保策略状态
        
        Returns:
            Dict: 状态信息
        """
        try:
            logger.info("获取套保策略状态")
            
            # 提交异步任务并等待结果
            task = get_hedging_status_task.delay()
            
            # 等待任务完成（设置超时时间）
            result = task.get(timeout=30)
            
            return result
            
        except Exception as e:
            logger.error(f"获取套保策略状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取套保策略状态失败: {str(e)}'
            }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态
        """
        try:
            # 获取任务结果
            task_result = self.celery_app.AsyncResult(task_id)
            
            status_info = {
                'task_id': task_id,
                'status': task_result.status,
                'ready': task_result.ready(),
                'successful': task_result.successful() if task_result.ready() else None,
                'failed': task_result.failed() if task_result.ready() else None,
            }
            
            # 如果任务完成，获取结果
            if task_result.ready():
                if task_result.successful():
                    status_info['result'] = task_result.result
                elif task_result.failed():
                    status_info['error'] = str(task_result.info)
            
            return {
                'success': True,
                'task_status': status_info
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {task_id}, 错误: {str(e)}")
            return {
                'success': False,
                'error': f'获取任务状态失败: {str(e)}'
            }
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 取消结果
        """
        try:
            logger.info(f"取消任务: {task_id}")
            
            # 撤销任务
            self.celery_app.control.revoke(task_id, terminate=True)
            
            return {
                'success': True,
                'message': f'任务 {task_id} 已取消',
                'task_id': task_id,
                'cancelled_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return {
                'success': False,
                'error': f'取消任务失败: {str(e)}'
            }
    
    def get_worker_status(self) -> Dict[str, Any]:
        """
        获取 Worker 状态
        
        Returns:
            Dict: Worker 状态信息
        """
        try:
            # 获取活跃的 Worker
            inspect = self.celery_app.control.inspect()
            
            active_workers = inspect.active()
            registered_tasks = inspect.registered()
            stats = inspect.stats()
            
            return {
                'success': True,
                'worker_status': {
                    'active_workers': list(active_workers.keys()) if active_workers else [],
                    'worker_count': len(active_workers) if active_workers else 0,
                    'registered_tasks': registered_tasks,
                    'stats': stats
                }
            }
            
        except Exception as e:
            logger.error(f"获取 Worker 状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取 Worker 状态失败: {str(e)}'
            }


# 全局任务管理器实例
_task_manager = None

def get_task_manager() -> HedgingTaskManager:
    """获取任务管理器单例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = HedgingTaskManager()
    return _task_manager
