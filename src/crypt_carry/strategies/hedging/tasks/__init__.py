"""
套保策略任务队列模块

基于 Redis + Celery 的异步任务处理系统
用于解决 Web API 服务和套保策略服务之间的通信问题
"""

from .celery_app import celery_app
from .hedging_tasks import (
    create_hedging_order_task,
    create_single_order_task,
    start_condition_monitoring_task,
    stop_condition_monitoring_task,
    get_hedging_status_task
)
from .task_manager import HedgingTaskManager

__all__ = [
    'celery_app',
    'create_hedging_order_task',
    'create_single_order_task', 
    'start_condition_monitoring_task',
    'stop_condition_monitoring_task',
    'get_hedging_status_task',
    'HedgingTaskManager'
]
