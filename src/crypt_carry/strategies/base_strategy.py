"""
基础策略类
"""
import asyncio
import logging
from abc import ABC, abstractmethod

from crypt_carry.core.exchange.account.account_manager import AccountManager
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)

class BaseStrategy(ABC):
    """基础策略类"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化

        Args:
            exchange_client: 交易所客户端
        """
        # 交易所客户端
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name

        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)
        self.leverage = 2  # 默认杠杆
        self.max_positions = 5  # 默认最大持仓数量
        # 最大重试次数
        self.max_retries = self.config.get_order_value("MAX_RETRIES", 3)
        # 重试间隔（秒）
        self.retry_interval = self.config.get_order_value("RETRY_INTERVAL_MS", 1000) / 1000

        self.strategy_config = {}
        self.scoring_config = {}

        # 初始化管理器
        self.account_manager = AccountManager.get_instance()
        self.position_manager: PositionManager = PositionManager.get_instance()

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

        # 设置账户管理器的交易所客户端
        self.account_manager.set_exchange_client(exchange_client)

        self.is_running = False
        self._stop_event = asyncio.Event()

        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 加载交易所特有配置
        self.load_exchange_config()

    @abstractmethod
    async def run(self):
        """运行策略

        此方法需要在子类中实现，用于定义策略的具体运行逻辑。
        """
        pass



    def _init_configuration(self):
        """初始化配置"""

        self.market_data_age_sec = self.config.trading_config.get('MARKET_DATA_AGE_SEC', 10)
        # 从config中读取策略配置
        self.strategy_config = self.config.get_strategy_config()

        # 读取打分配置
        self.scoring_config = self.strategy_config.get('SCORING_CONFIG', {})

        # 读取杠杆配置
        self.leverage = self.strategy_config.get('LEVERAGE', 2)

        # 读取最大持仓数量
        self.max_positions = self.strategy_config.get('MAX_POSITIONS', 5)

    async def reload_config(self):
        """重新加载配置"""
        try:
            logger.debug("重新加载配置...")
            # 强制重新加载配置
            self.config.reload_config(force=True)

            # 重新初始化配置变量
            self._init_configuration()
            await self.exchange_client.reload_detect_exclude_pairs()
            logger.debug("配置加载完成")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}", exc_info=True)

    def load_exchange_config(self):
        """加载交易所特有配置"""
        try:
            # 获取策略配置
            self._init_configuration()

            logger.debug(f"加载 {self.exchange_name} 特有配置成功")

        except Exception as e:
            logger.error(f"加载 {self.exchange_name} 特有配置失败: {e}")

    async def stop(self):
        """停止策略"""
        self.is_running = False
        self._stop_event.set()
