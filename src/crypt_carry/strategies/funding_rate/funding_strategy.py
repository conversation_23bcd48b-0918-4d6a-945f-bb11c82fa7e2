"""
交易所内部的资金费率套利策略 - 核心策略框架
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import List

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.funding.funding_rate_cache import FundingRateCache
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.monitor.position_monitor import PositionMonitor
from crypt_carry.core.trading.opportunity.opportunity_detector import OpportunityDetector
from crypt_carry.core.trading.position.position_adjuster import PositionAdjuster
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.strategies.base_strategy import BaseStrategy
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager
from crypt_carry.core.trading.task.task_processor import TaskProcessor
from crypt_carry.core.trading.trade.trade_executor import TradeExecutor
from util.log_throttler import LogThrottler

logger = logging.getLogger(__name__)


class ExchangeFundingStrategy(BaseStrategy):
    """单个交易所内部的资金费率套利策略 - 核心框架"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化资金费率套利策略

        Args:
            exchange_client: 交易所客户端
        """
        super().__init__(exchange_client)
        self.last_rebalance_time = 0

        # 创建组件
        self.opportunity_detector = OpportunityDetector(self.exchange_client)
        self.position_monitor = PositionMonitor(self.exchange_client)
        # 使用单例模式获取交易执行器实例
        self.trade_executor: TradeExecutor = TradeExecutor.get_instance(self.exchange_client)

        # 初始化资金费率管理器
        self.funding_rate_cache = FundingRateCache()
        self.position_adjuster = PositionAdjuster()

        # 初始化待处理任务管理器（单例模式）
        self.pending_tasks_manager = PendingTasksManager.get_instance()

        # 任务处理器
        self.task_processor = None
        self.is_running = False

        # 初始化日志频率控制器
        self.log_throttler = LogThrottler.get_instance(f"{self.exchange_name}_exchange_funding")
        self._5m_log_key = f"{self.exchange_name}_pending_tasks_manager"
        self.log_throttler.set_interval(self._5m_log_key, 5 * 60)
        self._3m_log_key = f"{self.exchange_name}_clear_open_tasks"
        self.log_throttler.set_interval(self._3m_log_key, 3 * 60)

    async def run(self):
        """运行策略"""
        try:
            self.is_running = True
            logger.info(f"{self.exchange_name} 策略启动")
            self.ding_talk_manager.send_message(f"{self.exchange_name} 策略启动", prefix=self.exchange_name)

            # 创建并启动任务处理器
            task_processor = TaskProcessor(
                exchange_name=self.exchange_name,
                trade_executor=self.trade_executor,
                pending_tasks_manager=self.pending_tasks_manager,
                position_manager=PositionManager.get_instance(),
                market_data_manager=MarketDataManager.get_instance(),
                funding_rate_cache=self.funding_rate_cache,
                ding_talk_manager=self.ding_talk_manager,
                strategy_config=self.strategy_config,
                reset_force_rebalance_callback=self._reset_force_rebalance_flag
            )
            await task_processor.start()
            self.task_processor = task_processor

            # 启动持仓监控任务
            await self.position_monitor.start_monitor()

            while self.is_running:
                try:
                    await self.reload_config()
                    # 检查是否到达换仓时间
                    is_rebalance_time, is_force_rebalance = await self._check_rebalance_time()
                    if is_rebalance_time:
                        # 检查行情数据是否已经准备就绪
                        market_data_ready = self.market_data_manager.is_market_data_ready(
                            exchange=self.exchange_name,
                            symbols=self.exchange_client.base_pair,
                            max_age=self.market_data_age_sec
                        )

                        if not market_data_ready:
                            logger.warning(f"{self.exchange_name} 行情数据未准备就绪，将等待3秒后重试")
                            await asyncio.sleep(3)  # 3秒后重试
                            self._last_rebalance_hour = -1
                            continue

                        if not self.position_monitor.last_monitor_time:
                            logger.warning(f"{self.exchange_name} 持仓监控未启动，将等待3秒后重试")
                            await asyncio.sleep(3)  # 3秒后重试
                            self._last_rebalance_hour = -1
                            continue

                        # 获取交易机会
                        opportunities = await self.opportunity_detector.detect_opportunities()
                        if not opportunities:
                            logger.info(f"{self.exchange_name} 未发现交易机会")
                            continue

                        # logger.info(f"{self.exchange_name} ,检查行情数据延迟情况")
                        # continue

                        # 清理过期任务
                        await self.pending_tasks_manager.clear_tasks(self.exchange_name)

                        # 设置持仓监控冷却期，避免在仓位轮动过程中触发不必要的监控和平仓
                        logger.info(f"{self.exchange_name} 开始轮动仓位，设置持仓监控冷却期")
                        self.position_monitor.set_cooldown()

                        # 调整持仓
                        positions_to_close, positions_to_open = self.position_adjuster.calculate_adjustments(
                            exchange=self.exchange_name,
                            opportunities=opportunities,
                            max_positions=self.max_positions
                        )

                        # 暂时停止持仓监控
                        # self.position_monitor.stop_monitor()

                        # 将待平仓和待建仓的任务加入待处理列表
                        await self.pending_tasks_manager.add_close_tasks(self.exchange_name, positions_to_close)
                        await self.pending_tasks_manager.add_open_tasks(self.exchange_name, positions_to_open,
                                                                  is_force_rebalance)
                        await self.pending_tasks_manager.update_funding_time(self.exchange_name)

                        task_counts = await self.pending_tasks_manager.get_task_counts(self.exchange_name)
                        msg = (
                            f"{self.exchange_name} ExchangeFundingStrategy 待处理任务更新 - "
                            f"待平仓: {task_counts['close_count']}, "
                            f"待建仓: {task_counts['open_count']}"
                        )
                        logger.info(msg)
                        self.ding_talk_manager.send_message(msg, prefix=self.exchange_name)

                    if not await self._is_within_rebalance_window():
                        # 清理过期任务
                        self.log_throttler.log_if_allowed(logger_obj=logger, key=self._3m_log_key,
                                                          message=f"{self.exchange_name} 超出轮动时间窗口，清理过期任务")
                        await self.pending_tasks_manager.clear_open_tasks(self.exchange_name)

                    # 等待下一次检查
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"{self.exchange_name} 策略运行出错: {e}")
                    await asyncio.sleep(10)

        except Exception as e:
            logger.error(f"{self.exchange_name}策略运行失败: {e}")
            self.is_running = False
        finally:
            # 取消任务处理器
            if self.task_processor:
                await self.task_processor.stop()

            # 停止持仓监控
            await self.position_monitor.stop_monitor()

    async def stop(self):
        """停止策略"""
        self.is_running = False  # 先设置停止标志
        await super().stop()

        # 停止持仓监控
        await self.position_monitor.stop_monitor()

        # 停止任务处理器
        if self.task_processor:
            try:
                await self.task_processor.stop()
                logger.info(f"{self.exchange_name} 任务处理器已停止")
            except Exception as e:
                logger.error(f"{self.exchange_name} 停止任务处理器失败: {e}")

    async def _get_rebalance_time_info(self):
        """
        获取与换仓时间相关的基础信息
        返回：换仓时间配置、当前时间信息等
        """
        # 获取当前UTC时间
        now = datetime.now(timezone.utc)
        current_hour = now.hour
        current_minute = now.minute

        # 获取提前换仓时间（分钟）
        ahead_minutes = self.strategy_config.get('REBALANCE_AHEAD_MINUTES', 120)
        ahead_hours = ahead_minutes // 60
        remaining_minutes = ahead_minutes % 60

        # 获取资金费率收取时间（UTC时间）
        funding_hours = await self._get_funding_hours()

        # 计算各换仓窗口信息
        window_info = []
        for hour in funding_hours:
            # 计算窗口起始时间（资金费率收取时间前X小时Y分钟）
            window_start_hour = (hour - ahead_hours - 1 if remaining_minutes > 0 else hour - ahead_hours) % 24
            window_start_minute = 60 - remaining_minutes if remaining_minutes > 0 else 0

            # 计算窗口结束时间（资金费率收取时间）
            window_end_hour = hour % 24
            window_end_minute = 0

            window_info.append({
                'funding_hour': hour,
                'start_hour': window_start_hour,
                'start_minute': window_start_minute,
                'end_hour': window_end_hour,
                'end_minute': window_end_minute
            })

        return {
            'now': now,
            'current_hour': current_hour,
            'current_minute': current_minute,
            'ahead_minutes': ahead_minutes,
            'ahead_hours': ahead_hours,
            'remaining_minutes': remaining_minutes,
            'funding_hours': funding_hours,
            'window_info': window_info
        }

    async def _check_rebalance_time_window(self):
        """
        检查是否在换仓时间窗口内
        :return: 是否在换仓时间窗口内
        """
        try:
            # 记录上次换仓时间（使用UTC小时）
            if not hasattr(self, '_last_rebalance_hour'):
                self._last_rebalance_hour = -1

            # 获取换仓时间信息
            time_info = await self._get_rebalance_time_info()
            current_hour = time_info['current_hour']
            current_minute = time_info['current_minute']
            window_info = time_info['window_info']

            # 检查是否需要换仓
            for window in window_info:
                target_hour = window['start_hour']
                start_minute = window['start_minute']
                funding_hour = window['funding_hour']

                # 如果当前小时等于目标换仓小时，且这个小时还没有换过仓
                if current_hour == target_hour:
                    # 如果有剩余分钟，检查分钟数是否达到要求
                    if start_minute > 0:
                        if current_minute >= start_minute and self._last_rebalance_hour != current_hour:
                            logger.info(
                                f"{self.exchange_name} 检测到换仓时间: UTC {current_hour:02d}:{current_minute:02d} "
                                f"[资金费率时间: {funding_hour:02d}:00, 提前{time_info['ahead_minutes']}分钟]"
                            )
                            self._last_rebalance_hour = current_hour
                            return True
                    else:
                        if self._last_rebalance_hour != current_hour:
                            logger.info(
                                f"{self.exchange_name} 检测到换仓时间: UTC {current_hour:02d}:{current_minute:02d} "
                                f"[资金费率时间: {funding_hour:02d}:00, 提前{time_info['ahead_minutes']}分钟]"
                            )
                            self._last_rebalance_hour = current_hour
                            return True

            return False
        except Exception as e:
            logger.error(f"检查换仓时间窗口失败: {str(e)}")
            return False

    async def _is_within_rebalance_window(self):
        """
        检查当前时间是否在换仓窗口内
        :return: 是否在换仓窗口内
        """
        try:
            # 获取换仓时间信息
            time_info = await self._get_rebalance_time_info()
            current_hour = time_info['current_hour']
            current_minute = time_info['current_minute']
            window_info = time_info['window_info']

            # 检查是否在任一换仓窗口内
            for window in window_info:
                window_start_hour = window['start_hour']
                window_start_minute = window['start_minute']
                window_end_hour = window['end_hour']
                window_end_minute = window['end_minute']

                # 检查当前时间是否在窗口内
                is_within_window = False

                # 如果起始和结束在同一小时
                if window_start_hour == window_end_hour:
                    if current_hour == window_start_hour and window_start_minute <= current_minute <= window_end_minute:
                        is_within_window = True
                # 如果在起始小时
                elif current_hour == window_start_hour and current_minute >= window_start_minute:
                    is_within_window = True
                # 如果在结束小时
                elif current_hour == window_end_hour and current_minute <= window_end_minute:
                    is_within_window = True
                # 如果在中间小时
                elif (window_start_hour < window_end_hour and window_start_hour < current_hour < window_end_hour) or \
                        (window_start_hour > window_end_hour and (
                                current_hour > window_start_hour or current_hour < window_end_hour)):
                    is_within_window = True

                if is_within_window:
                    return True

            return False
        except Exception as e:
            logger.error(f"检查换仓窗口时间失败: {str(e)}")
            return False

    async def _check_force_rebalance(self):
        """
        检查是否强制换仓
        :return: (是否强制换仓, 是否真的强制换仓)
        """
        try:
            # 检查是否设置了强制换仓标志
            force_position_check = self.strategy_config.get('FORCE_REBALANCE', False)
            if force_position_check:
                logger.debug(f"{self.exchange_name} 检测到强制换仓标志")
                await self._reset_force_rebalance_flag()
                return True, True
            return False, False
        except Exception as e:
            logger.error(f"检查强制换仓失败: {str(e)}")
            return False, False

    async def _check_rebalance_time(self):
        """
        检查是否到了换仓时间
        :return: (是否换仓, 是否强制换仓)
        """
        try:
            # 检查是否强制换仓
            is_force_rebalance, force_rebalance = await self._check_force_rebalance()
            if is_force_rebalance:
                return True, force_rebalance

            # 检查是否在换仓时间窗口内
            is_time_window = await self._check_rebalance_time_window()
            if is_time_window:
                return True, False

            return False, False
        except Exception as e:
            logger.error(f"检查换仓时间失败: {str(e)}")
            return False, False

    async def _get_funding_hours(self) -> List[int]:
        """获取换仓时间点列表

        Returns:
            List[int]: 换仓时间点列表
        """
        try:
            funding_hours = self.config.trading_config.get('FUNDING_RATE_TIMES', [0, 8, 16])
            return funding_hours
        except Exception as e:
            logger.error(f"获取资金费率更新时间点失败: {e}")
            return [0, 8, 16]

    async def _reset_force_rebalance_flag(self) -> None:
        """重置强制换仓标志"""
        try:
            # 通过ExchangeConfig设置策略值
            self.config.set_strategy_value("FORCE_REBALANCE", False)
            logger.info(f"{self.exchange_name} 强制换仓标志已重置")
        except Exception as e:
            logger.error(f"{self.exchange_name} 重置强制换仓标志失败: {e}")
