"""
资金费率套利策略管理器 - 管理多个交易所的资金费率套利策略
"""
import asyncio
import logging
from typing import Dict, Set

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.factory.exchange_factory import ExchangeFactory
from crypt_carry.strategies.funding_rate.funding_strategy import ExchangeFundingStrategy

logger = logging.getLogger(__name__)


class FundingRateStrategyManager:
    """资金费率套利策略管理器，管理多个交易所的资金费率套利策略"""

    def __init__(self):
        """初始化策略管理器"""
        self.config = ExchangeConfig()

        # 初始化交易所客户端和策略
        self.exchange_clients: Dict[str, ExchangeClient] = {}
        self.strategies: Dict[str, ExchangeFundingStrategy] = {}

        # 初始化交易所支持的币对集合
        self.exchange_symbols: Dict[str, Set[str]] = {}

    async def initialize(self):
        """初始化所有交易所客户端和策略"""
        try:
            # 获取配置中的交易所列表
            exchanges = self.config.get_enabled_exchanges()
            logger.info(f"开始初始化交易所: {exchanges}")

            # 创建交易所工厂
            factory = ExchangeFactory()

            # 为每个交易所创建客户端和策略
            for exchange_name in exchanges:
                try:
                    # 创建交易所客户端
                    exchange_client = await factory.create_exchange_client(exchange_name)
                    if exchange_client:
                        self.exchange_clients[exchange_name] = exchange_client
                        
                        # 创建资金费率套利策略
                        strategy = ExchangeFundingStrategy(exchange_client)
                        self.strategies[exchange_name] = strategy
                        
                        # 获取交易所支持的币对
                        symbols = await exchange_client.get_supported_symbols()
                        self.exchange_symbols[exchange_name] = set(symbols)
                        
                        logger.info(f"成功初始化 {exchange_name} 交易所，支持 {len(symbols)} 个交易对")
                    else:
                        logger.error(f"创建 {exchange_name} 交易所客户端失败")
                        
                except Exception as e:
                    logger.error(f"初始化 {exchange_name} 交易所失败: {str(e)}")
                    continue

            logger.info(f"策略管理器初始化完成，成功初始化 {len(self.strategies)} 个交易所策略")

        except Exception as e:
            logger.error(f"策略管理器初始化失败: {str(e)}")
            raise

    async def start_all_strategies(self):
        """启动所有策略"""
        try:
            logger.info("开始启动所有资金费率套利策略...")
            
            # 创建所有策略的启动任务
            tasks = []
            for exchange_name, strategy in self.strategies.items():
                task = asyncio.create_task(
                    self._start_strategy_with_retry(exchange_name, strategy)
                )
                tasks.append(task)
            
            # 等待所有策略启动完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
            logger.info("所有资金费率套利策略启动完成")
            
        except Exception as e:
            logger.error(f"启动策略失败: {str(e)}")
            raise

    async def _start_strategy_with_retry(self, exchange_name: str, strategy: ExchangeFundingStrategy):
        """带重试机制的策略启动"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                logger.info(f"启动 {exchange_name} 资金费率套利策略...")
                await strategy.run()
                break
                
            except Exception as e:
                retry_count += 1
                logger.error(f"{exchange_name} 策略启动失败 (第{retry_count}次): {str(e)}")
                
                if retry_count < max_retries:
                    wait_time = retry_count * 5  # 递增等待时间
                    logger.info(f"{exchange_name} 将在 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"{exchange_name} 策略启动失败，已达到最大重试次数")

    async def stop_all_strategies(self):
        """停止所有策略"""
        try:
            logger.info("开始停止所有资金费率套利策略...")
            
            # 创建所有策略的停止任务
            tasks = []
            for exchange_name, strategy in self.strategies.items():
                task = asyncio.create_task(self._stop_strategy(exchange_name, strategy))
                tasks.append(task)
            
            # 等待所有策略停止完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
            logger.info("所有资金费率套利策略已停止")
            
        except Exception as e:
            logger.error(f"停止策略失败: {str(e)}")

    async def _stop_strategy(self, exchange_name: str, strategy: ExchangeFundingStrategy):
        """停止单个策略"""
        try:
            logger.info(f"停止 {exchange_name} 资金费率套利策略...")
            await strategy.stop()
            logger.info(f"{exchange_name} 资金费率套利策略已停止")
            
        except Exception as e:
            logger.error(f"停止 {exchange_name} 策略失败: {str(e)}")

    async def restart_strategy(self, exchange_name: str):
        """重启指定交易所的策略"""
        try:
            if exchange_name not in self.strategies:
                logger.error(f"未找到 {exchange_name} 的策略")
                return False
                
            strategy = self.strategies[exchange_name]
            
            # 停止策略
            await self._stop_strategy(exchange_name, strategy)
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 重新启动策略
            await self._start_strategy_with_retry(exchange_name, strategy)
            
            logger.info(f"{exchange_name} 策略重启完成")
            return True
            
        except Exception as e:
            logger.error(f"重启 {exchange_name} 策略失败: {str(e)}")
            return False

    def get_strategy_status(self) -> Dict[str, Dict]:
        """获取所有策略的状态"""
        status = {}
        
        for exchange_name, strategy in self.strategies.items():
            status[exchange_name] = {
                'is_running': strategy.is_running,
                'exchange_name': exchange_name,
                'supported_symbols_count': len(self.exchange_symbols.get(exchange_name, [])),
                'strategy_type': 'funding_rate_arbitrage'
            }
            
        return status

    def get_supported_symbols(self, exchange_name: str) -> Set[str]:
        """获取指定交易所支持的交易对"""
        return self.exchange_symbols.get(exchange_name, set())

    def get_common_symbols(self) -> Set[str]:
        """获取所有交易所都支持的交易对"""
        if not self.exchange_symbols:
            return set()
            
        # 计算所有交易所的交集
        common_symbols = set.intersection(*self.exchange_symbols.values())
        return common_symbols

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止所有策略
            await self.stop_all_strategies()
            
            # 关闭所有交易所客户端
            for exchange_name, client in self.exchange_clients.items():
                try:
                    await client.close()
                    logger.info(f"已关闭 {exchange_name} 交易所客户端")
                except Exception as e:
                    logger.error(f"关闭 {exchange_name} 交易所客户端失败: {str(e)}")
            
            # 清空所有引用
            self.strategies.clear()
            self.exchange_clients.clear()
            self.exchange_symbols.clear()
            
            logger.info("资金费率策略管理器清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")
