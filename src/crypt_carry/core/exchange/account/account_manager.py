"""
账户管理器
"""
import logging
from typing import Dict

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.account.balance_manager import BalanceManager
from crypt_carry.core.exchange.account.order_manager import OrderManager

logger = logging.getLogger(__name__)


class AccountManager:
    """账户管理器"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AccountManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化账户管理器"""
        if not hasattr(self, 'initialized'):
            self.balance_manager = BalanceManager.get_instance()
            self.order_manager = OrderManager.get_instance()
            self.initialized = True

    def set_exchange_client(self, exchange_client: ExchangeClient):
        """设置交易所客户端
        
        Args:
            exchange_client: 交易所客户端
        """
        self.balance_manager.set_exchange_client(exchange_client)
        self.order_manager.set_exchange_client(exchange_client)

    async def get_spot_balance(self, exchange: str) -> Dict[str, float]:
        """获取现货账户余额
        
        Args:
            exchange: 交易所名称
            
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        return await self.balance_manager.get_spot_balance(exchange)

    async def get_futures_balance(self, exchange: str) -> Dict[str, float]:
        """获取合约账户余额
        
        Args:
            exchange: 交易所名称
            
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        return await self.balance_manager.get_futures_balance(exchange)

    async def get_account_balance(self, exchange: str) -> float:
        """获取账户总余额（USDT）
        
        Args:
            exchange: 交易所名称
            
        Returns:
            float: 账户总余额（USDT）
        """
        try:
            # 获取现货和合约账户余额
            spot_balance = await self.get_spot_balance(exchange)
            futures_balance = await self.get_futures_balance(exchange)
            
            # 计算总余额
            total = 0.0
            
            # 统计现货余额
            for currency, amount in spot_balance.items():
                if currency in ['USDT', 'USDC']:
                    total += amount
                    
            # 统计合约余额
            for currency, amount in futures_balance.items():
                if currency in ['USDT', 'USDC']:
                    total += amount
                    
            logger.info(f"{exchange} 账户总余额: {total} USDT")
            return total
            
        except Exception as e:
            error_msg = f"{exchange} 获取账户总余额失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def create_order(self, exchange: str, symbol: str, order_type: str,
                         side: str, amount: float, price: float = None,
                         is_spot: bool = True) -> Dict:
        """创建订单
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            order_type: 订单类型 (market/limit)
            side: 交易方向 (buy/sell)
            amount: 数量
            price: 价格（市价单可选）
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 订单信息
        """
        return await self.order_manager.create_order(
            exchange=exchange,
            symbol=symbol,
            order_type=order_type,
            side=side,
            amount=amount,
            price=price,
            is_spot=is_spot
        )

    @classmethod
    def get_instance(cls) -> 'AccountManager':
        """获取单例实例"""
        return cls()
