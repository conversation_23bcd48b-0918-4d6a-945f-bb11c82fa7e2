"""
订单管理器
"""
import logging
from typing import Dict, Optional, List

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient

logger = logging.getLogger(__name__)


class OrderManager:
    """订单管理器"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OrderManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化订单管理器"""
        if not hasattr(self, 'exchange_clients'):
            self.exchange_clients = {}  # {exchange_name: exchange_client}

    def set_exchange_client(self, exchange_client: ExchangeClient):
        """设置交易所客户端
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_clients[exchange_client.exchange_name] = exchange_client

    async def create_order(
        self,
        exchange: str,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
        is_spot: bool = True
    ) -> Dict:
        """创建订单
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            order_type: 订单类型 (market/limit)
            side: 交易方向 (buy/sell)
            amount: 数量
            price: 价格（市价单可选）
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 订单信息
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            order = await client.create_order(
                symbol=symbol,
                order_type=order_type,
                side=side,
                amount=amount,
                price=price,
                is_spot=is_spot
            )

            logger.info(
                f"{exchange} 创建订单成功 - "
                f"symbol: {symbol}, type: {order_type}, side: {side}, "
                f"amount: {amount}, price: {price}, is_spot: {is_spot}"
            )
            return order

        except Exception as e:
            error_msg = (
                f"{exchange} 创建订单失败 - "
                f"symbol: {symbol}, type: {order_type}, side: {side}, "
                f"amount: {amount}, price: {price}, is_spot: {is_spot}, "
                f"error: {str(e)}"
            )
            logger.error(error_msg)
            raise

    async def fetch_order(self, exchange: str, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """查询订单信息
        
        Args:
            exchange: 交易所名称
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 订单信息
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            order = await client.fetch_order(order_id, symbol, is_spot)
            logger.info(f"{exchange} 查询订单成功: {order}")
            return order

        except Exception as e:
            error_msg = f"{exchange} 查询订单失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def cancel_order(self, exchange: str, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """取消订单
        
        Args:
            exchange: 交易所名称
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 取消结果
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            result = await client.cancel_order(order_id, symbol, is_spot)
            logger.info(f"{exchange} 取消订单成功: {result}")
            return result

        except Exception as e:
            error_msg = f"{exchange} 取消订单失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def fetch_open_orders(self, exchange: str, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询当前未完成的订单
        
        Args:
            exchange: 交易所名称
            symbol: 交易对（可选，如果不指定则查询所有交易对）
            is_spot: 是否是现货订单
            
        Returns:
            List[Dict]: 订单列表
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            orders = await client.fetch_open_orders(symbol, is_spot)
            logger.info(f"{exchange} 查询未完成订单成功: {len(orders)}个")
            return orders

        except Exception as e:
            error_msg = f"{exchange} 查询未完成订单失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def fetch_closed_orders(self, exchange: str, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询历史订单
        
        Args:
            exchange: 交易所名称
            symbol: 交易对（可选，如果不指定则查询所有交易对）
            is_spot: 是否是现货订单
            
        Returns:
            List[Dict]: 订单列表
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            orders = await client.fetch_closed_orders(symbol, is_spot)
            logger.info(f"{exchange} 查询历史订单成功: {len(orders)}个")
            return orders

        except Exception as e:
            error_msg = f"{exchange} 查询历史订单失败: {str(e)}"
            logger.error(error_msg)
            raise

    @classmethod
    def get_instance(cls) -> 'OrderManager':
        """获取单例实例"""
        return cls()
