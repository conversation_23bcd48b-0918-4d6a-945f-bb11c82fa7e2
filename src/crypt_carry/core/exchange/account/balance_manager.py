"""
余额管理器
"""
import logging
from typing import Dict

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient

logger = logging.getLogger(__name__)


class BalanceManager:
    """余额管理器"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BalanceManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化余额管理器"""
        if not hasattr(self, 'exchange_clients'):
            self.exchange_clients = {}  # {exchange_name: exchange_client}

    def set_exchange_client(self, exchange_client: ExchangeClient):
        """设置交易所客户端
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_clients[exchange_client.exchange_name] = exchange_client

    async def get_spot_balance(self, exchange: str) -> Dict[str, float]:
        """获取现货账户余额
        
        Args:
            exchange: 交易所名称
            
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            balance = await client.fetch_spot_balance()
            logger.info(f"{exchange} 现货账户余额: {balance}")
            return balance

        except Exception as e:
            error_msg = f"{exchange} 获取现货账户余额失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def get_futures_balance(self, exchange: str) -> Dict[str, float]:
        """获取合约账户余额
        
        Args:
            exchange: 交易所名称
            
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        try:
            client = self.exchange_clients.get(exchange)
            if not client:
                error_msg = f"未找到交易所客户端: {exchange}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            balance = await client.fetch_futures_balance()
            logger.info(f"{exchange} 合约账户余额: {balance}")
            return balance

        except Exception as e:
            error_msg = f"{exchange} 获取合约账户余额失败: {str(e)}"
            logger.error(error_msg)
            raise

    @classmethod
    def get_instance(cls) -> 'BalanceManager':
        """获取单例实例"""
        return cls()
