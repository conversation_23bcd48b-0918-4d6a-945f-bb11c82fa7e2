from abc import ABC, abstractmethod
from typing import Dict

class AccountInterface(ABC):
    """账户接口定义"""
    
    @abstractmethod
    async def fetch_spot_balance(self) -> Dict[str, float]:
        """
        获取现货账户USDT和USDC余额
        Returns:
            Dict[str, float]: {'USDT': float, 'USDC': float}
        """
        pass

    @abstractmethod
    async def fetch_futures_balance(self) -> Dict[str, float]:
        """
        获取合约账户USDT和USDC余额
        Returns:
            Dict[str, float]: {'USDT': float, 'USDC': float}
        """
        pass

    @abstractmethod
    async def create_spot_order(self, symbol: str, side: str, quantity: float, price: float = None):
        """
        创建现货订单
        
        Args:
            symbol: 交易对
            side: 方向 ('buy' 或 'sell')
            quantity: 数量
            price: 价格（None表示市价单）
        """
        pass

    @abstractmethod
    async def create_futures_order(self, symbol: str, side: str, quantity: float, price: float = None):
        """
        创建合约订单
        
        Args:
            symbol: 交易对
            side: 方向 ('buy' 或 'sell')
            quantity: 数量
            price: 价格（None表示市价单）
        """
        pass
