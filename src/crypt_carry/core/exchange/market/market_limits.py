"""
交易所市场限制管理器
用于管理和验证交易所的各种限制，如最小下单量、步长等
"""
import logging
from decimal import Decimal, ROUND_DOWN, ROUND_UP
from typing import Dict, Optional, List

import ccxt

logger = logging.getLogger(__name__)


class MarketLimit:
    """单个市场的限制信息"""

    def __init__(self, market_info: Dict):
        """初始化
        
        Args:
            market_info: CCXT market() 返回的市场信息
        """
        self.market_info = market_info
        self._parse_limits()

    def _safe_precision(self, value) -> int:
        """安全获取精度值
        
        Args:
            value: 精度值，可能是float、int或None
            
        Returns:
            int: 标准化后的精度值
        """
        if value is None:
            return 8  # 默认精度
        if isinstance(value, float):
            try:
                return abs(int(str(value)[::-1].find('.')))
            except:
                return 8
        return int(value)

    def _calculate_precision(self, step_str: str) -> int:
        """从步长计算精度
        
        Args:
            step_str: 步长字符串
            
        Returns:
            int: 计算出的精度
        """
        try:
            decimal_str = str(Decimal(step_str))
            if 'E' in decimal_str:
                return abs(int(decimal_str.split('E')[1]))
            return len(decimal_str.split('.')[-1]) if '.' in decimal_str else 0
        except:
            logger.warning(f"Failed to calculate precision from step: {step_str}")
            return 8  # 默认精度

    def _safe_float(self, value, default=None) -> Optional[float]:
        """安全转换为float
        
        Args:
            value: 要转换的值
            default: 转换失败时的默认值
            
        Returns:
            Optional[float]: 转换后的float值或默认值
        """
        if value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"Failed to convert to float: {value}")
            return default

    def _parse_limits(self):
        """解析市场限制"""
        try:
            limits = self.market_info.get('limits', {})
            precision = self.market_info.get('precision', {})
            raw_info = self.market_info.get('info', {})

            # 获取合约面值
            self.contract_size = self._safe_float(self.market_info.get('contractSize'), 1)

            # 根据不同平台处理精度
            if 'tickSz' in raw_info:  # OKX
                self.price_precision = self._calculate_precision(raw_info['tickSz'])
                self.amount_precision = self._calculate_precision(raw_info.get('lotSz', '0.00000001'))
                # OKX特有的限制
                self.min_amount = self._safe_float(raw_info.get('minSz'), 0)
                self.max_amount = self._safe_float(raw_info.get('maxSz'), float('inf'))
            else:  # 使用CCXT标准化数据
                self.price_precision = self._safe_precision(precision.get('price'))
                self.amount_precision = self._safe_precision(precision.get('amount'))
                # 解析数量限制
                amount_limits = limits.get('amount', {})
                self.min_amount = self._safe_float(amount_limits.get('min'), 0)
                self.max_amount = self._safe_float(amount_limits.get('max'), float('inf'))

            # 解析金额限制
            cost_limits = limits.get('cost', {})
            self.min_cost = self._safe_float(cost_limits.get('min'), 0)
            self.max_cost = self._safe_float(cost_limits.get('max'), float('inf'))

            # 解析价格限制
            price_limits = limits.get('price', {})
            self.min_price = self._safe_float(price_limits.get('min'), 0)
            self.max_price = self._safe_float(price_limits.get('max'), float('inf'))

            # 获取步长
            if 'tickSz' in raw_info:  # OKX
                self.price_step = self._safe_float(raw_info['tickSz'], 1 / (10 ** self.price_precision))
                self.amount_step = self._safe_float(raw_info.get('lotSz'), 1 / (10 ** self.amount_precision))
            else:
                self.amount_step = self._safe_float(self.market_info.get('contractSize'),
                                                    1 / (10 ** self.amount_precision))
                self.price_step = 1 / (10 ** self.price_precision)

        except Exception as e:
            logger.error(f"Error parsing market limits: {e}")
            # 设置默认值确保系统可以继续运行
            self.price_precision = 8
            self.amount_precision = 8
            self.min_amount = 0
            self.max_amount = float('inf')
            self.min_cost = 0
            self.max_cost = float('inf')
            self.min_price = 0
            self.max_price = float('inf')
            self.amount_step = 1e-8
            self.price_step = 1e-8

    def calculate_contract_amount(self, usdt_amount: float, price: float) -> float:
        """计算合约张数
        
        Args:
            usdt_amount: USDT金额
            price: 当前价格
            
        Returns:
            float: 调整后的合约张数
        """
        if price <= 0:
            raise ValueError("Price must be positive")

        # 计算原始数量
        base_amount = usdt_amount / price

        # 转换为合约张数
        contract_amount = base_amount / self.contract_size

        # 调整到合法张数
        return self.adjust_amount(contract_amount)

    def validate_cost(self, price: float, amount: float) -> bool:
        """验证订单金额是否合法
        
        Args:
            price: 价格
            amount: 数量(合约张数)
            
        Returns:
            bool: 是否合法
        """
        cost = price * amount * self.contract_size

        if not (self.min_cost <= cost <= (self.max_cost or float('inf'))):
            logger.warning(f"Cost {cost} is out of range [{self.min_cost}, {self.max_cost}]")
            return False

        return True

    def adjust_amount(self, amount: float, round_up: bool = False) -> float:
        """调整数量到合法值"""
        if amount < self.min_amount:
            return self.min_amount

        # 转换为Decimal以保证精确计算
        d_amount = Decimal(str(amount))
        d_step = Decimal(str(self.amount_step))

        # 计算步长倍数
        steps = d_amount / d_step

        # 根据方向取整
        if round_up:
            steps = steps.quantize(Decimal('1'), rounding=ROUND_UP)
        else:
            steps = steps.quantize(Decimal('1'), rounding=ROUND_DOWN)

        # 计算结果
        result = float(steps * d_step)

        # 处理精度
        return round(result, self.amount_precision)

    def adjust_price(self, price: float, round_up: bool = False) -> float:
        """调整价格到合法值"""
        if price < self.min_price:
            return self.min_price

        # 转换为Decimal以保证精确计算
        d_price = Decimal(str(price))
        d_step = Decimal(str(self.price_step))

        # 计算步长倍数
        steps = d_price / d_step

        # 根据方向取整
        if round_up:
            steps = steps.quantize(Decimal('1'), rounding=ROUND_UP)
        else:
            steps = steps.quantize(Decimal('1'), rounding=ROUND_DOWN)

        # 计算结果
        result = float(steps * d_step)

        # 处理精度
        return round(result, self.price_precision)

    def adjust_quantity(self, quantity: float, round_up: bool = False) -> float:
        """调整现货下单数量到符合交易所精度和限制的值
        
        Args:
            quantity: 原始数量
            round_up: 是否向上取整，默认向下取整
            
        Returns:
            float: 调整后的数量
        """
        try:
            # 使用 adjust_amount 方法进行基础精度调整
            adjusted_quantity = self.adjust_amount(quantity, round_up=round_up)

            # 确保满足最小下单量
            if adjusted_quantity < self.min_amount:
                logger.info(
                    f"调整后数量 {adjusted_quantity} 小于最小下单量 {self.min_amount}，"
                    f"将使用最小下单量"
                )
                adjusted_quantity = self.min_amount

            # 确保不超过最大下单量
            if self.max_amount != float('inf') and adjusted_quantity > self.max_amount:
                logger.info(
                    f"调整后数量 {adjusted_quantity} 大于最大下单量 {self.max_amount}，"
                    f"将使用最大下单量"
                )
                adjusted_quantity = self.max_amount

            logger.debug(
                f"数量调整: 原始数量={quantity}, 调整后={adjusted_quantity}, "
                f"精度={self.amount_precision}, 步长={self.amount_step}"
            )
            return adjusted_quantity
        except Exception as e:
            logger.error(f"调整数量出错: {str(e)}")
            return quantity

    def validate_order(self, price: float, amount: float) -> bool:
        """验证订单参数是否合法
        
        Args:
            price: 价格
            amount: 数量(合约张数)
            
        Returns:
            bool: 是否合法
        """
        try:
            # 数量验证
            if not (self.min_amount <= amount <= (self.max_amount or float('inf'))):
                logger.warning(f"Amount {amount} is out of range [{self.min_amount}, {self.max_amount}]")
                return False

            # 价格验证 (如果价格限制未定义则跳过)
            if self.min_price and self.max_price:
                if not (self.min_price <= price <= self.max_price):
                    logger.warning(f"Price {price} is out of range [{self.min_price}, {self.max_price}]")
                    return False

            # 金额验证
            if not self.validate_cost(price, amount):
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return False


class MarketLimitManager:
    """市场限制管理器"""

    def __init__(self, exchange: ccxt.okx, exchange_name: str):
        """初始化
        
        Args:
            exchange: CCXT交易所实例
            exchange_name: 交易所名称
        """
        self.exchange: ccxt.okx = exchange
        self.exchange_name = exchange_name
        self._market_limits: Dict[str, MarketLimit] = {}

    async def init_market_limits(self, symbols: List[str], ):
        """初始化市场限制信息
        
        Args:
            symbols: 需要监控的交易对列表(内部标准格式,如 BTC/USDT)
        """
        try:
            # 加载市场信息
            markets = await self.exchange.load_markets()

            # 根据交易所转换交易对格式
            for symbol in symbols:
                if self.exchange_name.lower() == 'okx':
                    if symbol in markets:
                        self._market_limits[symbol] = MarketLimit(markets[symbol])
                        logger.debug(f"已更新 {self.exchange_name} {symbol} 的限制信息")

                elif self.exchange_name.lower() == 'binance':
                    if symbol in markets:
                        self._market_limits[symbol] = MarketLimit(markets[symbol])
                        logger.debug(f"已更新 {self.exchange_name} {symbol} 的限制信息")

            logger.info(f"已初始化 {len(symbols)} 个交易对的市场限制信息")

        except Exception as e:
            logger.error(f"初始化市场限制信息失败: {str(e)}")
            raise e

    def get_market_limit(self, symbol: str) -> Optional[MarketLimit]:
        """获取市场限制信息
        
        Args:
            symbol: 交易对
            
        Returns:
            Optional[MarketLimit]: 市场限制信息，如果不存在则返回None
        """
        return self._market_limits.get(symbol)
