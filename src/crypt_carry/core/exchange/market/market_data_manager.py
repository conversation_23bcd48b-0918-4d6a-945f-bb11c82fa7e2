"""市场数据管理器基类，用于管理交易所的市场数据和基差数据"""
import logging
import time
from typing import Dict, List, Optional

from crypt_carry.utils.calc import calculate_basis_rate
from util.formatter import format_basis_rate

logger = logging.getLogger(__name__)

class MarketDataManager:
    """市场数据管理器基类，用于管理每个交易所的市场数据和基差数据"""

    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        self.market_data: Dict[str, Dict[str, Dict]] = {}  # exchange -> symbol -> data
        self.basis_data: Dict[str, Dict[str, Dict]] = {}  # exchange -> symbol -> data
        self.last_update: Dict[str, Dict[str, float]] = {}  # exchange -> symbol -> timestamp

    def update_market_data(self, exchange: str, standard_symbol: str, data: Dict) -> None:
        """更新市场数据并计算基差率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            data: 市场数据，格式如下：
                {
                    "spot_price": float,  # 现货价格
                    "futures_price": float,  # 合约价格
                }
        """
        try:
            # 初始化数据结构
            if exchange not in self.market_data:
                self.market_data[exchange] = {}
            if exchange not in self.last_update:
                self.last_update[exchange] = {}
            if exchange not in self.basis_data:
                self.basis_data[exchange] = {}

            # 初始化或获取现有市场数据
            if standard_symbol not in self.market_data[exchange]:
                self.market_data[exchange][standard_symbol] = {
                    'spot_price': None,
                    'futures_price': None,
                    'spot_timestamp': None,
                    'futures_timestamp': None,
                    'last_update': None
                }

            # 更新市场数据，保留未更新的字段
            current_data = self.market_data[exchange][standard_symbol]
            current_timestamp = time.time()
            
            if data.get('spot_price') is not None:
                current_data['spot_price'] = data['spot_price']
                current_data['spot_timestamp'] = current_timestamp
            if data.get('futures_price') is not None:
                current_data['futures_price'] = data['futures_price']
                current_data['futures_timestamp'] = current_timestamp

            # 保存更新后的数据
            current_data['last_update'] = current_timestamp
            self.market_data[exchange][standard_symbol] = current_data
            self.last_update[exchange][standard_symbol] = current_timestamp

            # 记录日志，同时显示现有的现货和合约价格
            logger.debug(
                f"{exchange} {standard_symbol} 市场数据更新 - "
                f"现货: {current_data.get('spot_price')} ({format_timestamp(current_data.get('spot_timestamp'))}), "
                f"合约: {current_data.get('futures_price')} ({format_timestamp(current_data.get('futures_timestamp'))})"
            )

            # 如果同时有现货和合约价格，计算基差
            spot_price = current_data.get('spot_price')
            futures_price = current_data.get('futures_price')

            # 检查价格数据的时效性
            max_price_age = 60  # 60秒内的数据视为有效
            if (spot_price is not None and futures_price is not None and
                    current_data.get('spot_timestamp') is not None and
                    current_data.get('futures_timestamp') is not None and
                    current_timestamp - current_data['spot_timestamp'] <= max_price_age and
                    current_timestamp - current_data['futures_timestamp'] <= max_price_age):

                basis = calculate_basis_rate(spot_price, futures_price)
                basis_info = {
                    'spot_price': spot_price,
                    'futures_price': futures_price,
                    'basis_rate': basis,
                    'timestamp': int(current_timestamp * 1000),
                    'spot_timestamp': current_data['spot_timestamp'],
                    'futures_timestamp': current_data['futures_timestamp']
                }

                # 更新基差数据
                if standard_symbol not in self.basis_data[exchange]:
                    self.basis_data[exchange][standard_symbol] = {}
                self.basis_data[exchange][standard_symbol] = basis_info

                logger.debug(
                    f"{exchange} {standard_symbol} 基差数据更新 - "
                    f"基差率: {format_basis_rate(basis)}"
                )

        except Exception as e:
            logger.error(f"更新市场数据失败: {str(e)}")

    def get_market_data(self, exchange: str, standard_symbol: str) -> Optional[Dict]:
        """获取市场数据
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            
        Returns:
            Dict: 市场数据
        """
        return self.market_data.get(exchange, {}).get(standard_symbol)

    def get_market_data(self, exchange: str, standard_symbol: str, is_spot: bool = True) -> float:
        """获取特定交易所和交易对的市场数据
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            is_spot: 是否为现货市场，默认为True
        
        Returns:
            Decimal: 市场价格
        """
        market_data = self.market_data.get(exchange, {}).get(standard_symbol)
        price = None
        if is_spot:
            price = market_data['spot_price']
        else:
            price = market_data['futures_price']
        return price

    def get_basis_data(self, exchange: str, standard_symbol: str) -> Optional[Dict]:
        """获取基差数据
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            
        Returns:
            Dict: 基差数据，格式如下：
                {
                    "spot_price": float,
                    "futures_price": float,
                    "basis_rate": float,
                    "timestamp": int
                }
                如果数据不存在，则返回 None
        """
        return self.basis_data.get(exchange, {}).get(standard_symbol)

    def get_basis_rate(self, exchange: str, standard_symbol: str) -> Optional[float]:
        """获取基差率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            
        Returns:
            float: 基差率，如果数据不存在，则返回 None
        """
        try:
            return self.basis_data.get(exchange, {}).get(standard_symbol).get('basis_rate')
        except Exception as e:
            logger.error(f"获取基差率失败: {str(e)}, exchange: {exchange}, symbol: {standard_symbol}")
            return None

    def get_all_market_data(self, exchange: str = None) -> Dict:
        """获取所有市场数据
        
        Args:
            exchange: 交易所名称，如果不指定则返回所有交易所的数据
            
        Returns:
            Dict: 市场数据
        """
        if exchange:
            return self.market_data.get(exchange, {})
        return self.market_data

    def get_all_basis_data(self, exchange: str = None) -> Dict:
        """获取所有基差数据
        
        Args:
            exchange: 交易所名称，如果不指定则返回所有交易所的数据
            
        Returns:
            Dict: 基差数据
        """
        if exchange:
            return self.basis_data.get(exchange, {})
        return self.basis_data

    def get_symbols(self, exchange: str = None) -> List[str]:
        """获取交易对列表
        
        Args:
            exchange: 交易所名称，如果不指定则返回所有交易所的交易对并集
            
        Returns:
            List[str]: 交易对列表
        """
        if exchange:
            return list(self.market_data.get(exchange, {}).keys())

        # 返回所有交易所的交易对并集
        symbols = set()
        for exchange_data in self.market_data.values():
            symbols.update(exchange_data.keys())
        return list(symbols)

    def get_last_update_time(self, exchange: str, standard_symbol: str) -> Optional[float]:
        """获取最后更新时间
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            
        Returns:
            float: 最后更新时间戳
        """
        return self.last_update.get(exchange, {}).get(standard_symbol)

    def is_market_data_ready(self, exchange: str, symbols: List[str], max_age: int = 10) -> bool:
        """检查指定交易所和交易对的行情数据是否已准备好
        
        Args:
            exchange: 交易所名称
            symbols: 交易对列表
            max_age: 最大有效时间（秒），默认30秒
            
        Returns:
            bool: 如果所有交易对的现货和合约数据都已加载且在有效时间内，返回True
        """
        current_time = time.time()

        # 检查每个交易对
        for symbol in symbols:
            market_data = self.market_data.get(exchange, {}).get(symbol)

            # 如果没有行情数据，返回False
            if not market_data:
                logger.info(f"{exchange} {symbol} 行情数据未加载")
                return False

            # 检查现货和合约价格是否存在
            spot_price = market_data.get('spot_price')
            futures_price = market_data.get('futures_price')
            spot_timestamp = market_data.get('spot_timestamp')
            futures_timestamp = market_data.get('futures_timestamp')

            # 如果价格不存在或时间戳不存在，返回False
            if (spot_price is None or futures_price is None or
                    spot_timestamp is None or futures_timestamp is None):
                logger.info(f"{exchange} {symbol} 价格数据不完整: spot={spot_price}, futures={futures_price}")
                return False

            # 检查数据是否过期
            if (int(current_time - spot_timestamp) > max_age or
                    int(current_time - spot_timestamp) > max_age):
                logger.info(f"{exchange} {symbol} 价格数据已过期: "
                             f"spot age={int(current_time - spot_timestamp)}s, "
                             f"futures age={int(current_time - futures_timestamp)}s")
                return False

        # 所有检查都通过，数据已准备好
        return True


def format_timestamp(ts: Optional[float]) -> str:
    """格式化时间戳显示"""
    if ts is None:
        return "N/A"
    age = time.time() - ts
    return f"{age:.1f}s ago"
