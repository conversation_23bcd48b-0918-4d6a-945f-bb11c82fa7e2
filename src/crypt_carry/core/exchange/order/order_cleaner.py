#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
订单资源清理器

负责取消订单和清理相关资源，将与订单清理相关的逻辑从OrderExecutor中分离出来。
"""
import logging

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.order.order_creator import OrderCreator
from crypt_carry.core.exchange.order.order_error_handler import OrderErrorHandler
from crypt_carry.core.exchange.order.order_state import OrderStatus
from crypt_carry.core.exchange.order.order_state_manager import OrderStateManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class OrderCleaner:
    """订单资源清理器
    
    负责取消订单和清理资源
    """

    def __init__(
            self,
            exchange_name: str,
            exchange_client: ExchangeClient,
            order_state_manager: OrderStateManager,
            order_creator: OrderCreator
    ):
        """初始化订单清理器
        
        Args:
            exchange_name: 交易所名称
            exchange_client: 交易所客户端
            order_state_manager: 订单状态管理器
            order_creator: 订单创建器
        """
        self.exchange_name = exchange_name
        self.exchange_client = exchange_client
        self.order_state_manager = order_state_manager
        self.order_creator = order_creator

        # 初始化订单错误处理器
        self.order_error_handler = OrderErrorHandler(
            exchange_client=exchange_client,
            order_state_manager=order_state_manager,
            order_creator=order_creator,
        )

        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

    async def cleanup_orders(self, symbol: str):
        """清理订单
        
        直接执行取消合约和现货订单操作，不进行复杂的状态检查
        
        Args:
            symbol: 交易对
        """
        order_state = self.order_state_manager.get_order_state(symbol)
        if not order_state:
            return

        try:
            logger.info(f"{self.exchange_name} {symbol} 开始清理订单: {order_state.get_status_summary()}")

            # 1. 如果合约订单活跃，尝试取消
            if OrderStatus.is_active_status(str(order_state.futures_status)):
                try:
                    futures_order = await self.exchange_client.fetch_order(order_state.futures_order_id, symbol, False)

                    # 如果订单状态为开放，则取消
                    if futures_order.get('status') == 'open':
                        cancel_result = await self.order_creator.cancel_order_with_retry(
                            order_id=order_state.futures_order_id,
                            symbol=symbol,
                            is_spot=False
                        )
                        logger.info(
                            f"{self.exchange_name} {symbol} 取消合约订单结果: {cancel_result['status'] if cancel_result else 'failed'}")
                    # 如果订单已成交，更新状态
                    elif futures_order.get('status') == 'closed':
                        order_state.futures_status = OrderStatus.FILLED
                        order_state.futures_fill_quantity = futures_order.get('filled', 0)
                        order_state.futures_cost = futures_order.get('cost', 0)
                        logger.info(
                            f"{self.exchange_name} {symbol} 合约订单已成交: {order_state.futures_fill_quantity}")

                        # 如果合约已成交但现货未成交，尝试执行平仓
                        if order_state.spot_status != OrderStatus.FILLED:
                            await self.order_error_handler.handle_taker_failure(symbol)
                            return  # 平仓后直接返回，不进行后续操作
                except Exception as e:
                    logger.error(f"{self.exchange_name} {symbol} 处理合约订单异常: {str(e)}")
                    order_state.record_error(e, "处理合约订单异常")

            # 2. 如果现货订单活跃，尝试取消所有现货订单
            if OrderStatus.is_active_status(str(order_state.spot_status)):
                try:
                    orders = await self.order_creator.fetch_open_orders_with_retry(symbol=symbol, is_spot=True)
                    cancel_count = 0

                    for order in orders:
                        await self.order_creator.cancel_order_with_retry(order_id=order['id'], symbol=symbol,
                                                                         is_spot=True)
                        cancel_count += 1

                    if cancel_count > 0:
                        order_state.spot_status = OrderStatus.CANCELED
                        logger.info(f"{self.exchange_name} {symbol} 成功取消 {cancel_count} 个现货订单")
                except Exception as e:
                    logger.error(f"{self.exchange_name} {symbol} 取消现货订单异常: {str(e)}")
                    order_state.record_error(e, "取消现货订单异常")
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 清理订单主流程异常: {str(e)}")
            if order_state:
                order_state.record_error(e, "清理订单主流程异常")
        finally:
            # 清理状态
            await self.cleanup_symbol(symbol)

    async def cleanup_symbol(self, symbol: str):
        """清理symbol相关的资源
        
        取消并清理与交易对相关的所有资源
        
        Args:
            symbol: 交易对
        """
        # 记录最终状态
        order_state = self.order_state_manager.get_order_state(symbol)
        if order_state:
            logger.info(f"{self.exchange_name} {symbol} 资源清理前 - {order_state.get_status_summary()}")

        # 取消监控任务
        await self.order_state_manager.cancel_monitoring_task(symbol)

        # 清理所有相关资源
        await self.order_state_manager.cleanup_symbol(symbol)

        logger.info(f"{self.exchange_name} {symbol} 资源清理完成")
