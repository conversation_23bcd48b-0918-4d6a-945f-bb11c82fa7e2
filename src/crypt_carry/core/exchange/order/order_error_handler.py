"""订单错误处理器

负责处理订单执行过程中的各种异常情况，包括订单创建失败、订单超时、状态不一致等
"""

import asyncio
import logging
from typing import Optional, Callable

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.order.constants import OrderStatus
from crypt_carry.core.exchange.order.exceptions import (
    OrderExecutionError, MakerOrderCreationError,
    TakerOrderCreationError, OrderMonitorError, OrderTimeoutError
)
from crypt_carry.core.exchange.order.order_creator import OrderCreator
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.core.exchange.order.order_state_manager import OrderStateManager
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class OrderErrorHandler:
    """订单错误处理器
    
    负责处理订单执行过程中的各种异常情况，如：
    1. Maker订单创建失败
    2. Taker订单创建失败（需要平仓已成交的合约订单）
    3. 订单状态不一致（合约已成交但现货未成交）
    4. 订单超时或监控失败
    5. 其他未预期的异常
    """

    def __init__(self, exchange_client: ExchangeClient, order_state_manager: OrderStateManager,
                 order_creator: OrderCreator):
        """初始化订单错误处理器
        
        Args:
            exchange_client: 交易所客户端
            order_state_manager: 订单状态管理器
            order_creator: 订单创建器
        """
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name
        self.order_state_manager = order_state_manager
        self.order_creator = order_creator
        self.ding_talk_manager = DingTalkManager.get_instance()
        # 初始化待处理任务管理器（单例模式）
        self.pending_tasks_manager = PendingTasksManager.get_instance()

    async def handle_exception(self, symbol: str, exception: Exception,
                               cleanup_orders_callback: Callable,
                               create_emergency_spot_order_callback: Optional[Callable] = None) -> None:
        """处理异常
        
        根据异常类型和当前订单状态，执行相应的处理逻辑
        
        Args:
            symbol: 交易对
            exception: 异常
            cleanup_orders_callback: 清理订单的回调函数
            create_emergency_spot_order_callback: 创建紧急现货订单的回调函数
        """

        order_state = self.order_state_manager.get_order_state(symbol)
        if order_state:
            logger.info(f"{self.exchange_name} {symbol} 订单状态: {order_state.get_status_summary()}")
        else:
            logger.error(f"{self.exchange_name} {symbol} 处理异常时未找到订单状态")
            return
        
        if isinstance(exception, asyncio.CancelledError):
            logger.error(f"{self.exchange_name} {symbol} 订单执行任务被取消")
            # 取消订单可能需要清理
            asyncio.create_task(cleanup_orders_callback(symbol))
            logger.info(f"{self.exchange_name} {symbol} cancelledError 开始回滚任务")
            await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)

        elif isinstance(exception, MakerOrderCreationError):
            # Maker订单创建失败，不需要特殊处理
            logger.error(f"{self.exchange_name} {symbol} Maker订单创建失败: {str(exception)}")
            self.order_state_manager.update_order_status(symbol, 'futures_status', OrderStatus.FAILED)
            asyncio.create_task(cleanup_orders_callback(symbol))
            logger.info(f"{self.exchange_name} {symbol} MakerOrderCreationError 开始回滚任务")
            await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)

        elif isinstance(exception, TakerOrderCreationError):
            # Taker订单创建失败，需要平仓已成交的合约订单
            logger.error(f"{self.exchange_name} {symbol} Taker订单创建失败，准备平仓合约: {str(exception)}")
            self.order_state_manager.update_order_status(symbol, 'spot_status', OrderStatus.FAILED)
            asyncio.create_task(self.handle_taker_failure(symbol, cleanup_orders_callback))

        elif isinstance(exception, (OrderTimeoutError, OrderMonitorError)):
            # 订单超时或监控失败
            logger.warning(f"{self.exchange_name} {symbol}, order_state:{order_state}, 订单异常: {str(exception)}")

            if not order_state:
                logger.warning(f"{self.exchange_name} {symbol} 订单异常但未找到订单状态")
                return

            # 根据错误信息判断是否为订单超时而非真正失败
            error_message = str(exception).lower()
            is_timeout_error = 'timeout' in error_message or '超时' in error_message

            # 如果是超时错误，需要进一步检查订单实际状态
            if is_timeout_error:
                logger.warning(f"{self.exchange_name} {symbol}, 检测到订单超时，准备检查实际订单状态")
                asyncio.create_task(self.verify_and_fix_order_status(symbol, order_state, cleanup_orders_callback,
                                                                     create_emergency_spot_order_callback))
                return

            # 检查是否需要平仓
            if order_state.futures_status == OrderStatus.FILLED:
                logger.error(f"{self.exchange_name} {symbol} 合约已成交但现货订单异常，准备平仓")
                self.order_state_manager.update_order_status(symbol, 'spot_status', OrderStatus.FAILED)
                asyncio.create_task(self.handle_taker_failure(symbol, cleanup_orders_callback))
            else:
                # 如果合约未成交，只需要清理订单
                asyncio.create_task(cleanup_orders_callback(symbol))
        elif isinstance(exception, OrderExecutionError):
            # 其他订单执行错误，需要检查状态决定是否平仓
            logger.error(f"{self.exchange_name} {symbol} 订单执行错误: {str(exception)}")

            if order_state:
                # 记录错误到订单状态
                order_state.record_error(exception, "订单执行")

                # 检查错误是否与maker超时相关
                error_message = str(exception).lower()
                if 'maker' in error_message and ('timeout' in error_message or '超时' in error_message):
                    logger.warning(
                        f"{self.exchange_name} {symbol} 检测到Maker超时错误，可能已成交但未被正确记录，进行状态验证")
                    asyncio.create_task(self.verify_and_fix_order_status(symbol, order_state, cleanup_orders_callback,
                                                                         create_emergency_spot_order_callback))
                    return

                # 如果合约已成交但现货未成交，需要平仓
                if (order_state.futures_status == OrderStatus.FILLED and
                        order_state.spot_status != OrderStatus.FILLED):
                    logger.error(f"{self.exchange_name} {symbol} 合约已成交但现货未完成，准备平仓")
                    asyncio.create_task(self.handle_taker_failure(symbol, cleanup_orders_callback))
                else:
                    # 记录错误到订单状态
                    order_state.record_error(exception, "订单执行")
                    logger.info(f"{self.exchange_name} {symbol} 订单执行失败，准备清理订单")
                    # 其他情况清理订单
                    asyncio.create_task(cleanup_orders_callback(symbol))

                logger.info(f"{self.exchange_name} {symbol} OrderExecutionError 开始回滚任务")
                await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
        else:
            # 未预期的错误，需要检查状态决定是否平仓
            logger.error(f"{self.exchange_name} {symbol} 订单执行任务发生未预期异常: {str(exception)}")

            if order_state:
                # 记录错误到订单状态
                order_state.record_error(exception, "未预期异常")

                # 如果合约已成交但现货未成交，需要平仓
                if (order_state.futures_status == OrderStatus.FILLED and
                        order_state.spot_status != OrderStatus.FILLED):
                    logger.error(f"{self.exchange_name} {symbol} 合约已成交但现货未完成，准备平仓")
                    # 发送高优先级通知
                    self.ding_talk_manager.send_message(
                        f"⚠️ 警告! {self.exchange_name} {symbol} 发生未预期异常，"
                        f"合约已成交但现货未完成，准备平仓\n"
                        f"错误信息: {str(exception)}\n"
                        f"订单状态: {order_state.get_status_summary()}",
                        prefix=self.exchange_name
                    )
                    asyncio.create_task(self.handle_taker_failure(symbol, cleanup_orders_callback))
                else:
                    # 记录错误到订单状态
                    order_state.record_error(exception, "未预期异常")
                    logger.info(f"{self.exchange_name} {symbol} 订单执行失败，准备清理订单")
                    # 其他情况清理订单
                    asyncio.create_task(cleanup_orders_callback(symbol))
                logger.info(f"{self.exchange_name} {symbol} 未预期异常 开始回滚任务")
                await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)

    async def handle_taker_failure(self, symbol: str, cleanup_orders_callback: Callable[[str], None]) -> None:
        """处理 Taker 订单创建失败的情况
        
        当 Taker 订单（现货订单）创建失败时，直接平仓已成交的合约订单，
        以避免单边持仓带来的风险。采用简化的处理逻辑，直接执行平仓操作。
        
        Args:
            symbol: 交易对
        """
        order_state = self.order_state_manager.get_order_state(symbol)
        if not order_state:
            logger.error(f"{self.exchange_name} {symbol} 处理 Taker 失败时未找到订单状态")
            return

        logger.info(f"{self.exchange_name} {symbol} 开始处理 Taker 订单失败，准备平仓")

        # 获取持仓信息
        close_quantity = order_state.futures_fill_quantity

        try:
            await self.verify_and_fix_order_status(symbol, order_state, cleanup_orders_callback)

        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 处理 Taker 订单创建失败发生异常: {str(e)}")
            # 发送错误通知
            self.ding_talk_manager.send_message(
                f"❌ {self.exchange_name} {symbol} 平仓失败，请手动处理!\n"
                f"错误信息: {str(e)}\n"
                f"合约成交量: {close_quantity}",
                prefix=self.exchange_name, is_at_all=True
            )
            logger.info(f"{self.exchange_name} {symbol} handle_taker_failure 开始回滚任务")
            await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
        finally:
            # 清理资源
            self.order_state_manager.cleanup_symbol(symbol)

    async def verify_and_fix_order_status(self, symbol: str, order_state: OrderState,
                                          cleanup_orders_callback: Callable,
                                          create_emergency_spot_order_callback: Optional[Callable] = None) -> None:
        """验证和修复订单状态
        
        简化的订单状态验证和修复逻辑。当检测到可能存在订单状态不一致时，
        直接查询实际订单状态并采取相应操作，不进行复杂的状态检查。
        
        Args:
            symbol: 交易对
            order_state: 订单状态对象
            cleanup_orders_callback: 清理订单的回调函数
            create_emergency_spot_order_callback: 创建紧急现货订单的回调函数
        """
        logger.info(
            f"{self.exchange_name} {symbol}, order_state:{order_state.get_status_summary()}, 开始验证和修复订单状态")

        # 1. 验证合约订单状态
        if order_state.futures_status not in [OrderStatus.FILLED, OrderStatus.FAILED, OrderStatus.CANCELED]:
            logger.info(
                f"{self.exchange_name} {symbol} 检测到合约订单状态不一致，开始修复, order_state:{order_state.to_dict()}")
            futures_order_id = order_state.futures_order_id
            try:
                # 先执行取消订单
                await self.order_creator.cancel_order_with_retry(order_id=futures_order_id, symbol=symbol,
                                                                 is_spot=False)
                logger.info(f"{self.exchange_name} {symbol} 成功发送合约订单取消请求")
                await asyncio.sleep(1)  # 简短等待让取消操作生效
                # 查询合约订单实际状态
                futures_order = await self.exchange_client.fetch_order(
                    symbol=symbol,
                    order_id=futures_order_id,
                    is_spot=False
                )

                if not futures_order:
                    logger.error(f"{self.exchange_name} {symbol} 无法获取合约订单状态: {futures_order_id}")
                    await cleanup_orders_callback(symbol)
                    logger.info(
                        f"{self.exchange_name} {symbol} verify_and_fix_order_status 无法获取合约订单状态,开始回滚任务")
                    await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
                    return

                # 提取订单状态信息
                actual_status = futures_order.get('status', '')
                filled_amount = float(futures_order.get('filled', 0))

                if actual_status in ['closed', 'filled'] or filled_amount > 0:
                    # 订单实际已成交或部分成交，更新为已成交状态
                    order_state.futures_status = OrderStatus.FILLED
                    order_state.futures_fill_quantity = filled_amount
                    order_state.futures_cost = futures_order.get('cost', 0)
                    logger.warning(f"{self.exchange_name} {symbol} 合约订单实际已成交，更新状态为FILLED")
                elif actual_status == 'canceled' and filled_amount == 0:
                    # 订单已取消且未成交
                    order_state.futures_status = OrderStatus.CANCELED
                    logger.info(f"{self.exchange_name} {symbol} 合约订单已取消且未成交，状态更新为CANCELED")
                    logger.info(
                        f"{self.exchange_name} {symbol} verify_and_fix_order_status 订单已取消且未成交, 开始回滚任务")
                    await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol} 检查合约订单状态异常: {str(e)}")

        # 2. 处理合约已成交但现货未成交的情况
        if order_state.futures_status == OrderStatus.FILLED and order_state.spot_status != OrderStatus.FILLED:
            logger.warning(f"{self.exchange_name} {symbol} 检测到合约已成交但现货未成交，尝试修复")

            # 发送通知
            self.ding_talk_manager.send_message(
                f"⚠️ {self.exchange_name} {symbol} 状态不一致，执行修复\n" +
                f"合约状态: {order_state.futures_status}，成交量: {order_state.futures_fill_quantity}\n" +
                f"现货状态: {order_state.spot_status}",
                prefix=self.exchange_name
            )

            try:
                # 如果提供了创建紧急现货订单的回调函数，尝试创建现货订单完成对冲
                if create_emergency_spot_order_callback:
                    await create_emergency_spot_order_callback(order_state, is_emergency=True)
                    logger.info(f"{self.exchange_name} {symbol} 成功创建紧急现货订单完成对冲")
                    return
                else:
                    # 如果没有提供回调函数，改为平仓处理
                    logger.info(f"{self.exchange_name} {symbol} 无法创建紧急现货订单，转为平仓处理")
                    await self.handle_taker_failure(symbol, cleanup_orders_callback)
                    logger.info(f"{self.exchange_name} {symbol} 无法创建紧急现货订单，转为平仓处理, 开始回滚任务")
                    await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
                    return
            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol} 创建紧急现货订单失败: {str(e)}")
                # 如果创建现货订单失败，直接平仓合约头寸
                await self.handle_taker_failure(symbol, cleanup_orders_callback)
                logger.info(f"{self.exchange_name} {symbol} 创建紧急现货订单失败, 开始回滚任务")
                await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
                return

        # 3. 如果没有需要修复的问题，继续清理订单
        logger.info(f"{self.exchange_name} {symbol} 状态验证完成，执行清理操作")
        await cleanup_orders_callback(symbol)
