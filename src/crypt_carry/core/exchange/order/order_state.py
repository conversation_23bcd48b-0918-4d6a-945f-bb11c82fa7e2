"""订单状态管理相关类定义"""

from datetime import datetime
from typing import Dict, Optional

from crypt_carry.constant.direction_type import DirectionType
from crypt_carry.constant.trade_type import TradeType
from crypt_carry.core.exchange.order.constants import OrderStatus


class OrderState:
    """订单状态管理"""

    def __init__(self, symbol: str, futures_order_id: str, spot_order_id: str, position: Dict,
                 futures_fill_quantity: float = 0, futures_cost: float = 0,
                 spot_fill_quantity: float = 0, spot_cost: float = 0):
        self.symbol = symbol
        self.futures_order_id = futures_order_id
        self.spot_order_id = spot_order_id
        self.position = position
        self.direction = position.get('direction', DirectionType.LONG_SPOT_SHORT_FUTURES)
        self.trade_type = position.get('trade_type', TradeType.CREATE)
        self.futures_fill_quantity = futures_fill_quantity
        self.futures_cost = futures_cost
        self.spot_fill_quantity = spot_fill_quantity
        self.spot_cost = spot_cost
        # 分别记录合约和现货订单的开始时间
        self.futures_start_time: Optional[datetime] = None
        self.spot_start_time: Optional[datetime] = None
        self.is_filled = False
        self.retry_count = 0
        # 状态管理
        self.futures_status = OrderStatus.PENDING
        self.spot_status = OrderStatus.PENDING
        self.last_error = None  # 记录最后一次错误
        self.execution_info = {}  # 记录执行过程中的关键信息

    def start_futures_order(self):
        """开始合约订单"""
        self.futures_start_time = datetime.now()
        self.futures_status = OrderStatus.EXECUTING

    def start_spot_order(self):
        """开始现货订单"""
        self.spot_start_time = datetime.now()
        self.spot_status = OrderStatus.EXECUTING

    def is_futures_timeout(self, timeout_seconds: float) -> bool:
        """检查合约订单是否超时"""
        return self.futures_start_time and (datetime.now() - self.futures_start_time).total_seconds() > timeout_seconds

    def is_spot_timeout(self, timeout_seconds: float) -> bool:
        """检查现货订单是否超时"""
        return self.spot_start_time and (datetime.now() - self.spot_start_time).total_seconds() > timeout_seconds

    def record_error(self, error: Exception, context: str = None):
        """记录错误信息
        
        Args:
            error: 异常对象
            context: 错误发生的上下文
        """
        self.last_error = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat()
        }

    def record_execution_info(self, stage: str, info: Dict):
        """记录执行信息
        
        Args:
            stage: 执行阶段
            info: 相关信息
        """
        self.execution_info[stage] = {
            'info': info,
            'timestamp': datetime.now().isoformat()
        }

    def is_active(self) -> bool:
        """检查订单是否处于活跃状态"""
        return (OrderStatus.is_active_status(str(self.futures_status)) or
                OrderStatus.is_active_status(str(self.spot_status)))

    def is_all_filled(self) -> bool:
        """检查所有订单是否都已成交"""
        return (self.futures_status == OrderStatus.FILLED and
                self.spot_status == OrderStatus.FILLED)

    def is_any_failed(self) -> bool:
        """检查是否有任何订单失败"""
        return (self.futures_status == OrderStatus.FAILED or
                self.spot_status == OrderStatus.FAILED)

    def get_status_summary(self) -> str:
        """获取状态摘要
        
        Returns:
            str: 状态摘要字符串
        """
        return (f"futures_order_id:{self.futures_order_id}, futures_status: {self.futures_status}, "
                f"spot_order_id:{self.spot_order_id}, spot_status: {self.spot_status}, "
                f"futures_fill: {self.futures_fill_quantity}, "
                f"spot_fill: {self.spot_fill_quantity}")

    def is_completed(self) -> bool:
        """检查订单是否已完成（包括成交、失败或取消）
        
        Returns:
            bool: 如果订单已完成则返回True，否则返回False
        """
        return (OrderStatus.is_final_status(str(self.futures_status)) and
                OrderStatus.is_final_status(str(self.spot_status)))

    def to_dict(self):
        return {
            'symbol': self.symbol,
            'futures_order_id': self.futures_order_id,
            'spot_order_id': self.spot_order_id,
            'futures_start_time': self.futures_start_time,
            'spot_start_time': self.spot_start_time,
            'position': self.position,
            'futures_fill_quantity': self.futures_fill_quantity,
            'futures_cost': self.futures_cost,
            'spot_fill_quantity': self.spot_fill_quantity,
            'spot_cost': self.spot_cost,
            'futures_status': str(self.futures_status),
            'spot_status': str(self.spot_status),
            'last_error': self.last_error,
            'execution_info': self.execution_info,
        }
