"""订单执行相关异常类定义"""


class OrderExecutionError(Exception):
    """订单执行错误基类"""
    pass


class OrderCreationError(OrderExecutionError):
    """订单创建错误"""
    pass


class MakerOrderCreationError(OrderCreationError):
    """Maker订单创建错误"""
    pass


class TakerOrderCreationError(OrderCreationError):
    """Taker订单创建错误，需要平仓已成交的合约订单"""
    pass


class OrderMonitorError(OrderExecutionError):
    """订单监控错误"""
    pass


class OrderGlobalTimeoutError(OrderExecutionError):
    """订单全局超时错误"""
    pass


class OrderTimeoutError(OrderExecutionError):
    """订单超时错误"""
    pass
