"""订单执行相关常量定义"""

from enum import Enum


class OrderStatus(str, Enum):
    """订单状态枚举"""
    PENDING = 'pending'  # 等待中
    EXECUTING = 'executing'  # 执行中
    FILLED = 'filled'  # 已成交
    FAILED = 'failed'  # 失败
    CANCELED = 'canceled'  # 已取消

    def __str__(self) -> str:
        return self.value

    @classmethod
    def is_final_status(cls, status: str) -> bool:
        """判断是否为终态
        
        Args:
            status: 状态值
            
        Returns:
            bool: 是否为终态
        """
        return status in [cls.FILLED.value, cls.FAILED.value, cls.CANCELED.value]

    @classmethod
    def is_active_status(cls, status: str) -> bool:
        """判断是否为活跃状态
        
        Args:
            status: 状态值
            
        Returns:
            bool: 是否为活跃状态
        """
        return status in [cls.PENDING.value, cls.EXECUTING.value]
