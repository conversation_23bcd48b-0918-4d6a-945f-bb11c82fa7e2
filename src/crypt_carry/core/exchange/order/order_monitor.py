"""
订单监控器

负责监控订单状态和进度
"""
import asyncio
import logging
import time
from decimal import Decimal
from typing import Dict

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.order.exceptions import (
    OrderMonitorError, OrderTimeoutError, OrderGlobalTimeoutError, OrderExecutionError
)
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class OrderMonitor:
    """订单监控器
    
    负责监控订单状态和进度，提供统一的订单监控接口
    """

    def __init__(
            self,
            exchange_name: str,
            exchange_client: ExchangeClient,
            global_execution_timeout_ms=60000,
            order_timeout=30000

    ):
        """初始化订单监控器
        
        Args:
            exchange_name: 交易所名称
            exchange_client: 交易所客户端
            global_execution_timeout_ms: 全局订单执行超时时间（毫秒）
            order_timeout: 订单超时时间(秒)
        """
        self.exchange_name = exchange_name
        self.exchange_client = exchange_client
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 配置参数
        self.global_execution_timeout_ms = global_execution_timeout_ms
        self.order_timeout = order_timeout

    async def monitor_order(
            self,
            symbol: str,
            order_id: str,
            is_spot: bool,
            order_state: OrderState,
            check_interval: float,
            error_type: str
    ) -> Dict:
        """监控订单状态直到完成
        
        Args:
            symbol: 交易对
            order_id: 订单ID
            is_spot: 是否是现货订单
            order_state: 订单状态
            check_interval: 检查间隔(秒)
            error_type: 错误类型,用于日志
            
        Returns:
            Dict: 订单信息
            
        Raises:
            OrderMonitorError: 监控异常
            OrderTimeoutError: 订单超时
        """
        start_time = time.time()
        last_filled = Decimal('0')

        while True:
            # 检查全局执行超时
            if (time.time() - start_time) * 1000 > self.global_execution_timeout_ms:
                await self.ding_talk_manager.send_message(
                    f"订单执行全局超时: {order_id}, 耗时: {(time.time() - start_time) * 1000:.2f}ms",
                    prefix=self.exchange_name,
                )
                raise OrderGlobalTimeoutError(f"{error_type}全局执行超时")

            try:
                # 检查是否超时
                if is_spot:
                    if order_state.is_spot_timeout(self.order_timeout):
                        raise OrderTimeoutError(f"{error_type}超时未成交")
                else:
                    if order_state.is_futures_timeout(self.order_timeout):
                        raise OrderTimeoutError(f"{error_type}超时未成交")

                # 查询订单状态
                order = await self.exchange_client.fetch_order(
                    order_id=order_id,
                    symbol=symbol,
                    is_spot=is_spot
                )
                status = order['status']

                # 订单完全成交
                if status == 'closed':
                    logger.info(f"{self.exchange_name} {symbol} {error_type}成交: {order}")
                    # 统一订单字段
                    filled_order = {
                        'quantity': order.get('filled', order.get('amount', 0)),  # 优先使用filled字段
                        'cost': order.get('cost', 0),
                        'price': order.get('price', 0),
                        'status': status,
                        'id': order['id']
                    }
                    return filled_order

                # 订单被取消或拒绝
                if status in ['canceled', 'rejected']:
                    raise OrderMonitorError(f"{error_type}状态异常: {order}")

                # 检查部分成交情况
                if order['filled'] != last_filled:
                    last_filled = order['filled']
                    logger.warning(f"{self.exchange_name} {symbol} {error_type}部分成交: {order}")

                # 继续等待
                logger.info(f"{self.exchange_name} {symbol}, orderId: {order_id}, status: {status} 等待中...")
                await asyncio.sleep(check_interval)

            except OrderExecutionError:
                raise

            except Exception as e:
                logger.error(
                    f"{self.exchange_name} {symbol}, orderId: {order_id}, {error_type}, retry_count: {order_state.retry_count} 异常: {str(e)}")
                order_state.retry_count += 1

                if order_state.retry_count >= 3:
                    raise OrderMonitorError(f"{error_type}重试次数超限: {str(e)}")

                await asyncio.sleep(check_interval)

    async def watch_order_execution(
            self,
            symbol: str,
            order_id: str,
            is_spot: bool,
            check_interval: float = 1.0,
            max_retries: int = 3
    ) -> Dict:
        """监控订单执行状态（简化版，不需要OrderState对象）
        
        适用于简单的订单监控场景，如后台任务中监控订单状态
        
        Args:
            symbol: 交易对
            order_id: 订单ID
            is_spot: 是否是现货订单
            check_interval: 检查间隔(秒) 
            max_retries: 最大重试次数
            
        Returns:
            Dict: 订单信息
            
        Raises:
            OrderMonitorError: 监控异常
        """
        start_time = time.time()
        retry_count = 0
        last_filled = Decimal('0')
        order_type = "现货" if is_spot else "合约"

        while True:
            # 检查全局执行超时
            if (time.time() - start_time) * 1000 > self.global_execution_timeout_ms:
                logger.error(f"{self.exchange_name} {symbol} {order_type}订单执行全局超时: {order_id}")
                raise OrderGlobalTimeoutError(f"{order_type}订单全局执行超时")

            try:
                # 查询订单状态
                order = await self.exchange_client.fetch_order(
                    order_id=order_id,
                    symbol=symbol,
                    is_spot=is_spot
                )

                status = order['status']

                # 订单完全成交
                if status == 'closed':
                    logger.info(f"{self.exchange_name} {symbol} {order_type}订单成交: {order}")
                    return order

                # 订单被取消或拒绝
                if status in ['canceled', 'rejected']:
                    raise OrderMonitorError(f"{order_type}订单状态异常: {order}")

                # 检查部分成交情况
                if order['filled'] != last_filled:
                    last_filled = order['filled']
                    logger.info(f"{self.exchange_name} {symbol} {order_type}订单部分成交: {order['filled']}")

                # 继续等待
                logger.info(f"{self.exchange_name} {symbol} {order_type}订单状态: {status}, 等待中...")
                await asyncio.sleep(check_interval)

            except OrderExecutionError:
                raise

            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol} {order_type}订单监控异常: {str(e)}")
                retry_count += 1

                if retry_count >= max_retries:
                    raise OrderMonitorError(f"{order_type}订单监控重试次数超限: {str(e)}")

                await asyncio.sleep(check_interval)
