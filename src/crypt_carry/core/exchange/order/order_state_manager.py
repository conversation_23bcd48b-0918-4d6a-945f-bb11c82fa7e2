"""订单状态管理器

负责管理订单状态的创建、获取、更新和删除等操作
"""

import asyncio
import logging
from typing import Dict, Optional

from crypt_carry.core.exchange.order.constants import OrderStatus
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager

logger = logging.getLogger(__name__)


class OrderStateManager:
    """订单状态管理器
    
    负责管理所有活跃订单的状态，提供添加、获取、更新和删除订单状态的方法。
    同时提供订单锁机制，确保对同一个订单的操作是线程安全的。
    """

    def __init__(self, exchange_name: str):
        """初始化订单状态管理器
        
        Args:
            exchange_name: 交易所名称，用于日志记录
        """
        self.exchange_name = exchange_name
        # 活跃订单状态管理
        self._active_orders: Dict[str, OrderState] = {}
        # 监控任务
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        # 并发控制
        self._order_locks: Dict[str, asyncio.Lock] = {}
        # 待处理任务管理器
        self.pending_tasks_manager = PendingTasksManager.get_instance()

    def add_order_state(self, symbol: str, order_state: OrderState) -> None:
        """添加订单状态
        
        Args:
            symbol: 交易对
            order_state: 订单状态对象
        """
        self._active_orders[symbol] = order_state
        logger.info(f"{self.exchange_name} {symbol} 添加订单状态: {order_state.get_status_summary()}")

    def get_order_state(self, symbol: str) -> Optional[OrderState]:
        """获取订单状态
        
        Args:
            symbol: 交易对
            
        Returns:
            Optional[OrderState]: 订单状态对象，如果不存在则返回None
        """
        return self._active_orders.get(symbol)

    def remove_order_state(self, symbol: str) -> None:
        """移除订单状态
        
        Args:
            symbol: 交易对
        """
        if symbol in self._active_orders:
            self._active_orders.pop(symbol)
            logger.info(f"{self.exchange_name} {symbol} 移除订单状态")

    def update_order_status(self, symbol: str, status_field: str, new_status: OrderStatus) -> None:
        """更新订单状态
        
        Args:
            symbol: 交易对
            status_field: 状态字段，'futures_status'或'spot_status'
            new_status: 新状态
        """
        order_state = self._active_orders.get(symbol)
        if order_state:
            setattr(order_state, status_field, new_status)
            logger.info(f"{self.exchange_name} {symbol} 更新状态 {status_field}: {new_status}")

    def has_active_order(self, symbol: str) -> bool:
        """检查是否有活跃订单
        
        Args:
            symbol: 交易对
            
        Returns:
            bool: 如果有活跃订单则返回True，否则返回False
        """
        return symbol in self._active_orders

    def add_monitoring_task(self, symbol: str, task: asyncio.Task) -> None:
        """添加监控任务
        
        Args:
            symbol: 交易对
            task: 监控任务
        """
        self._monitoring_tasks[symbol] = task
        logger.debug(f"{self.exchange_name} {symbol} 添加监控任务")

    def get_monitoring_task(self, symbol: str) -> Optional[asyncio.Task]:
        """获取监控任务
        
        Args:
            symbol: 交易对
            
        Returns:
            Optional[asyncio.Task]: 监控任务，如果不存在则返回None
        """
        return self._monitoring_tasks.get(symbol)

    def remove_monitoring_task(self, symbol: str) -> None:
        """移除监控任务
        
        Args:
            symbol: 交易对
        """
        if symbol in self._monitoring_tasks:
            self._monitoring_tasks.pop(symbol)
            logger.debug(f"{self.exchange_name} {symbol} 移除监控任务")

    async def get_or_create_lock(self, symbol: str) -> asyncio.Lock:
        """获取或创建锁
        
        Args:
            symbol: 交易对
            
        Returns:
            asyncio.Lock: 锁对象
        """
        return self._order_locks.setdefault(symbol, asyncio.Lock())

    def remove_lock(self, symbol: str) -> None:
        """移除锁
        
        Args:
            symbol: 交易对
        """
        if symbol in self._order_locks:
            self._order_locks.pop(symbol)
            logger.debug(f"{self.exchange_name} {symbol} 移除锁")

    async def cleanup_symbol(self, symbol: str) -> None:
        """清理与交易对相关的所有资源（向后兼容方法）

        Args:
            symbol: 交易对
        """
        # 调用新的方法以保持兼容性
        return await self.cleanup_resources(symbol)

    async def cleanup_resources(self, symbol: str) -> None:
        """清理相关资源

        Args:
            symbol: 交易对
        """
        self.remove_order_state(symbol)
        self.remove_monitoring_task(symbol)
        self.remove_lock(symbol)
        # 清理锁定的资金，传入交易对以精确释放该交易对的资金
        await self.pending_tasks_manager.clear_locked_funds(self.exchange_name, symbol)
        logger.info(f"{self.exchange_name} {symbol} 资源清理完成")

    async def cancel_monitoring_task(self, symbol: str) -> None:
        """取消监控任务
        
        Args:
            symbol: 交易对
        """
        task = self._monitoring_tasks.get(symbol)
        if task and not task.done():
            try:
                task.cancel()
                await asyncio.wait_for(asyncio.shield(task), timeout=0.5)
                logger.debug(f"{self.exchange_name} {symbol} 成功取消监控任务")
            except (asyncio.CancelledError, asyncio.TimeoutError):
                # 正常的取消行为，不需要记录错误
                pass
            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol} 取消监控任务异常: {str(e)}")

    def get_all_active_symbols(self) -> list:
        """获取所有活跃订单的交易对
        
        Returns:
            list: 活跃订单的交易对列表
        """
        return list(self._active_orders.keys())

    def get_all_active_orders(self) -> Dict[str, OrderState]:
        """获取所有活跃订单状态
        
        Returns:
            Dict[str, OrderState]: 活跃订单状态字典
        """
        return self._active_orders.copy()
