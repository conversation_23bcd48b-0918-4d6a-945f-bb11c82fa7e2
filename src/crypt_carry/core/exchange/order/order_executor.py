import asyncio
import logging
from typing import Dict

from crypt_carry.constant.trade_type import TradeType
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.exchange.order.constants import OrderStatus
from crypt_carry.core.exchange.order.exceptions import (
    OrderExecutionError, MakerOrderCreationError,
    Taker<PERSON><PERSON>r<PERSON><PERSON>tionError, OrderMonitorError
)
from crypt_carry.core.exchange.order.order_cleaner import OrderCleaner
from crypt_carry.core.exchange.order.order_creator import OrderCreator
from crypt_carry.core.exchange.order.order_error_handler import OrderError<PERSON>and<PERSON>
from crypt_carry.core.exchange.order.order_monitor import OrderMonitor
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.core.exchange.order.order_state_manager import OrderStateManager
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)

class OrderExecutor:
    def __init__(self, exchange_client: ExchangeClient):
        """初始化
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client: ExchangeClient = exchange_client
        self.exchange_name = exchange_client.exchange_name

        self.exchange_config = ExchangeConfig(self.exchange_name)
        self.trading_config = self.exchange_config.trading_config

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()
        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 加载配置
        self._load_config()

        # 初始化订单创建器
        self.order_creator = OrderCreator(exchange_client)

        # 初始化订单状态管理器
        self.order_state_manager = OrderStateManager(exchange_name=self.exchange_name)

        self.order_monitor = OrderMonitor(
            exchange_client=self.exchange_client,
            exchange_name=self.exchange_name,
            global_execution_timeout_ms=self.global_execution_timeout_ms,
            order_timeout=self.order_timeout
        )

        # 初始化订单清理器
        self.order_cleaner = OrderCleaner(
            exchange_name=self.exchange_name,
            exchange_client=self.exchange_client,
            order_state_manager=self.order_state_manager,
            order_creator=self.order_creator
        )

        # 初始化订单错误处理器
        self.order_error_handler = OrderErrorHandler(
            exchange_client=exchange_client,
            order_state_manager=self.order_state_manager,
            order_creator=self.order_creator
        )

        # 初始化待处理任务管理器（单例模式）
        self.pending_tasks_manager = PendingTasksManager.get_instance()
        
    def _load_config(self):
        """加载配置
        
        从trading_config中加载订单相关配置,包括:
        - maker_check_interval: maker订单检查间隔(秒)
        - taker_check_interval: taker订单检查间隔(秒)
        - order_timeout: 订单超时时间(秒)
        - max_retries: 最大重试次数
        - retry_interval: 重试间隔(秒)
        - global_execution_timeout_ms: 全局订单执行超时时间（毫秒）
        """
        order_config = self.trading_config.get("ORDER", {})
        self.maker_check_interval = order_config.get("MAKER_CHECK_INTERVAL_MS", 10) / 1000
        self.taker_check_interval = order_config.get("TAKER_CHECK_INTERVAL_MS", 50) / 1000
        self.order_timeout = order_config.get("ORDER_TIMEOUT_MS", 300) / 1000
        self.max_retries = order_config.get("MAX_RETRIES", 3)
        self.retry_interval = order_config.get("RETRY_INTERVAL_MS", 1000) / 1000
        self.global_execution_timeout_ms = order_config.get("GLOBAL_EXECUTION_TIMEOUT_MS", 30000)

    async def execute_orders(self, position: Dict, on_order_failed: callable = None) -> bool:
        """执行订单 - 业务层
        
        只负责启动订单执行任务，立即返回
        
        Args:
            position: 持仓信息
                {
                    'symbol': symbol,
                    'direction': direction,
                    'trade_type': TradeType.CREATE,
                    'spot_amt': spot_amt,  # 现货可用余额,建仓时使用
                    'future_amt': future_amt,   # 合约可用余额,建仓时使用
                    'spot_quantity': float,  # 现货数量,平仓时使用
                    'future_quantity': float  # 合约数量，平仓时使用
                }
            on_order_failed: 订单失败回调函数
            
        Returns:
            bool: 是否成功启动订单执行任务
        """
        symbol = position['symbol']

        # 获取或创建symbol的锁
        lock = await self.order_state_manager.get_or_create_lock(symbol)

        async with lock:
            # 检查是否已有活跃订单
            if self.order_state_manager.has_active_order(symbol):
                logger.warning(f"{self.exchange_name} {symbol} 已有活跃订单正在执行")
                return True

            try:
                # 创建并启动异步任务
                task = asyncio.create_task(
                    self._execute_order_flow(position, on_order_failed)
                )

                # 使用包装器转换异步方法为同步回调
                def task_done_wrapper(t):
                    # 创建新的异步任务来执行异步回调
                    asyncio.create_task(self._on_task_done(symbol, t))

                # 添加任务完成回调
                task.add_done_callback(task_done_wrapper)
                # 保存任务引用
                self.order_state_manager.add_monitoring_task(symbol, task)
                return True

            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol} 启动订单执行任务失败: {str(e)}")
                # 发生异常 调用失败回调
                if on_order_failed:
                    try:
                        await on_order_failed(symbol, position)
                    except Exception as callback_error:
                        logger.error(f"{self.exchange_name} {symbol} 执行订单失败回调异常: {str(callback_error)}")
                        self.ding_talk_manager.send_message(
                            f"{self.exchange_name} {symbol} 执行订单失败回调异常: {str(callback_error)}",
                            prefix=self.exchange_name
                        )

    async def _execute_order_flow(self, position: Dict, on_order_failed: callable = None):
        """执行订单流程 - 执行层
        
        异步执行完整的订单流程
        
        Args:
            position: 持仓信息
            on_order_failed: 订单失败回调函数
            
        Raises:
            OrderExecutionError: 订单执行错误
        """
        symbol = position['symbol']
        trade_type = position.get('trade_type', TradeType.CREATE)  # 默认为开仓
        order_state = None

        # 平仓时检查是否有合约持仓需要平仓
        future_quantity = position.get('future_quantity', 0)
        spot_quantity = position.get('spot_quantity', 0)

        filled_futures = None  # 初始化为None以避免变量未定义错误
        filled_spot = None
        
        try:
            # 1. 创建合约maker订单 (平仓时先检查是否有合约持仓)
            futures_order = None
            if trade_type != TradeType.CLOSE or (trade_type == TradeType.CLOSE and abs(future_quantity) >= 1e-8):
                futures_order = await self.order_creator.create_futures_limit_order(symbol, position)
                if not futures_order or futures_order.get('status') == 'rejected':
                    raise MakerOrderCreationError(f"创建合约maker订单失败: {futures_order}")
            else:
                logger.warning(f"{self.exchange_name} {symbol} 没有合约持仓需要平仓: {future_quantity}")
                # 如果没有现货持仓需要平仓并且没有合约持仓，则提前返回
                if trade_type == TradeType.CLOSE and abs(spot_quantity) < 1e-8:
                    logger.info(f"{self.exchange_name} {symbol} 没有持仓需要平仓，跳过订单执行")
                    return
            # 2. 创建订单状态 - 只有在有合约订单时创建
            if futures_order:
                order_state = OrderState(
                    symbol=symbol,
                    futures_order_id=futures_order['id'],
                    spot_order_id=None,
                    position=position,
                    futures_fill_quantity=0,
                    spot_fill_quantity=0
                )
                self.order_state_manager.add_order_state(symbol, order_state)
                order_state.record_execution_info('futures_order_created', futures_order)

                # 3. 开始监控maker订单
                order_state.start_futures_order()
                filled_futures = await self.order_monitor.monitor_order(
                    symbol=symbol,
                    order_id=futures_order['id'],
                    is_spot=False,
                    order_state=order_state,
                    check_interval=self.maker_check_interval,
                    error_type='maker'
                )
                if not filled_futures:
                    raise OrderMonitorError("合约maker订单未能成交")

                # 4. 更新订单状态
                order_state.futures_fill_quantity = filled_futures.get('quantity', 0)  # 使用filled字段表示实际成交数量
                order_state.futures_cost = filled_futures.get('cost', 0)
                order_state.futures_status = OrderStatus.FILLED
                order_state.record_execution_info('futures_order_filled', filled_futures)
            else:
                # 如果没有合约订单（可能是因为平仓时没有合约持仓），则创建一个基本状态
                order_state = OrderState(
                    symbol=symbol,
                    futures_order_id=None,
                    spot_order_id=None,
                    position=position,
                    futures_fill_quantity=0,
                    spot_fill_quantity=0
                )
                self.order_state_manager.add_order_state(symbol, order_state)

            # 5. 执行现货订单（创建并监控）- 平仓场景下先检查是否有现货持仓
            filled_spot = None
            try:
                # 平仓场景下检查是否有现货持仓需要平仓
                if trade_type != TradeType.CLOSE or (trade_type == TradeType.CLOSE and abs(spot_quantity) >= 1e-8):
                    filled_spot = await self._create_and_monitor_spot_order(order_state, is_emergency=False)
                    if not filled_spot:
                        logger.error(f"{self.exchange_name} {symbol} 现货订单创建或监控失败")
                        # 记录错误信息
                        order_state.record_error(TakerOrderCreationError("创建现货订单失败"), "现货订单创建失败")
                        # 处理合约订单平仓 - 但仅在有合约订单的情况下
                        if futures_order:
                            await self.order_error_handler.handle_taker_failure(symbol)
                        raise TakerOrderCreationError(f"创建现货订单失败")
                else:
                    logger.warning(f"{self.exchange_name} {symbol} 没有现货持仓需要平仓: {spot_quantity}")
            except Exception as e:
                logger.error(f"{self.exchange_name} {symbol}, orderId: {order_id}, 现货订单处理异常: {str(e)}")
                # 记录错误信息
                order_state.record_error(e, "现货订单处理异常")
                # 处理合约订单平仓
                await self.order_error_handler.handle_taker_failure(symbol)
                raise TakerOrderCreationError(f"现货订单处理异常: {str(e)}")

            # 8. 计算并验证订单价值匹配 - 仅当两者都存在时进行比较
            if filled_futures is not None and filled_spot is not None:
                futures_value = filled_futures.get('cost', 0)
                spot_value = filled_spot.get('cost', 0)
                logger.info(
                    f"{self.exchange_name} {symbol} 现货订单实际价值:{spot_value}, 合约订单名义价值:{futures_value}")

                # 使用相对差异（比例）来判断价值是否匹配
                # 避免除以零的情况
                if spot_value > 0 and futures_value > 0:
                    value_ratio_diff = abs(futures_value - spot_value) / min(futures_value, spot_value)
                    if value_ratio_diff > 0.01:  # 相对差异大于1%认为不匹配
                        value_diff = abs(futures_value - spot_value)
                        msg = (
                            f"{self.exchange_name} {symbol} 订单价值不匹配:\n"
                            f"futures_value: {futures_value} USDT (名义价)\n"
                            f"spot_value: {spot_value} USDT\n"
                            f"绝对差值: {value_diff:.6f} USDT\n"
                            f"相对差异: {value_ratio_diff:.2%}\n"
                            f"futures原始数据: {filled_futures}\n"
                            f"spot原始数据: {filled_spot}"
                        )
                        logger.warning(msg)
                        self.ding_talk_manager.send_message(msg, prefix=self.exchange_name)
            elif filled_futures is not None:
                logger.info(
                    f"{self.exchange_name} {symbol} 只有合约订单成交，价值: {filled_futures.get('cost', 0)} USDT")
            elif filled_spot is not None:
                logger.info(f"{self.exchange_name} {symbol} 只有现货订单成交，价值: {filled_spot.get('cost', 0)} USDT")
            else:
                # 平仓情况下可能两者都不参与交易
                if trade_type == TradeType.CLOSE:
                    logger.info(f"{self.exchange_name} {symbol} 平仓完成，没有订单需要执行")

            logger.info(f"{self.exchange_name} {symbol} 订单执行完成")

        except OrderExecutionError as e:
            await self.order_error_handler.handle_exception(
                symbol=symbol,
                exception=e,
                cleanup_orders_callback=self.order_cleaner.cleanup_orders,
                create_emergency_spot_order_callback=self._create_emergency_spot_order
            )
            # 构建错误信息
            error_msg = (
                f"{self.exchange_name} {symbol}:\n"
                f"错误信息: {str(e)}\n"
                f"订单状态: {order_state.get_status_summary() if order_state else '无'}"
            )
            logger.error(error_msg)
            self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)
            # raise

        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 订单执行异常: {str(e)}")
            if order_state:
                order_state.record_error(e, "未预期异常")
            raise OrderExecutionError(f"订单执行异常: {str(e)}")

        finally:
            # 确保在任何情况下都能清理资源
            if order_state and order_state.is_completed():
                logger.info(f"{self.exchange_name} {symbol} is_completed 订单执行完成，开始清理资源")
                await self.order_cleaner.cleanup_symbol(symbol)

    async def _create_and_monitor_spot_order(self, order_state: OrderState, is_emergency: bool = False):
        """创建并监控现货订单
        
        统一处理现货订单的创建和监控逻辑，无论是常规流程还是紧急修复流程
        
        Args:
            order_state: 订单状态
            is_emergency: 是否为紧急模式（合约已成交但现货未执行）
            
        Raises:
            TakerOrderCreationError: 创建现货订单失败
            OrderMonitorError: 监控现货订单失败
            
        Returns:
            Dict: 成交的现货订单信息
        """
        symbol = order_state.symbol
        context_prefix = "紧急" if is_emergency else ""

        if is_emergency:
            logger.warning(f"{self.exchange_name} {symbol} 开始{context_prefix}创建现货订单以完成套保")
        else:
            logger.info(f"{self.exchange_name} {symbol} 开始创建现货订单")

        # 开始现货订单创建流程
        order_state.start_spot_order()
        try:
            spot_order = await self.order_creator.create_spot_taker_order(order_state)
            if not spot_order or spot_order.get('status') == 'rejected':
                error_msg = f"{context_prefix}创建现货订单失败: {spot_order}"
                logger.error(f"{self.exchange_name} {symbol} {error_msg}")
                raise TakerOrderCreationError(error_msg)
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} {context_prefix}创建现货订单异常: {str(e)}")
            raise TakerOrderCreationError(f"{context_prefix}创建现货订单异常: {str(e)}")

        # 更新现货订单ID
        order_state.spot_order_id = spot_order['id']
        logger.info(f"{self.exchange_name} {symbol} {context_prefix}创建现货订单成功: {spot_order['id']}")
        order_state.record_execution_info('spot_order_created', spot_order)

        # 监控现货订单
        spot_filled_order = await self.order_monitor.monitor_order(
            symbol=symbol,
            order_id=order_state.spot_order_id,
            is_spot=True,
            order_state=order_state,
            check_interval=self.taker_check_interval,
            error_type=f"{context_prefix}现货订单"
        )

        # 更新现货订单成交信息
        if spot_filled_order:
            order_state.spot_fill_quantity = spot_filled_order.get('quantity', 0)
            order_state.spot_cost = spot_filled_order.get('cost', 0)
            order_state.spot_status = OrderStatus.FILLED
            logger.info(f"{self.exchange_name} {symbol} {context_prefix}现货订单成交: {spot_filled_order}")
            order_state.record_execution_info('spot_order_filled', spot_filled_order)

            # 发送成功通知（仅在紧急模式下）
            if is_emergency:
                self.ding_talk_manager.send_message(
                    f"✅ {self.exchange_name} {symbol} 自动修复套保成功！\n" +
                    f"合约订单ID: {order_state.futures_order_id}\n" +
                    f"现货订单ID: {order_state.spot_order_id}\n" +
                    f"合约成交量: {order_state.futures_fill_quantity}\n" +
                    f"现货成交量: {order_state.spot_fill_quantity}",
                    prefix=self.exchange_name
                )
            return spot_filled_order
        return None

    async def _on_task_done(self, symbol: str, task: asyncio.Task):
        """任务完成回调
        
        处理订单执行任务的完成状态，将异常处理委托给错误处理器。
        
        Args:
            symbol: 交易对
            task: 完成的任务
        """
        try:
            # 获取任务结果，如果有异常会在这里抛出
            task.result()

        except Exception as e:
            # 使用错误处理器处理所有异常
            await self.order_error_handler.handle_exception(
                symbol=symbol,
                exception=e,
                cleanup_orders_callback=self.order_cleaner.cleanup_orders
            )
        finally:
            # 清理任务引用和锁
            if self.order_state_manager.get_monitoring_task(symbol) == task:
                await self.order_state_manager.cleanup_symbol(symbol)

    async def _create_emergency_spot_order(self, order_state: OrderState, is_emergency: bool = True) -> Dict:
        """创建紧急现货订单
        
        在合约已成交但现货未成交的情况下，创建紧急现货订单完成对冲。
        这个方法会被传递给OrderErrorHandler，在需要时调用。
        
        Args:
            order_state: 订单状态
            is_emergency: 是否为紧急模式，默认为True
            
        Returns:
            Dict: 成交的现货订单信息
        """
        logger.warning(f"{self.exchange_name} {order_state.symbol} 开始创建紧急现货订单")
        
        try:
            # 调用已有的现货订单创建和监控方法
            return await self._create_and_monitor_spot_order(order_state, is_emergency=True)
        except Exception as e:
            logger.error(f"{self.exchange_name} {order_state.symbol} 创建紧急现货订单失败: {str(e)}")
            # 发送通知
            self.ding_talk_manager.send_message(
                f"\u26a0\ufe0f {self.exchange_name} {order_state.symbol} 紧急现货订单创建失败\n" +
                f"错误信息: {str(e)}\n" +
                f"合约状态: {order_state.futures_status}, 成交量: {order_state.futures_fill_quantity}",
                prefix=self.exchange_name
            )
            # 返回None表示失败
            return None
