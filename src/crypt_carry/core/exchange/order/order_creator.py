import logging
from typing import Dict, Optional

from crypt_carry.constant.direction_type import DirectionType
from crypt_carry.constant.trade_type import TradeType
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.exchange.order.exceptions import MakerOrderCreationError, TakerOrderCreationError
from crypt_carry.core.exchange.order.order_helper import _calculate_target_quantity
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.utils.retry import async_retry

logger = logging.getLogger(__name__)


class OrderCreator:
    """订单创建器
    
    负责创建各种类型的订单，包括合约限价单、现货市价单和合约市价单。
    采用统一的参数处理和错误处理机制。
    """

    def __init__(self, exchange_client: ExchangeClient):
        """初始化订单创建器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client: ExchangeClient = exchange_client
        self.exchange_name = exchange_client.exchange_name

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

    @async_retry(
        max_retries=3,  # 最大重试次数
        initial_delay=1.0,  # 初始重试延迟
        backoff_factor=2.0,  # 退避因子
        timeout=10.0,  # 执行超时时间
        error_type="创建合约订单",
        retryable_exceptions=(MakerOrderCreationError,)
    )
    async def create_futures_limit_order(self, symbol: str, position: Dict) -> Optional[Dict]:
        """创建合约限价单
        
        Args:
            symbol: 交易对
            position: 持仓信息
                {
                    'symbol': str,
                    'direction': str,
                    'trade_type': str,
                    'spot_amt': float,
                    'future_amt': float,
                    'spot_quantity': float,
                    'future_quantity': float
                }
            
        Returns:
            Optional[Dict]: 订单信息
            
        Raises:
            MakerOrderCreationError: 创建异常
        """
        # 获取交易类型和其他订单参数
        trade_type = position.get('trade_type', TradeType.CREATE)  # 默认为开仓
        reduce_only = trade_type == TradeType.CLOSE
        futures_price = self.market_data_manager.get_market_data(self.exchange_name, symbol, is_spot=False)
        target_quantity = None

        # 根据交易类型计算数量
        if trade_type == TradeType.CLOSE:
            target_quantity = position['future_quantity']
        else:
            # 开仓时根据可用余额计算
            available_amt = position['future_amt']
            target_quantity = _calculate_target_quantity(symbol, available_amt, futures_price, is_spot=False)
        # 获取position_side和订单方向
        if position['direction'] == DirectionType.LONG_SPOT_SHORT_FUTURES:
            position_side = 'short'  # 合约做空
            side = 'sell' if trade_type == TradeType.CREATE else 'buy'  # 开仓做空,平仓做多
        else:
            position_side = 'long'  # 合约做多
            side = 'buy' if trade_type == TradeType.CREATE else 'sell'  # 开仓做多,平仓做空

        logger.info(
            f"{self.exchange_name} {symbol} 创建合约{'平仓' if reduce_only else '开仓'}订单参数: "
            f"side={side}, posSide={position_side}, quantity={target_quantity}, price={futures_price}")
        try:
            order = await self.exchange_client.create_order(
                symbol=symbol,
                order_type='limit',  # 合约市场使用限价单
                side=side,
                quantity=target_quantity,
                price=futures_price,
                is_spot=False,  # 合约订单
                position_side=position_side,  # 设置仓位方向
                reduce_only=reduce_only  # 平仓标识
            )
            logger.debug(f"{self.exchange_name} {symbol} 创建合约{'平仓' if reduce_only else '开仓'}订单成功: {order}")
            return order
        except Exception as e:
            error_msg = f"{self.exchange_name} {symbol} 创建合约{'平仓' if trade_type == TradeType.CLOSE else '开仓'}订单异常: {str(e)}"
            logger.error(error_msg)
            raise MakerOrderCreationError(error_msg)

    @async_retry(
        max_retries=3,  # 最大重试次数
        initial_delay=1.0,  # 初始重试延迟
        backoff_factor=2.0,  # 退避因子
        timeout=10.0,  # 执行超时时间
        error_type="创建现货订单",
        retryable_exceptions=(TakerOrderCreationError,)
    )
    async def create_spot_taker_order(self, order_state: OrderState) -> Dict:
        """创建现货taker订单
        
        Args:
            order_state: 订单状态
            
        Returns:
            Dict: 订单信息
            
        Raises:
            TakerOrderCreationError: 创建异常
        """
        symbol = order_state.symbol
        position = order_state.position
        futures_cost = order_state.futures_cost
        trade_type = position.get('trade_type', TradeType.CREATE)  # 默认为开仓
        target_quantity = None

        # 根据交易类型计算数量
        if trade_type == TradeType.CLOSE:
            target_quantity = position['spot_quantity']
        else:
            futures_price = self.market_data_manager.get_market_data(self.exchange_name, symbol, is_spot=False)
            target_quantity = _calculate_target_quantity(symbol, futures_cost, futures_price, is_spot=True)
        # 根据方向和trade_type决定买卖方向
        if position['direction'] == DirectionType.LONG_SPOT_SHORT_FUTURES:
            side = 'buy' if trade_type == TradeType.CREATE else 'sell'  # 开仓做多,平仓做空
        else:
            side = 'sell' if trade_type == TradeType.CREATE else 'buy'  # 开仓做空,平仓做多

        logger.info(
            f"{self.exchange_name} {symbol} 创建现货{'平仓' if trade_type == TradeType.CLOSE else '开仓'}订单参数: "
            f"side={side}, quantity={target_quantity}, order_type=market")
        try:
            taker_order = await self.exchange_client.create_order(
                symbol=symbol,
                order_type='market',  # 现货市场使用市价单
                side=side,
                quantity=target_quantity,
                # price=spot_price,  # 市价单可以不传price
                is_spot=True
            )
            logger.info(
                f"{self.exchange_name} {symbol} 创建现货{'平仓' if trade_type == TradeType.CLOSE else '开仓'}订单成功: {taker_order}")

            if not taker_order or taker_order.get('status') == 'rejected':
                raise TakerOrderCreationError(
                    f"创建现货{'平仓' if trade_type == TradeType.CLOSE else '开仓'}订单失败: {taker_order}")
            return taker_order
        except Exception as e:
            error_msg = f"{self.exchange_name} {symbol} 创建现货{'平仓' if trade_type == TradeType.CLOSE else '开仓'}订单异常: {str(e)}"
            logger.error(error_msg)
            raise TakerOrderCreationError(error_msg)

    @async_retry(
        max_retries=3,  # 最大重试次数
        initial_delay=0.5,  # 初始重试延迟
        backoff_factor=2.0,  # 退避因子
        timeout=5.0,  # 执行超时时间
        error_type="创建合约市价单"
    )
    async def create_futures_market_order(self, symbol: str, side: str, quantity: float,
                                          reduce_only: bool = False) -> Dict:
        """创建合约市价单，带重试机制
        
        Args:
            symbol: 交易对
            side: 方向 (buy/sell)
            quantity: 数量
            reduce_only: 是否仅平仓
        
        Returns:
            dict: 订单创建结果
        """
        try:
            logger.info(
                f"{self.exchange_name} {symbol} 创建合约市价平仓订单: side={side}, quantity={quantity}, reduce_only={reduce_only}")
            order = await self.exchange_client.create_order(
                symbol=symbol,
                order_type='market',  # 使用市价单快速平仓
                side=side,
                quantity=quantity,
                is_spot=False,  # 合约订单
                reduce_only=reduce_only  # 平仓标识
            )
            logger.info(f"{self.exchange_name} {symbol} 创建合约市价平仓订单成功: {order['id']}")
            return order
        except Exception as e:
            error_msg = f"{self.exchange_name} {symbol} 创建合约市价平仓订单异常: {str(e)}"
            logger.error(error_msg)
            # 记录错误但不抛出异常，让重试机制处理
            return None

    @async_retry(
        max_retries=3,
        initial_delay=0.5,
        backoff_factor=2.0,
        timeout=5.0,
        error_type="取消订单"
    )
    async def cancel_order_with_retry(self, order_id: str, symbol: str, is_spot: bool) -> Dict:
        """取消订单，带重试机制
        
        Args:
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否为现货订单
            
        Returns:
            Dict: 取消结果
        """
        return await self.exchange_client.cancel_order(order_id, symbol, is_spot)

    @async_retry(
        max_retries=3,
        initial_delay=0.5,
        backoff_factor=2.0,
        timeout=5.0,
        error_type="查询订单"
    )
    async def fetch_open_orders_with_retry(self, symbol: str, is_spot: bool) -> list[Dict]:
        """获取未完成订单列表，带重试机制
        
        Args:
            symbol: 交易对
            is_spot: 是否为现货订单
            
        Returns:
            List[Dict]: 未完成订单列表
        """
        return await self.exchange_client.fetch_open_orders(symbol, is_spot)
