import logging
from typing import Dict, Optional, List

from crypt_carry.config.config_loader import ConfigLoader

# 创建logger，使用模块名作为logger名称
logger = logging.getLogger(__name__)

class ExchangeConfig:
    """交易所配置管理"""

    def __init__(self, exchange_name: str = None):
        self.exchange_name = exchange_name
        self.base_config = ConfigLoader.get_base_config()
        self.trading_config = ConfigLoader.get_trading_config()

        # 只有指定了交易所名称才加载交易所特定配置
        if exchange_name:
            self._load_config()
        else:
            # 全局配置初始化
            self.api_config = {}
            self.api_key = ''
            self.api_secret = ''
            self.proxy_enabled = self.base_config.get('PROXY', {}).get('enabled', False)
            self.http_proxy = self.base_config.get('PROXY', {}).get('http') if self.proxy_enabled else None
            self.ws_proxy = self.base_config.get('PROXY', {}).get('ws') if self.proxy_enabled else None
            self.timeout = self.base_config.get('HTTP_CONFIG', {}).get('TIMEOUT', 30000)
            self.trading_symbols = []
            self.trading_quotes = []
            self.trading_detect_exclude = []
            self.paths = self.base_config.get('PATHS', {})
            logger.debug("全局配置加载完成")

    def _load_config(self):
        """加载交易所配置"""
        # 统一大写处理交易所名称
        exchange_name_upper = self.exchange_name.upper()
        
        # API配置
        self.api_config = self.base_config['API_KEYS'].get(exchange_name_upper, {})
        self.api_key = self.api_config.get('KEY', '')
        self.api_secret = self.api_config.get('SECRET', '')

        # 代理配置
        proxy_config = self.base_config.get('PROXY', {})
        self.proxy_enabled = proxy_config.get('enabled', False)
        self.http_proxy = proxy_config.get('http') if self.proxy_enabled else None
        self.ws_proxy = proxy_config.get('ws') if self.proxy_enabled else None

        # HTTP超时配置（毫秒）
        http_config = self.base_config.get('HTTP_CONFIG', {})
        self.timeout = http_config.get('TIMEOUT', 30000)

        # 交易对配置
        exchange_pairs = self.trading_config.get('EXCHANGE_PAIRS', {}).get(exchange_name_upper, {})
        self.trading_symbols = exchange_pairs.get('symbol', [])
        self.trading_quotes = exchange_pairs.get('quote', [])
        self.trading_detect_exclude = exchange_pairs.get('detect_exclude', [])
        self.pair_from = self.get_exchange_config().get('PAIR_FROM', 'CONFIG')

        # 路径配置
        self.paths = self.base_config.get('PATHS', {})

        # 打印配置信息
        logger.debug(f"{self.exchange_name} 配置加载完成:")
        logger.debug(f"- API配置: {self.api_config}")
        logger.debug(f"- 代理配置: {proxy_config}")
        logger.debug(f"- 交易对配置: {exchange_pairs}")

    def get_api_credentials(self) -> Dict[str, str]:
        """获取API凭证"""
        return {
            'api_key': self.api_key,
            'api_secret': self.api_secret
        }

    def get_proxy_settings(self) -> Dict[str, Optional[str]]:
        """获取代理设置"""
        return {
            'http_proxy': self.http_proxy,
            'ws_proxy': self.ws_proxy
        }

    def is_proxy_enabled(self) -> bool:
        """检查是否启用代理"""
        return self.proxy_enabled

    def reload_config(self, force: bool = False):
        """重新加载配置
        
        Args:
            force: 是否强制重新加载，即使配置实例已存在
        """
        # 使用 ConfigLoader 的 reload_config 方法重新加载所有配置
        ConfigLoader.reload_config(force=force)
        # 重新获取配置
        self.base_config = ConfigLoader.get_base_config()
        self.trading_config = ConfigLoader.get_trading_config()
        # 重新加载交易所特定配置
        if self.exchange_name:
            self._load_config()

    def get_path(self, path_key: str) -> str:
        """获取配置的路径
        
        Args:
            path_key: 路径配置键名，如 'FUNDING_RATES', 'MARKET_DATA' 等
            
        Returns:
            str: 配置的路径值
        """
        return self.paths.get(path_key, '')

    def get_strategy_config(self) -> Dict:
        """获取交易所策略配置
        
        Returns:
            Dict: 策略配置
        """
        exchange_config = self.trading_config.get('EXCHANGE_CONFIG', {}).get(self.exchange_name.upper(), {})
        return exchange_config.get('STRATEGY', {})

    def get_strategy_value(self, key: str, default=None):
        """获取策略配置中的具体值
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        strategy_config = self.get_strategy_config()
        return strategy_config.get(key, default)

    def get_order_config(self) -> Dict:
        """获取订单配置
        
        Returns:
            Dict: 订单配置
        """
        return self.trading_config.get('ORDER', {})

    def get_order_value(self, key: str, default=None):
        """获取订单配置中的具体值
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        order_config = self.get_order_config()
        return order_config.get(key, default)

    def get_trading_symbols(self) -> List[str]:
        """获取交易对列表
        
        Returns:
            List[str]: 交易对列表
        """
        return self.trading_symbols

    def get_trading_quotes(self) -> List[str]:
        """获取计价币种列表
        
        Returns:
            List[str]: 计价币种列表
        """
        return self.trading_quotes

    def get_config_value(self, section: str, key: str, default=None):
        """获取配置中的值，支持多级路径
        
        Args:
            section: 配置区块，如 'EXCHANGE_CONFIG', 'ORDER' 等
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        if section == 'BASE':
            return self.base_config.get(key, default)
        elif section == 'TRADING':
            return self.trading_config.get(key, default)
        else:
            # 支持多级路径如 TRADING.ORDER.COOLDOWN
            if section == 'TRADING':
                config = self.trading_config
            else:
                config = self.base_config

            keys = key.split('.')
            for k in keys:
                if isinstance(config, dict):
                    config = config.get(k, {})
                else:
                    return default

            return config if config != {} else default

    def update_trading_config(self, updates: Dict) -> None:
        """更新交易配置并保存到文件
        
        Args:
            updates: 要更新的配置字典，支持多层级更新
                例如: {'EXCHANGE_CONFIG': {'OKX': {'STRATEGY': {'FORCE_POSITION_CHECK': False}}}}
        """
        # 调用ConfigLoader更新配置
        from crypt_carry.config.config_loader import ConfigLoader
        ConfigLoader.update_trading_config(updates)

        # 重新加载配置
        self.reload_config(force=True)

    def set_strategy_value(self, key: str, value) -> None:
        """设置策略配置值并保存
        
        Args:
            key: 配置键名
            value: 配置值
        """
        # 创建更新字典
        updates = {
            "EXCHANGE_CONFIG": {
                self.exchange_name.upper(): {
                    "STRATEGY": {
                        key: value
                    }
                }
            }
        }

        # 调用更新方法保存到文件
        self.update_trading_config(updates)

        # 更新内存中的策略配置
        logger.debug(f"已更新策略配置 [{key}={value}]")

    def get_exchanges(self) -> List[str]:
        """获取配置中的交易所列表
        
        Returns:
            List[str]: 交易所列表
        """
        return self.trading_config.get('EXCHANGES', [])

    def is_exchange_enabled(self, exchange_name: str) -> bool:
        """检查交易所是否启用
        
        Args:
            exchange_name: 交易所名称
            
        Returns:
            bool: 是否启用
        """
        exchange_name = exchange_name.upper()
        exchange_config = self.trading_config.get('EXCHANGE_CONFIG', {}).get(exchange_name, {})
        return exchange_config.get('ENABLED', False)

    def get_exchange_config(self) -> Dict:
        """获取交易所完整配置
        
        Returns:
            Dict: 交易所配置
        """
        return self.trading_config.get('EXCHANGE_CONFIG', {}).get(self.exchange_name.upper(), {})
