"""交易所客户端基类"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig

logger = logging.getLogger(__name__)


class ExchangeClient(ABC):
    """交易所客户端基类"""

    def __init__(self, config: ExchangeConfig):
        """初始化交易所客户端
        
        Args:
            config: 交易所配置
        """
        self.config = config
        self.exchange_name = config.exchange_name
        self.websocket = None
        self.reconnect_delay = 5
        self._running = False

        # 初始化交易对列表
        self.quotes: List[str] = []
        self.base_pair: Optional[str] = []
        self.perpetual_pairs: List[str] = []
        self.spot_pairs: List[str] = []
        self.all_pair: List[str] = []
        self.detect_exclude_pairs: List[str] = []
        


    @abstractmethod
    async def connect(self):
        """建立WebSocket连接"""
        pass

    @abstractmethod
    async def subscribe_market_data(self):
        """订阅市场数据
        
        Args:
            symbols: 交易对列表
        """
        pass

    @abstractmethod
    async def handle_message(self, message, message_type: str = None):
        """处理 WebSocket 消息
        
        Args:
            message: WebSocket消息
            message_type: 消息类型
        """
        pass



    async def listen(self):
        """监听WebSocket消息"""
        try:
            if not self.websocket:
                raise RuntimeError(f"{self.exchange_name} WebSocket未连接")
            message = await self.websocket.receive()
            return message
        except Exception as e:
            logger.error(f"{self.exchange_name} 监听消息错误: {str(e)}")
            raise

    async def ping(self):
        """发送ping消息保持连接"""
        logger.debug(f"{self.exchange_name} 发送ping消息")
        # 具体的ping实现由子类完成
        pass

    async def close(self):
        """关闭WebSocket连接"""
        self._running = False
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
            logger.info(f"{self.exchange_name} WebSocket连接已关闭")

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.websocket is not None and not self.websocket.closed

    async def ensure_connection(self):
        """确保WebSocket保持连接"""
        self._running = True
        while self._running:
            try:
                if not self.is_connected():
                    await self.connect()
                    logger.info(f"{self.exchange_name} WebSocket连接成功")
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"{self.exchange_name} WebSocket连接错误: {str(e)}")
                await asyncio.sleep(self.reconnect_delay)

    @abstractmethod
    async def fetch_spot_balance(self) -> Dict[str, float]:
        """获取现货账户余额
        
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        pass

    @abstractmethod
    async def fetch_futures_balance(self) -> Dict[str, float]:
        """获取合约账户余额
        
        Returns:
            Dict[str, float]: 币种和余额的映射
        """
        pass

    @abstractmethod
    async def start(self) -> bool:
        """启动客户端
        
        Returns:
            bool: 是否成功启动
        """
        pass

    @abstractmethod
    async def stop(self):
        """停止客户端"""
        pass

    @abstractmethod
    async def create_order(
            self,
            symbol: str,
            order_type: str,
            side: str,
            quantity: float,
            price: Optional[float] = None,
            is_spot: bool = True,
            position_side: Optional[str] = None,  # 'LONG' or 'SHORT'
            reduce_only: bool = False,  # 是否只减仓
    ) -> Dict:
        """创建订单
        
        Args:
            symbol: 交易对
            order_type: 订单类型 (market/limit)
            side: 交易方向 (buy/sell)
            quantity: 数量
            price: 价格（市价单可选）
            is_spot: 是否是现货订单
            position_side: 持仓方向，仅用于合约订单 ('LONG'/'SHORT')
            reduce_only: 是否只减仓，仅用于合约订单
            notional: 仅用于合约订单，表示仓位大小（仓位大小为0时，表示平仓）
            
        Returns:
            Dict: 订单信息，包含：
                {
                    'id': str,  # 订单ID
                    'status': str,  # 订单状态
                    'symbol': str,  # 交易对
                    'type': str,  # 订单类型
                    'side': str,  # 交易方向
                    'price': float,  # 价格
                    'amount': float,  # 数量
                    'filled': float,  # 已成交数量
                    'remaining': float,  # 剩余数量
                    'timestamp': int,  # 创建时间戳
                    'fee': {  # 手续费
                        'cost': float,
                        'currency': str
                    }
                }
        """
        pass

    @abstractmethod
    async def fetch_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """查询订单信息
        
        Args:
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 订单信息
        """
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """取消订单
        
        Args:
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否是现货订单
            
        Returns:
            Dict: 取消结果
        """
        pass

    @abstractmethod
    async def fetch_open_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询当前未完成的订单
        
        Args:
            symbol: 交易对（可选，如果不指定则查询所有交易对）
            is_spot: 是否是现货订单
            
        Returns:
            List[Dict]: 订单列表
        """
        pass

    @abstractmethod
    async def fetch_closed_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询历史订单
        
        Args:
            symbol: 交易对（可选，如果不指定则查询所有交易对）
            is_spot: 是否是现货订单
            
        Returns:
            List[Dict]: 订单列表
        """
        pass

    @abstractmethod
    async def fetch_positions_by_ws(self) -> List[Dict]:
        pass

    @abstractmethod
    async def fetch_positions_by_rest(self) -> List[Dict]:
        pass

    @abstractmethod
    def get_standard_symbol(self, symbol: str) -> str:
        """将交易所特定的交易对格式转换为标准化格式
        Args:
            symbol: 任意格式的交易对，可能是交易所特定格式或内部格式
            
        Returns:
            str: 标准化格式的交易对，如 BTC/USDT
        """
        pass

    @abstractmethod
    async def reload_detect_exclude_pairs(self):
        pass
