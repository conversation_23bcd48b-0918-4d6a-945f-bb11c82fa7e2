"""资金费率管理器"""
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from util.formatter import format_funding_rate
from .funding_rate_cache import FundingRateCache
from .funding_rate_storage import FundingRateStorage

logger = logging.getLogger(__name__)


class FundingRateManager:
    """
    资金费率管理器(负责业务逻辑)
        整合存储和缓存功能
        提供统一的更新接口
        定期保存数据
    """

    def __init__(self, config: ExchangeConfig):
        """初始化资金费率管理器
        
        Args:
            config: 交易所配置
        """
        self.exchange_name = config.exchange_name
        self.config = config
        self.storage = FundingRateStorage(config, self.get_funding_rates_dir())
        self.cache = FundingRateCache()

    def get_funding_rates_dir(self) -> Path:
        """获取资金费率文件目录
        
        Returns:
            Path: 资金费率文件目录
        """
        funding_rates_dir = Path("carry/" + self.config.get_path('FUNDING_RATES'))
        exchange_dir = funding_rates_dir / self.exchange_name.lower()
        return exchange_dir

    async def init_funding_rates(self, perpetual_pairs: List[str]):
        """
        初始化资金费率数据, 从历史数据中加载
        
        Args:
            perpetual_pairs: 永续合约交易对列表
        """
        # 分批处理交易对
        batch_size = 15  # 每批处理的交易对数量
        total_batches = (len(perpetual_pairs) + batch_size - 1) // batch_size  # 向上取整计算批次数

        logger.info(
            f"{self.exchange_name} 开始并发获取 {len(perpetual_pairs)} 个交易对的历史资金费率，分 {total_batches} 批处理")

        for i in range(0, len(perpetual_pairs), batch_size):
            batch_symbols = perpetual_pairs[i:i + batch_size]
            batch_num = i // batch_size + 1
            logger.debug(f"{self.exchange_name} 处理第 {batch_num}/{total_batches} 批资金费率数据")

            # 为当前批次创建并发任务
            tasks = [self.fetch_historical_rates(symbol) for symbol in batch_symbols]

            # 并发执行当前批次的任务
            await asyncio.gather(*tasks)

            # 每批次之间稍微延迟，避免API限制
            if i + batch_size < len(perpetual_pairs):
                await asyncio.sleep(0.5)

    async def update_funding_rate(self, symbol: str, rate: float,
                                  timestamp: Optional[int] = None,
                                  next_funding_time: Optional[int] = None):
        """更新资金费率
        
        Args:
            symbol: 交易对
            rate: 资金费率
            timestamp: 时间戳（毫秒）用于记录资金费率的有效时间
            next_funding_time: 下次资金费率时间（毫秒）
        """
        try:
            # 获取历史数据
            rates = self.cache.get_historical_rates(self.exchange_name, symbol)

            # 如果没有时间戳，使用当前时间
            if timestamp is None:
                timestamp = int(datetime.now().timestamp() * 1000)

            # 获取时间间隔（用于时间槽的判断）
            interval = self._get_funding_interval(symbol, rates)

            # 获取当前时间与时间槽
            current_timestamp = int(datetime.now().timestamp() * 1000)
            current_time = datetime.fromtimestamp(current_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
            funding_time = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')

            # 计算时间槽
            time_slot = timestamp // interval

            # 检查是否已有该时间槽的数据
            slot_exists = False
            if rates:
                for idx, rate_data in enumerate(rates):
                    rate_slot = rate_data['timestamp'] // interval
                    if rate_slot == time_slot:
                        slot_exists = True
                        # 找到相同时间槽的资金费率，检查是否有变化
                        if abs(rate_data['rate'] - rate) < 0.0000001:  # 允许小误差
                            logger.debug(
                                f"{self.exchange_name} {symbol} - 时间槽 {time_slot} 资金费率值相同 {format_funding_rate(rate)}, 没有更新")
                            return
                        else:
                            # 如果资金费率变化了，则更新该时间槽的数据
                            logger.debug(
                                f"{self.exchange_name} {symbol} - 更新时间槽 {time_slot} 的资金费率从 {format_funding_rate(rate_data['rate'])} 到 {format_funding_rate(rate)}")
                            rates[idx]['rate'] = rate
                            # 更新缓存
                            self.cache.replace_funding_rate(
                                self.exchange_name,
                                symbol,
                                rates  # 直接替换整个资金费率列表
                            )
                            return

            # 如果该时间槽不存在，添加新记录
            if not slot_exists:
                # 使用交易所的原始时间戳为记录时间
                # 但在初始化时使用当前时间来确保时间戳递增
                if rates:
                    record_timestamp = timestamp  # 加载历史数据时使用原始时间戳
                else:
                    record_timestamp = current_timestamp  # 初始化时使用当前时间

                logger.debug(
                    f"{self.exchange_name} {symbol} - 新时间槽 {time_slot} 资金费率: {format_funding_rate(rate)}, 资金时间: {funding_time}, 记录时间: {current_time}")

                self.cache.update_funding_rate(
                    self.exchange_name,
                    symbol,
                    rate,
                    record_timestamp,  # 使用交易所的原始时间戳或当前时间
                    next_funding_time
                )
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 更新资金费率失败: {e}")

    def _get_funding_interval(self, symbol: str, rates: List[Dict]) -> int:
        """根据历史数据计算资金费率更新间隔（毫秒）
        
        Args:
            rates: 历史资金费率数据
            
        Returns:
            int: 更新间隔（毫秒），如果无法确定则返回8小时
        """
        if len(rates) < 3:  # 修改为至少需要3条记录才能计算间隔
            return 8 * 60 * 60 * 1000  # 默认8小时

        # 取最近两条记录计算间隔
        latest = rates[-1]['timestamp']
        previous = rates[-2]['timestamp']
        interval = abs(latest - previous)

        # 如果间隔接近2小时、4小时或8小时，则返回对应值
        two_hours = 2 * 60 * 60 * 1000
        four_hours = 4 * 60 * 60 * 1000
        eight_hours = 8 * 60 * 60 * 1000

        # 允许30分钟的误差
        error_margin = 30 * 60 * 1000

        if abs(interval - four_hours) <= error_margin:
            return four_hours
        elif abs(interval - eight_hours) <= error_margin:
            return eight_hours
        elif abs(interval - two_hours) <= error_margin:
            return two_hours

        # 如果无法确定，返回默认值
        logger.warning(
            f"{self.exchange_name} {symbol} 无法确定资金费率更新间隔: {interval / 1000 / 60 / 60:.2f}小时, 使用默认值8小时")
        return eight_hours

    async def fetch_historical_rates(self, symbol: str):
        """获取历史资金费率数据
        
        Args:
            symbol: 交易对
        """
        # 由子类实现具体的获取逻辑
        raise NotImplementedError

    async def fetch_latest_funding_rates(self, symbol: str):
        """获取最新资金费率数据
        
        Args:
            symbol: 交易对
        """
        # 由子类实现具体的获取逻辑
        raise NotImplementedError
