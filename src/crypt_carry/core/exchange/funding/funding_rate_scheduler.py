"""资金费率定时任务管理"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from .funding_rate_manager import FundingRateManager

logger = logging.getLogger(__name__)


class FundingRateScheduler:
    """
    资金费率定时任务管理器(负责定时任务)
        根据配置的时间点提前更新资金费率
        统一管理所有交易所的定时任务
    """

    def __init__(self, funding_managers: Dict[str, FundingRateManager], perpetual_pairs: List[str]):
        """初始化定时任务管理器
        
        Args:
            funding_managers: 资金费率管理器字典 {exchange_name: manager}
            perpetual_pairs: 永续合约交易对列表
        """
        self.funding_managers = funding_managers
        self.perpetual_pairs = perpetual_pairs
        self.running = False
        self.tasks = []

        # 使用ExchangeConfig获取交易配置
        exchange_config = ExchangeConfig()  # 不指定交易所，获取全局配置
        trading_config = exchange_config.trading_config
        # 获取资金费率相关配置
        self.funding_times = trading_config.get("FUNDING_RATE_TIMES", ["00:00", "08:00", "16:00"])
        self.ahead_minutes = trading_config.get("REBALANCE_AHEAD_MINUTES", 480)

        # 是否始终更新资金费率，不考虑时间点限制
        self.always_update_funding_rates = trading_config.get("ALWAYS_UPDATE_FUNDING_RATES", False)
        # 资金费率更新间隔（分钟），仅在always_update_funding_rates=True时使用
        self.funding_rate_update_interval = trading_config.get("FUNDING_RATE_UPDATE_INTERVAL", 10)
        # 上次更新资金费率的时间
        self.last_update_time = None

        # 记录每个时间点是否已经执行过
        self.executed_times = {}

    async def start(self):
        """启动定时任务"""
        if self.running:
            return

        self.running = True
        logger.info("启动资金费率定时任务")

        # 创建定时任务
        self.tasks = [
            asyncio.create_task(self._update_funding_rates()),
            asyncio.create_task(self._save_funding_rates())
        ]

    async def stop(self):
        """停止定时任务"""
        if not self.running:
            return

        self.running = False
        logger.info("停止资金费率定时任务")

        # 取消所有任务
        for task in self.tasks:
            task.cancel()

        # 等待任务完成
        await asyncio.gather(*self.tasks, return_exceptions=True)
        self.tasks.clear()

    async def _update_funding_rates(self):
        """更新资金费率任务"""
        while self.running:
            try:
                # 判断是否应该更新资金费率
                if self._should_update_funding_rates():
                    for exchange_name, manager in self.funding_managers.items():
                        logger.info(f"定时任务正在更新 {exchange_name} 的资金费率")
                        # 创建每个交易对的任务
                        tasks = []
                        for symbol in self.perpetual_pairs:
                            # 正确创建协程任务
                            task = asyncio.create_task(manager.fetch_latest_funding_rates(symbol))
                            tasks.append(task)
                            await asyncio.sleep(0.5)
                        # 使用asyncio.gather并发执行所有任务
                        await asyncio.gather(*tasks, return_exceptions=True)
                        logger.info(f"{exchange_name} 资金费率更新完成")

                    # 记录最后更新时间
                    self.last_update_time = datetime.now()

                await asyncio.sleep(60 * self.funding_rate_update_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"更新资金费率失败: {e}")
                await asyncio.sleep(60)  # 发生错误时等待1分钟

    async def _save_funding_rates(self):
        """保存资金费率任务"""
        while self.running:
            try:
                current_time = datetime.now()

                # 每4小时的整点保存
                if current_time.hour % 4 == 0 and current_time.minute == 0:
                    for exchange_name, manager in self.funding_managers.items():
                        perpetual_pairs = self.perpetual_pairs
                        for symbol in perpetual_pairs:
                            logger.debug(f"定时任务正在保存 {exchange_name} 的 {symbol} 的资金费率数据")
                            rates = manager.cache.get_historical_rates(exchange_name, symbol)
                            manager.storage.save_funding_rates(symbol, rates)

                    logger.info("已保存所有交易对的资金费率数据")

                # 每2小时检查一次
                await asyncio.sleep(60 * 60 * 2)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"保存资金费率失败: {e}")
                await asyncio.sleep(60)  # 发生错误时等待1分钟

    def _should_update_funding_rates(self) -> bool:
        """判断是否应该更新资金费率
        
        根据配置决定是否更新资金费率:
        1. 如果always_update_funding_rates=True，则允许更新资金费率
        2. 如果always_update_funding_rates=False，则只在接近资金费率时间点且未执行过时更新
        
        Returns:
            bool: 是否需要执行更新
        """
        current_time = datetime.now()

        # 如果配置为始终更新资金费率
        if self.always_update_funding_rates:
            return True
        # 否则，按照原逻辑判断是否在资金费率时间点附近
        current_date = current_time.date()
        logger.debug(f"当前时间：{current_time}")

        # 检查今天和明天的时间点
        for days_ahead in [0, 1]:
            check_date = current_date + timedelta(days=days_ahead)
            for time_str in self.funding_times:
                hour, minute = map(int, time_str.split(':'))
                funding_time = datetime.combine(check_date, datetime.min.time().replace(hour=hour, minute=minute))

                time_diff = funding_time - current_time
                # 转换为分钟
                minutes_diff = time_diff.total_seconds() / 60
                logger.debug(f"检查时间点: {funding_time}, 时间差: {minutes_diff:.2f}分钟")

                # 如果在提前时间范围内
                if 0 <= minutes_diff <= self.ahead_minutes:
                    # 检查这个时间点是否已经执行过
                    execution_key = f"{check_date}_{time_str}"
                    if execution_key not in self.executed_times:
                        # 记录已执行
                        self.executed_times[execution_key] = True
                        # 清理旧的记录(保留最近3天的)
                        self._clean_executed_times()
                        logger.info(f"触发更新: {execution_key}, 距离时间点还有 {minutes_diff:.2f} 分钟")
                        return True

        return False

    def _clean_executed_times(self):
        """清理过期的执行记录"""
        current_date = datetime.now().date()
        keys_to_remove = []

        for key in self.executed_times:
            date_str = key.split('_')[0]
            record_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # 如果记录超过3天,则删除
            if (current_date - record_date).days > 3:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.executed_times[key]
