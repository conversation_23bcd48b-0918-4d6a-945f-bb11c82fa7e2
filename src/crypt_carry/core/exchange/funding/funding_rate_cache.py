"""资金费率缓存管理"""
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from util.formatter import format_funding_rate

logger = logging.getLogger(__name__)


class FundingRateCache:
    """资金费率缓存管理器"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FundingRateCache, cls).__new__(cls)
            cls._instance.funding_rates = {}  # 格式: {exchange: {symbol: [rates]}}
        return cls._instance

    def update_funding_rate(self, exchange: str, standard_symbol: str, rate: float,
                            timestamp: int, next_funding_time: Optional[int] = None,
                            settlement_interval: Optional[int] = None):
        """更新单条资金费率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            rate: 资金费率
            timestamp: 时间戳（毫秒）
            next_funding_time: 下次资金费率时间（毫秒）
            settlement_interval: 结算周期（小时），如 2/4/8 小时
        """
        try:
            # 创建新数据记录
            new_data = {
                'rate': rate,  # 保持原始数值格式
                'timestamp': timestamp,
                'next_funding_time': next_funding_time,
                'settlement_interval': settlement_interval  # 添加结算周期字段
            }

            # 调用批量更新函数，使用append_mode=True来追加单条数据
            self.update_funding_rates(
                exchange=exchange,
                standard_symbol=standard_symbol,
                rates=[new_data],  # 将单条数据放入列表
                append_mode=True  # 使用追加模式
            )
            
            # 记录日志时使用格式化显示
            logger.debug(
                f"更新资金费率 - {exchange} {standard_symbol}: {format_funding_rate(rate)}, 结算周期: {settlement_interval}小时")

        except Exception as e:
            logger.error(f"更新资金费率缓存失败 - {exchange} {standard_symbol}: {e}")

    def replace_funding_rate(self, exchange: str, standard_symbol: str, rates: List[Dict[str, Any]]):
        """直接替换整个资金费率列表，而不是合并
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            rates: 新的资金费率数据列表
        """
        try:
            # 调用批量更新函数，使用replace_mode=True来替换所有数据
            self.update_funding_rates(
                exchange=exchange,
                standard_symbol=standard_symbol,
                rates=rates,
                replace_mode=True  # 使用替换模式
            )
            
            logger.debug(f"替换资金费率列表 - {exchange} {standard_symbol}: {len(rates)}条记录")
        except Exception as e:
            logger.error(f"替换资金费率列表失败 - {exchange} {standard_symbol}: {e}")

    def update_funding_rates(self, exchange: str, standard_symbol: str, rates: List[Dict[str, Any]],
                             replace_mode: bool = False, append_mode: bool = False):
        """批量更新资金费率，根据模式决定是合并、替换还是追加
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            rates: 资金费率数据列表,每个元素包含 timestamp、rate、next_funding_time 和 settlement_interval
            replace_mode: 是否直接替换模式，为True则直接用新数据替换旧数据
            append_mode: 是否追加模式，为True则直接追加新数据（用于单条更新）
        """
        try:
            # 初始化交易所数据
            if exchange not in self.funding_rates:
                self.funding_rates[exchange] = {}

            # 初始化交易对数据
            if standard_symbol not in self.funding_rates[exchange]:
                self.funding_rates[exchange][standard_symbol] = []

            # 获取现有数据
            existing_rates = self.funding_rates[exchange][standard_symbol]

            # 根据不同模式处理数据
            if replace_mode:
                # 替换模式：直接用新数据替换
                merged_rates = rates
            elif append_mode:
                # 追加模式：添加新数据到现有列表
                merged_rates = existing_rates + rates
            else:
                # 合并模式：合并新旧数据，保证时间戳不重复
                # 将新数据按时间戳排序
                sorted_rates = sorted(rates, key=lambda x: x['timestamp'])

                # 合并数据,保证时间戳不重复
                merged_rates = []
                i, j = 0, 0

                while i < len(existing_rates) and j < len(sorted_rates):
                    existing_ts = existing_rates[i]['timestamp']
                    new_ts = sorted_rates[j]['timestamp']

                    if existing_ts < new_ts:
                        merged_rates.append(existing_rates[i])
                        i += 1
                    elif existing_ts > new_ts:
                        merged_rates.append(sorted_rates[j])
                        j += 1
                    else:
                        # 时间戳相同时使用新数据
                        merged_rates.append(sorted_rates[j])
                        i += 1
                        j += 1

                # 添加剩余数据
                merged_rates.extend(existing_rates[i:])
                merged_rates.extend(sorted_rates[j:])

            # 按时间戳排序，确保最新的数据在最后
            merged_rates.sort(key=lambda x: x['timestamp'])

            # 只保留最近100条记录
            max_history = 100
            if len(merged_rates) > max_history:
                merged_rates = merged_rates[-max_history:]

            # 更新缓存
            self.funding_rates[exchange][standard_symbol] = merged_rates

        except Exception as e:
            logger.error(f"更新资金费率数据失败 - {exchange} {standard_symbol}: {e}")

    def get_current_rate(self, exchange: str, standard_symbol: str) -> Optional[float]:
        """获取当前资金费率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
        """
        try:
            if exchange in self.funding_rates and standard_symbol in self.funding_rates[exchange] and \
                    self.funding_rates[exchange][standard_symbol]:
                # 资金费率数据
                rates = self.funding_rates[exchange][standard_symbol]

                # 先按时间戳排序，确保获取最新数据
                rates.sort(key=lambda x: x['timestamp'])

                # 获取最新数据
                latest_rate_data = rates[-1]

                # 输出详细日志用于调试
                timestamp = latest_rate_data['timestamp']
                timestamp_str = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                logger.debug(
                    f"{exchange} {standard_symbol} 当前资金费率: {latest_rate_data['rate']}, 资金费率时间: {timestamp_str}, 当前时间: {current_time}")

                return latest_rate_data['rate']
        except Exception as e:
            logger.error(f"获取当前资金费率失败 - {exchange} {standard_symbol}: {e}")
        return None

    def get_historical_rates(self, exchange: str, standard_symbol: str, limit: Optional[int] = None) -> List[Dict]:
        """获取历史资金费率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 精简标准交易对
            limit: 返回条数限制
        """
        try:
            if exchange in self.funding_rates and standard_symbol in self.funding_rates[exchange]:
                rates = self.funding_rates[exchange][standard_symbol]
                if limit:
                    return rates[-limit:]
                return rates
        except Exception as e:
            logger.error(f"获取历史资金费率失败 - {exchange} {standard_symbol}: {e}")
        return []

    def get_next_funding_time(self, exchange: str, standard_symbol: str) -> Optional[int]:
        """获取下次资金费率时间
        
        Args:
            exchange: 交易所名称
            standard_symbol: 交易对
        """
        try:
            if exchange in self.funding_rates and \
                    standard_symbol in self.funding_rates[exchange] and \
                    self.funding_rates[exchange][standard_symbol]:
                return self.funding_rates[exchange][standard_symbol][-1]['next_funding_time']
        except Exception as e:
            logger.error(f"获取下次资金费率时间失败 - {exchange} {standard_symbol}: {e}")
        return None

    def get_settlement_interval(self, exchange: str, standard_symbol: str) -> Optional[int]:
        """获取交易对的结算周期（小时）
        
        Args:
            exchange: 交易所名称
            standard_symbol: 交易对
            
        Returns:
            Optional[int]: 结算周期（小时），如 2/4/8 小时，如果未知则返回 None
        """
        try:
            if exchange in self.funding_rates and \
                    standard_symbol in self.funding_rates[exchange] and \
                    self.funding_rates[exchange][standard_symbol]:
                latest_data = self.funding_rates[exchange][standard_symbol][-1]
                return latest_data.get('settlement_interval')
        except Exception as e:
            logger.error(f"获取结算周期失败 - {exchange} {standard_symbol}: {e}")
        return None

    def get_normal_funding_rate(self, exchange: str, standard_symbol: str) -> Optional[float]:
        """获取交易对的折算成8小时的资金费率"""
        try:
            # 获取资金费率和结算周期
            funding_rate = self.get_current_rate(exchange, standard_symbol)
            settlement_interval = self.get_settlement_interval(exchange, standard_symbol)

            # 将资金费率标准化到8小时周期
            standard_interval = 8  # 标准结算周期为8小时
            if funding_rate and settlement_interval and settlement_interval != standard_interval:
                # 计算比例因子并应用到资金费率
                scaling_factor = standard_interval / settlement_interval
                normalized_funding_rate = funding_rate * scaling_factor
                logger.debug(
                    f"{exchange} {standard_symbol} 资金费率从 {funding_rate:.6f} "
                    f"({settlement_interval}小时周期) 标准化为 {normalized_funding_rate:.6f} (8小时周期)"
                )
            else:
                normalized_funding_rate = funding_rate
            return normalized_funding_rate
        except Exception as e:
            logger.error(f"获取正常资金费率失败 - {exchange} {standard_symbol}: {e}")
        return None

    def get_average_rate(self, exchange: str, standard_symbol: str, window: int = 24) -> Optional[float]:
        """计算平均资金费率
        
        Args:
            exchange: 交易所名称
            standard_symbol: 交易对
            window: 时间窗口(小时)
        """
        try:
            rates = self.get_historical_rates(exchange, standard_symbol, window)
            if rates:
                return sum(r['rate'] for r in rates) / len(rates)
        except Exception as e:
            logger.error(f"计算平均资金费率失败 - {exchange} {standard_symbol}: {e}")
        return None

    def get_symbols_by_exchange(self, exchange: str) -> List[str]:
        """获取指定交易所的交易对列表
        
        Args:
            exchange: 交易所名称
            
        Returns:
            List[str]: 交易对列表
        """
        return list(self.funding_rates.get(exchange, {}).keys())
