"""资金费率存储管理器"""
from pathlib import Path
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig

logger = logging.getLogger(__name__)

class FundingRateStorage:
    """
    资金费率存储管理器(负责文件存储)
        每个交易对单独存储一个文件
        提供数据过期检查
        负责文件读写操作
    """

    def __init__(self, config: ExchangeConfig, storage_dir: Path):
        """初始化资金费率存储管理器
        
        Args:
            config: 交易所配置
        """
        self.config = config
        self.exchange_name = config.exchange_name
        self.storage_dir = storage_dir

        # 确保存储目录存在
        self.storage_dir.mkdir(parents=True, exist_ok=True)

    def _get_symbol_file(self, symbol: str) -> Path:
        """获取交易对的存储文件路径
        
        Args:
            symbol: 交易对
            
        Returns:
            Path: 存储文件路径
        """
        # 将交易对名称转换为文件名
        filename = symbol.replace('/', '_').lower() + '.json'
        return self.storage_dir / filename

    def load_funding_rates(self, symbol: str) -> List[Dict]:
        """加载资金费率数据
        
        Args:
            symbol: 交易对
            
        Returns:
            List[Dict]: 资金费率数据列表
        """
        try:
            file_path = self._get_symbol_file(symbol)
            if not file_path.exists():
                return []

            with open(file_path, 'r') as f:
                data = json.load(f)
                return data.get('rates', [])

        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 加载资金费率数据失败: {e}")
            return []

    def save_funding_rates(self, symbol: str, rates: List[Dict]):
        """保存资金费率数据
        
        Args:
            symbol: 交易对
            rates: 资金费率数据列表
        """
        try:
            file_path = self._get_symbol_file(symbol)
            logger.debug(f"保存 {symbol} 的资金费率数据到 {file_path}")
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            data = {
                'symbol': symbol,
                'exchange': self.exchange_name,
                'last_update': datetime.now().isoformat(),
                'rates': rates
            }

            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.debug(f"{self.exchange_name} {symbol} 资金费率数据保存成功")
            
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 保存资金费率数据失败: {e}")

    def is_data_expired(self, symbol: str) -> bool:
        """检查数据是否过期
        
        Args:
            symbol: 交易对
            
        Returns:
            bool: 是否过期
        """
        try:
            file_path = self._get_symbol_file(symbol)
            if not file_path.exists():
                return True

            with open(file_path, 'r') as f:
                data = json.load(f)

            last_update = datetime.fromisoformat(data.get('last_update', ''))
            expiry_time = timedelta(hours=24)  # 24小时过期

            return datetime.now() - last_update > expiry_time
            
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 检查数据过期失败: {e}")
            return True
