"""OKX市场数据管理器"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple

import ccxt

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.providers.okx.okx_symbol_utils import build_internal_symbol, to_standard_symbol
from crypt_carry.utils.retry import async_retry

logger = logging.getLogger(__name__)


class OkxMarketManager:
    """OKX市场数据管理器"""

    def __init__(self, config: ExchangeConfig, exchange: ccxt.okx):
        """初始化OKX市场数据管理器
        
        Args:
            config: 交易所配置
            exchange: ccxt交易所实例
        """
        self.exchange_name = config.exchange_name
        self.config = config
        self.market_manager = MarketDataManager.get_instance()
        self.base_pair = []
        self.spot_pairs = []
        self.perpetual_pairs = []
        self.all_pairs = []
        self.detect_exclude_pairs = []
        self.exchange = exchange  # 由OkxClient设置
        self.quotes = []  # 由load_markets_with_retry设置

    async def _discover_trading_pairs_from_api(self) -> <PERSON><PERSON>[Dict, Dict, List[str]]:
        """从API发现有效的交易对
        
        Returns:
            Tuple[Dict, Dict, List[str]]: (有效交易对, paired_markets字典, 所有既有现货又有合约的交易对列表)
        """
        # 同时出现在现货和永续合约的交易对映射
        paired_markets = {}  # 格式: {base_quote: {'spot': spot_symbol, 'swap': swap_symbol}}

        try:
            # 加载所有市场数据
            markets = await self.exchange.load_markets()

            # 获取所有计价币种
            self.quotes = ['USDT']  # 默认使用USDT
            logger.info(f"{self.exchange_name} 计价币种: {self.quotes}")

            # 第一步: 找出所有现货和永续合约交易对
            for symbol, market in markets.items():
                try:
                    # 只考虑self.quotes 计价的交易对
                    if market['quote'] not in self.quotes:
                        continue

                    base_quote = f"{market['base']}/{market['quote']}"

                    if market['type'] == 'spot':
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['spot'] = symbol
                    elif market['type'] == 'swap' and market['linear']:  # 只考虑USDT线性合约
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['swap'] = symbol
                        # 保存合约面值信息
                        if 'contractSize' in market and market['contractSize'] is not None:
                            paired_markets[base_quote]['contractSize'] = float(market['contractSize'])
                        else:
                            # 如果无法获取合约面值，默认使用0.01（一般币种的标准）
                            paired_markets[base_quote]['contractSize'] = 0.01
                except Exception as e:
                    logger.error(f"处理市场数据时出错 {symbol}: {str(e)}")

            # 第二步: 筛选同时有现货和永续合约的交易对
            valid_pairs = {}
            all_pairs = []
            for base_quote, symbols in paired_markets.items():
                if 'spot' in symbols and 'swap' in symbols:
                    valid_pairs[base_quote] = symbols
                    all_pairs.append(base_quote)

            logger.info(
                f"{self.exchange_name} 找到同时有现货和永续合约的交易对: {len(valid_pairs)}, all_pairs: {len(all_pairs)}")

            return valid_pairs, paired_markets, all_pairs

        except Exception as e:
            # logger.error(f"{self.exchange_name} 从API发现交易对失败: {str(e)}")
            import traceback
            logger.error(f"{self.exchange_name} 加载市场数据失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise e

    async def _load_trading_pairs_from_config(self) -> Tuple[Dict, Dict, List[str]]:
        """从配置文件加载交易对
        
        Returns:
            Tuple[Dict, Dict, List[str]]: (配置文件中指定的交易对, 所有既有现货又有合约的交易对, 所有既有现货又有合约的交易对列表)
        """
        try:
            # 从已有的配置对象获取交易对配置
            exchange_config = self.config.trading_config

            # 获取交易所配置的交易对
            if 'EXCHANGE_PAIRS' not in exchange_config or self.exchange_name.upper() not in exchange_config[
                'EXCHANGE_PAIRS']:
                logger.warning(f"{self.exchange_name} 配置文件中未找到交易对配置")
                return {}, {}, []

            exchange_pairs_config = exchange_config['EXCHANGE_PAIRS'][self.exchange_name.upper()]
            symbol_list = exchange_pairs_config.get('symbol', [])
            quote_list = exchange_pairs_config.get('quote', ['USDT'])
            exclude_list = exchange_pairs_config.get('exclude', [])

            # 设置默认的计价货币
            self.quotes = quote_list
            logger.info(f"{self.exchange_name} 从配置文件获取计价币种: {self.quotes}")

            # 加载市场信息以获取合约面值等信息
            markets = await self.exchange.load_markets()

            # 第一步: 扫描所有市场数据，找出所有现货和永续合约交易对
            paired_markets = {}
            for symbol, market in markets.items():
                try:
                    # 只考虑self.quotes 计价的交易对
                    if market['quote'] not in self.quotes:
                        continue

                    base_quote = f"{market['base']}/{market['quote']}"

                    if market['type'] == 'spot':
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['spot'] = symbol
                    elif market['type'] == 'swap' and market['linear']:  # 只考虑USDT线性合约
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['swap'] = symbol
                        # 保存合约面值信息
                        if 'contractSize' in market and market['contractSize'] is not None:
                            paired_markets[base_quote]['contractSize'] = float(market['contractSize'])
                        else:
                            # 如果无法获取合约面值，默认使用0.01（一般币种的标准）
                            paired_markets[base_quote]['contractSize'] = 0.01
                except Exception as e:
                    logger.error(f"处理市场数据时出错 {symbol}: {str(e)}")

            # 第二步: 筛选同时有现货和永续合约的交易对
            valid_pairs = {}
            all_pairs = []
            for base_quote, symbols in paired_markets.items():
                # 检查是否在配置的交易对列表中
                base, quote = base_quote.split('/')

                # 只有同时存在现货和合约的交易对才是有效的
                if 'spot' in symbols and 'swap' in symbols:
                    # 添加到所有交易对列表
                    all_pairs.append(base_quote)

                    # 如果在配置中指定了，或者我们想要获取所有有效交易对
                    is_in_config = base in symbol_list and base not in exclude_list and quote in quote_list

                    if is_in_config or not symbol_list:  # 如果symbol_list为空，表示获取所有交易对
                        valid_pairs[base_quote] = symbols

            logger.info(
                f"{self.exchange_name} 从配置文件和市场数据中找到 {len(valid_pairs)} 个有效交易对，总共 {len(paired_markets)} 个交易对，其中 {len(all_pairs)} 个同时有现货和合约")
            return valid_pairs, paired_markets, all_pairs

        except Exception as e:
            logger.error(f"{self.exchange_name} 从配置文件加载交易对失败: {str(e)}")
            raise e

    @async_retry(max_retries=3, initial_delay=1.0, backoff_factor=2.0)
    async def load_markets_with_retry(self) -> bool:
        """加载市场数据并初始化交易对
        
        Returns:
            bool: 是否成功
        """
        # 使用集合存储交易对，避免重复
        base_pair_set = set()
        perpetual_pairs_set = set()
        spot_pairs_set = set()
        all_pairs_set = set()

        # 从配置文件读取交易量阈值设置
        exchange_config = self.config.trading_config.get('EXCHANGE_CONFIG', {}).get(self.exchange_name.upper(), {})
        market_config = exchange_config.get('MARKET', {})

        # 获取交易量阈值，如果配置文件中没有设置，则使用默认值
        SPOT_VOLUME_THRESHOLD = market_config.get('SPOT_VOLUME_THRESHOLD', 2000000)  # 默认200万USDT
        FUTURE_VOLUME_THRESHOLD = market_config.get('FUTURE_VOLUME_THRESHOLD', 4000000)  # 默认400万USDT
        logger.info(
            f"{self.exchange_name} 交易量阈值设置 - 现货: {SPOT_VOLUME_THRESHOLD} USDT, 合约: {FUTURE_VOLUME_THRESHOLD} USDT")

        valid_pairs = {}
        paired_markets = {}
        all_pairs = []

        try:
            if (self.config.pair_from == 'CONFIG'):
                logger.info(f"{self.exchange_name} 使用配置文件加载交易对")
                valid_pairs, paired_markets, all_pairs = await self._load_trading_pairs_from_config()
            elif (self.config.pair_from == 'API'):
                logger.info(f"{self.exchange_name} 使用API加载交易对")
                valid_pairs, paired_markets, all_pairs = await self._discover_trading_pairs_from_api()
            else:
                raise ValueError(f"{self.exchange_name} 无效的 PAIR_FROM 配置: {self.config.pair_from}")
            # 第三步: 获取合约交易对的24小时交易量并筛选
            # 使用协程并发获取所有交易对的交易量
            high_volume_pairs = await self._fetch_volumes_concurrently(valid_pairs, SPOT_VOLUME_THRESHOLD,
                                                                       FUTURE_VOLUME_THRESHOLD)

            # 交易量从高到低排序
            high_volume_pairs.sort(key=lambda x: x[2], reverse=True)
            logger.info(f"{self.exchange_name} 找到 {len(high_volume_pairs)} 个高交易量交易对")

            # 第四步: 将筛选出的交易对添加到交易列表中
            for base_quote, symbols, volume in high_volume_pairs:
                try:
                    base, quote = base_quote.split('/')

                    # 构造标准格式的交易对
                    spot_standard_symbol = build_internal_symbol(base, quote, is_futures=False)
                    future_standard_symbol = build_internal_symbol(base, quote, is_futures=True)
                    standard_symbol = to_standard_symbol(spot_standard_symbol)

                    # 添加到集合中（自动去重）
                    base_pair_set.add(standard_symbol)
                    perpetual_pairs_set.add(future_standard_symbol)
                    spot_pairs_set.add(spot_standard_symbol)

                    logger.debug(
                        f"添加交易对: {standard_symbol}, 现货: {spot_standard_symbol}, 合约: {future_standard_symbol}")

                except Exception as e:
                    logger.error(f"处理高交易量交易对 {base_quote} 时出错: {str(e)}")
                    continue

            # 第五步: 处理所有交易对（包括不满足交易量要求的）
            # 处理从 all_pairs 列表中获取的所有交易对
            if all_pairs is not None:  # 确保 all_pairs 不是 None
                for base_quote in all_pairs:
                    try:
                        base, quote = base_quote.split('/')

                        # 构造标准格式的交易对
                        spot_standard_symbol = build_internal_symbol(base, quote, is_futures=False)
                        future_standard_symbol = build_internal_symbol(base, quote, is_futures=True)

                        # 添加到所有交易对集合
                        all_pairs_set.add(spot_standard_symbol)
                        all_pairs_set.add(future_standard_symbol)

                    except Exception as e:
                        logger.error(f"处理交易对 {base_quote} 时出错: {str(e)}")
                        continue
            else:
                logger.error("all_pairs 为空，跳过处理所有交易对的步骤")

            # 将集合转换为列表
            self.base_pair = list(base_pair_set)
            self.perpetual_pairs = list(perpetual_pairs_set)
            self.spot_pairs = list(spot_pairs_set)
            self.all_pairs = list(all_pairs_set)
            self.detect_exclude_pairs = await self.load_detect_exclude_pairs()

            # 检查是否找到交易对
            if not self.perpetual_pairs:
                logger.warning(f"{self.exchange_name} 没有找到符合条件的永续合约交易对")
            if not self.spot_pairs:
                logger.warning(f"{self.exchange_name} 没有找到符合条件的现货交易对")

            logger.info(
                f"{self.exchange_name} 加载市场数据成功 - "
                f"基础交易对: {len(self.base_pair)}个, "
                f"永续合约: {len(self.perpetual_pairs)}个, "
                f"现货: {len(self.spot_pairs)}个, "
                f"所有交易对: {len(self.all_pairs)}个"
            )

            # 详细打印所有选中的交易对
            if logger.isEnabledFor(logging.DEBUG):
                for i, pair in enumerate(high_volume_pairs):
                    base_quote, symbols, volume = pair
                    logger.debug(f"#{i + 1} 交易对: {base_quote}, 交易量: {volume} USDT")

            return True

        except Exception as e:
            logger.error(f"{self.exchange_name} 加载市场数据失败: {str(e)}")
            from crypt_carry.utils.retry import RetryableError
            raise RetryableError(f"{self.exchange_name} load_markets_with_retry 失败: {str(e)}") from e

    async def load_detect_exclude_pairs(self) -> List[str]:
        detect_exclude_pairs_set = set()
        # 检查 detect_exclude
        if hasattr(self.config, 'trading_detect_exclude') and self.config.trading_detect_exclude is not None:
            for symbol in self.config.trading_detect_exclude:
                for quote in self.quotes:
                    detect_internal_symbol = build_internal_symbol(symbol, quote, is_futures=False)
                    detect_exclude_pairs_set.add(to_standard_symbol(detect_internal_symbol))
        else:
            logger.debug(f"{self.exchange_name} 没有配置 trading_detect_exclude 或为空")
        return list(detect_exclude_pairs_set)

    @async_retry(max_retries=3, initial_delay=1, backoff_factor=2.0)
    async def _fetch_ticker_with_retry(self, swap_symbol, contract_size=0.01):
        """获取交易对的ticker数据，并计算交易量
        
        Args:
            swap_symbol: 交易对符号
            contract_size: 合约面值
            
        Returns:
            tuple: (交易对符号, 24小时交易量(USDT))
        
        Raises:
            RetryableError: 当需要重试的错误发生时
        """
        try:
            ticker = await self.exchange.fetch_ticker(swap_symbol)

            # 获取最新价格
            last_price = ticker['last'] if 'last' in ticker and ticker['last'] is not None else 1

            # 计算实际交易量 (以USDT计价)
            if 'baseVolume' in ticker and ticker['baseVolume'] is not None:
                # 使用公式: baseVolume × 合约面值 × 最新价格
                base_volume = ticker['baseVolume']
                volume_usd = base_volume * contract_size * last_price
                logger.debug(
                    f"{swap_symbol} 交易量计算: {base_volume} × {contract_size} × {last_price} = {volume_usd} USDT")
            elif 'info' in ticker and 'volCcy24h' in ticker['info']:
                # 如果API直接提供了按计价货币的交易量，但需要乘以最新价格
                vol_ccy = float(ticker['info']['volCcy24h'])
                volume_usd = vol_ccy * last_price
                logger.debug(f"{swap_symbol} 使用volCcy24h计算交易量: {vol_ccy} × {last_price} = {volume_usd} USDT")
            else:
                logger.warning(f"无法获取{swap_symbol}的交易量数据，跳过")
                return None, 0

            return swap_symbol, volume_usd

        except Exception as e:
            logger.warning(f"获取 {swap_symbol} 交易量失败: {str(e)}")
            # 抛出可重试异常，让装饰器处理重试逻辑
            from crypt_carry.utils.retry import RetryableError
            raise RetryableError(f"获取 {swap_symbol} 交易量失败: {str(e)}") from e

    async def _fetch_volumes_concurrently(self, valid_pairs, spot_volume_threshold, future_volume_threshold,
                                          batch_size=20):
        """并发获取多个交易对的交易量
        
        Args:
            valid_pairs: 有效的交易对字典 {base_quote: {'spot': spot_symbol, 'swap': swap_symbol}}
            spot_volume_threshold: 现货交易量阈值
            future_volume_threshold: 合约交易量阈值
            batch_size: 批处理大小，控制并发数量
            
        Returns:
            list: 高交易量交易对列表 [(base_quote, symbols, volume)]
        """
        high_volume_pairs = []
        total_pairs = len(valid_pairs)

        # 将交易对按批次处理，避免过多并发请求
        items = list(valid_pairs.items())
        # 减小批处理大小，避免并发请求过多
        batch_size = min(batch_size, 15)  # 限制最大批处理大小为15
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]

        logger.info(f"开始获取 {total_pairs} 个交易对的交易量，分 {len(batches)} 批处理")

        for batch_idx, batch in enumerate(batches):
            swap_tasks = []  # 合约交易量获取任务
            spot_tasks = []  # 现货交易量获取任务

            # 创建每个交易对的获取任务
            for base_quote, symbols in batch:
                try:
                    # 创建合约交易量获取任务
                    if 'swap' in symbols:
                        swap_symbol = symbols['swap']
                        contract_size = symbols.get('contractSize', 0.01)

                        # 为每个任务添加一个小的随机延迟，避免请求同时发出
                        await asyncio.sleep(0.3)

                        swap_task = asyncio.create_task(
                            self._fetch_ticker_with_retry(swap_symbol, contract_size)
                        )
                        swap_tasks.append((base_quote, symbols, swap_task))

                    # 创建现货交易量获取任务
                    if 'spot' in symbols:
                        spot_symbol = symbols['spot']
                        # 现货不需要合约面值，传入1.0

                        # 为每个任务添加一个小的随机延迟，避免请求同时发出
                        await asyncio.sleep(0.3)

                        spot_task = asyncio.create_task(
                            self._fetch_ticker_with_retry(spot_symbol, 1.0)
                        )
                        spot_tasks.append((base_quote, symbols, spot_task))
                except Exception as e:
                    logger.error(f"创建获取 {base_quote} 交易量任务时出错: {str(e)}")

            # 处理合约交易量结果
            swap_volume_results = {}
            for base_quote, symbols, task in swap_tasks:
                try:
                    swap_symbol, volume_usd = await task
                    if swap_symbol:
                        swap_volume_results[base_quote] = volume_usd
                        logger.debug(f"合约 {swap_symbol} 交易量: {volume_usd} USDT")
                except Exception as e:
                    logger.error(f"处理 {base_quote} 合约交易量结果时出错: {str(e)}")

            # 处理现货交易量结果
            spot_volume_results = {}
            for base_quote, symbols, task in spot_tasks:
                try:
                    spot_symbol, volume_usd = await task
                    if spot_symbol:
                        spot_volume_results[base_quote] = volume_usd
                        logger.debug(f"现货 {spot_symbol} 交易量: {volume_usd} USDT")
                except Exception as e:
                    logger.error(f"处理 {base_quote} 现货交易量结果时出错: {str(e)}")

            # 根据交易量阈值筛选交易对
            for base_quote, symbols in batch:
                try:
                    swap_volume = swap_volume_results.get(base_quote, 0)
                    spot_volume = spot_volume_results.get(base_quote, 0)

                    # 同时满足现货和合约交易量阈值
                    if swap_volume >= future_volume_threshold and spot_volume >= spot_volume_threshold:
                        # 使用合约交易量作为排序依据
                        high_volume_pairs.append((base_quote, symbols, swap_volume))
                        logger.info(
                            f"添加高交易量交易对: {base_quote}, 合约交易量: {swap_volume} USDT, 现货交易量: {spot_volume} USDT")
                except Exception as e:
                    logger.error(f"筛选 {base_quote} 交易量时出错: {str(e)}")

            # 打印进度
            processed = min((batch_idx + 1) * batch_size, total_pairs)
            logger.info(f"进度: {processed}/{total_pairs} ({(processed / total_pairs * 100):.1f}%)")

            # 在批次之间增加暂停时间，避免可能的限流
            if batch_idx < len(batches) - 1:
                await asyncio.sleep(1)  # 增加批次间的延迟到1秒

        return high_volume_pairs

    def get_trading_pairs(self) -> Tuple[List[str], List[str], List[str], List[str], List[str]]:
        """获取交易对列表
        
        Returns:
            Tuple[List[str], List[str], List[str], List[str], List[str]]: 基础交易对、现货交易对、永续合约交易对、所有交易对、检测排除交易对
        """
        return self.base_pair, self.spot_pairs, self.perpetual_pairs, self.all_pairs, self.detect_exclude_pairs

    def update_market_data(self, symbol: str, data: Dict):
        """更新市场数据
        
        Args:
            symbol: 交易对符号
            data: 市场数据，包含timestamp和价格信息
        """
        # 获取当前价格类型
        if 'futures_price' in data:
            price_type = 'futures_price'
        elif 'spot_price' in data:
            price_type = 'spot_price'
        else:
            logger.warning(f"{self.exchange_name} {symbol}无效的价格数据类型: {data}")
            return

        # 更新市场数据
        self.market_manager.update_market_data(self.exchange_name, symbol, data)

    def get_market_data(self, symbol: str) -> Optional[Dict]:
        """获取特定交易对的市场数据"""
        return self.market_manager.get_market_data(self.exchange_name, symbol)

    def get_basis_data(self, symbol: str) -> Optional[Dict]:
        """获取基差数据"""
        return self.market_manager.get_basis_data(self.exchange_name, symbol)

    def get_price(self, symbol: str, market_type: str = 'spot') -> Optional[float]:
        """获取特定交易对的价格"""
        try:
            market_data = self.get_market_data(symbol)
            if market_data:
                price_key = f"{market_type}_price"
                return market_data.get(price_key)
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取价格失败: {str(e)}")
        return None

    def get_all_market_data(self) -> Dict[str, Dict]:
        """获取所有市场数据"""
        return self.market_manager.get_all_market_data(self.exchange_name)

    def get_symbols(self) -> List[str]:
        """获取所有交易对列表"""
        return self.market_manager.get_symbols(self.exchange_name)
