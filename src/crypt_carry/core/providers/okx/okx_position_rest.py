import asyncio
import logging
from typing import Dict, List

import ccxt.pro as ccxtpro

from crypt_carry.core.providers.okx.okx_position_utils import standardize_position_data
from crypt_carry.core.providers.okx.okx_symbol_utils import to_exchange_instrument_id, to_standard_symbol
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager
from crypt_carry.utils.retry import async_retry

logger = logging.getLogger(__name__)


class OkxPositionRest:
    def __init__(self, exchange: ccxtpro.okx, exchange_name: str, market_limit_manager: MarketLimitManager,
                 all_pairs: list[str],
                 base_pair: list[str],
                 quotes: list[str]):
        self.exchange: ccxtpro.okx = exchange
        self.exchange_name = exchange_name
        self.market_limit_manager = market_limit_manager
        self.all_pairs = all_pairs
        self.base_pair = base_pair
        self.quotes = quotes
        self.positions_cache = []
        self.balances_cache = {}
        self.last_positions_update = 0
        self.last_balances_update = 0
        self.is_running = False
        self.position_monitor_task = None

    @async_retry(max_retries=3, initial_delay=1.0, backoff_factor=2.0)
    async def _fetch_positions_batch(self, batch: list[str]) -> list[dict]:
        """获取一批合约持仓信息
        
        Args:
            batch: 一批合约交易对列表
            
        Returns:
            List[Dict]: 该批次的持仓列表
        """
        logger.debug(f"{self.exchange_name} 正在查询一批合约持仓，共{len(batch)}个交易对")
        positions = await self.exchange.fetch_positions(batch)
        return positions

    async def fetch_positions_rest(self, symbols: list[str]) -> list[dict]:
        """获取当前持仓数据
        
        Args:
            symbols: 交易对列表，如 [BTC/USDT, ETH/USDT]。如果为 None，则获取所有交易对的持仓。
            
        Returns:
            list[dict]: 持仓列表，每个持仓包含：
            {
                'symbol': str,      # 交易对
                'side': str,        # 方向，LONG 或 SHORT
                'is_spot': bool,    # 是否是现货
                'contracts': float, # 合约张数
                'amount': float,    # 实际数量
                'leverage': float,  # 杠杆倍数
                'entryPrice': float # 开仓均价
            }
        """
        try:
            # 准备查询的现货和合约交易对
            spot_symbols = []
            future_symbols = []

            if symbols:
                # 如果指定了具体交易对，只查询该交易对
                for symbol in symbols:
                    standard_future_symbol = to_standard_symbol(symbol)
                    future_symbol = to_exchange_instrument_id(standard_future_symbol, is_spot=False)
                    spot_symbols.append(standard_future_symbol)
                    future_symbols.append(future_symbol)
            else:
                # 否则查询所有基础交易对
                for base_symbol in self.base_pair:
                    standard_future_symbol = to_standard_symbol(base_symbol)
                    future_symbol = to_exchange_instrument_id(standard_future_symbol, is_spot=False)
                    spot_symbols.append(standard_future_symbol)
                    future_symbols.append(future_symbol)

            # 去重
            spot_symbols = list(set(spot_symbols))
            future_symbols = list(set(future_symbols))

            # 初始化结果列表
            all_positions = []

            # 获取合约持仓信息
            # OKX API限制：一次最多查询10个交易对
            # 将future_symbols分批处理，每批最多10个
            all_futures_positions = []
            if future_symbols:
                batch_size = 10  # OKX API限制
                # 按批次处理
                for i in range(0, len(future_symbols), batch_size):
                    batch = future_symbols[i:i + batch_size]
                    logger.debug(f"正在查询第{i // batch_size + 1}批合约持仓，共{len(batch)}个交易对")
                    batch_positions = await self._fetch_positions_batch(batch)
                    all_futures_positions.extend(batch_positions)
                    await asyncio.sleep(0.1)
            # 处理和标准化持仓数据
            for position in all_futures_positions:
                # 只处理有效持仓
                contracts = float(position.get('contracts', 0))
                if contracts <= 0:
                    continue

                # 将OKX特定格式转换为标准格式
                side = 'LONG' if position.get('side') == 'long' else 'SHORT'
                standardized_position = {
                    'symbol': position.get('symbol'),
                    'side': side,
                    'is_spot': False,
                    'contracts': contracts,
                    'leverage': float(position.get('leverage', 1)),
                    'entryPrice': float(position.get('entryPrice', 0))
                }

                # 添加CCXT标准化的风险相关字段
                # liquidationPrice: 清算价格
                # marginRatio: 保证金比率
                # marginMode: 保证金模式
                # markPrice: 标记价格
                # maintenanceMargin: 维持保证金
                # initialMargin: 初始保证金
                risk_fields = ['liquidationPrice', 'marginRatio', 'marginMode', 'markPrice',
                               'maintenanceMargin', 'initialMargin']
                for field in risk_fields:
                    if field in position:
                        standardized_position[field] = position.get(field)

                # 处理可能不存在的风险字段，从原始info中提取
                if 'info' in position:
                    info = position['info']

                    # 处理清算价格
                    if 'liquidationPrice' not in standardized_position and 'liqPx' in info:
                        standardized_position['liquidationPrice'] = float(info.get('liqPx', 0))

                    # 处理保证金率
                    if 'marginRatio' not in standardized_position and 'mgnRatio' in info:
                        standardized_position['marginRatio'] = float(info.get('mgnRatio', 0))

                    # 处理标记价格
                    if 'markPrice' not in standardized_position and 'markPx' in info:
                        standardized_position['markPrice'] = float(info.get('markPx', 0))

                    # 处理保证金模式
                    if 'marginMode' not in standardized_position and 'mgnMode' in info:
                        standardized_position['marginMode'] = info.get('mgnMode', '')

                    # 标准化持仓数据，添加amount字段
                    standardized_position = standardize_position_data(standardized_position,
                                                                      self.market_limit_manager)

                    all_positions.append(standardized_position)

            # 获取现货账户数据，将其转换为持仓格式
            # 注意：对于现货，我们只能获得账户余额，没有杠杆等信息
            balances = await self.fetch_balances_with_cost_price(spot_symbols)

            # 将现货余额转化为持仓格式
            for symbol, balance in balances.items():
                # 跳过计价货币
                if symbol in self.quotes:
                    continue

                # 获取交易对的内部符号和标准符号
                standard_symbol = to_standard_symbol(symbol)

                # 获取该交易对的最小交易量限制
                market_limit = self.market_limit_manager.get_market_limit(standard_symbol)

                # 只有当持仓大于最小交易量时才记录
                # 如果无法获取市场限制，则使用一个小的默认值
                min_trading_amount = market_limit.min_amount if market_limit else 0.0001

                # 只处理有效余额的币种
                if abs(balance['balance']) < min_trading_amount:
                    continue

                # 构建标准化持仓数据
                standardized_position = {
                    'symbol': to_standard_symbol(symbol),  # 确保使用标准格式
                    'side': 'LONG',  # 现货只有多头
                    'is_spot': True,
                    'contracts': balance['balance'],  # 现货中contracts与amount相同
                    'amount': balance['balance'],  # 现货中amount与contracts相同
                    'leverage': 1,  # 现货默认杠杆为1
                    'entryPrice': balance['cost_price']  # 现货没有开仓价格概念
                }

                all_positions.append(standardized_position)

            logger.debug(f"{self.exchange_name} 查询持仓成功: {all_positions}")
            return all_positions

        except Exception as e:
            error_msg = f"{self.exchange_name} 查询持仓失败: {str(e)}"
            logger.error(error_msg)
            raise

    async def fetch_balances(self, symbols: list[str] = None) -> dict[str, float]:
        """查询当前现货余额
        
        Args:
            symbols: 币种列表（可选，如果不指定则查询所有币种）
            
        Returns:
            dict[str, float]: 币种余额字典，key为币种，value为余额
        """
        try:
            # 调用CCXT的fetchBalance方法
            # 注意：现货余额查询通常一次性返回所有余额，不需要分批
            balances = await self.exchange.fetch_balance()
            logger.debug(f"{self.exchange_name}成功获取余额信息")

            total_balances = balances.get('total', {})

            # 如果指定了币种，则只返回指定币种的余额
            if symbols:
                filtered_balances = {}
                for standard_symbol in symbols:
                    base = standard_symbol.split('/')[0]
                    if base in total_balances:
                        filtered_balances[standard_symbol] = total_balances[base]
                return filtered_balances

            # 否则返回所有余额
            return {k: v for k, v in total_balances.items() if v > 0}

        except Exception as e:
            logger.error(f"{self.exchange_name} 查询余额失败: {str(e)}")
            # 出错时返回空字典，避免影响后续逻辑
            return {}

    async def fetch_balances_with_cost_price(self, symbols: list[str] = None) -> dict[str, dict]:
        """查询当前现货余额及成本价，支持分批处理
        
        Args:
            symbols: 币种列表（可选，如果不指定则查询所有币种）
            
        Returns:
            dict[str, dict]: 币种余额和成本价字典，格式为 {币种: {'balance': 余额, 'cost_price': 成本价}}
        """
        try:
            result = {}

            # 分批处理，每批最多20个币种
            batch_size = 20
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                logger.debug(
                    f"{self.exchange_name} 正在获取第 {i // batch_size + 1} 批币种成本价，币种: {batch_symbols}")

                # 构建请求参数
                params = {}

                # 将标准符号转换为OKX格式的币种列表
                currencies = []
                for standard_symbol in batch_symbols:
                    if '/' in standard_symbol:  # 如果是标准符号格式 (例如 BTC/USDT)
                        base = standard_symbol.split('/')[0]
                        currencies.append(base)
                    else:  # 如果已经是币种格式 (例如 BTC)
                        currencies.append(standard_symbol)

                if currencies:
                    params['ccy'] = ','.join(currencies)

                # 调用OKX的账户余额API
                batch_response = await self.exchange.privateGetAccountBalance(params)

                if not batch_response or 'data' not in batch_response or not batch_response['data']:
                    logger.warning(
                        f"{self.exchange_name} 获取第 {i // batch_size + 1} 批币种账户余额及成本价失败: 返回数据为空")
                    continue

                data = batch_response['data']

                # 处理返回的数据
                for account_data in data:
                    details = account_data.get('details', [])
                    for detail in details:
                        currency = detail.get('ccy')
                        if not currency:
                            continue

                        # 获取余额
                        balance = float(detail.get('cashBal', 0))
                        if balance <= 0:
                            continue

                        # 获取成本价 (openAvgPx字段)
                        cost_price = 0.0
                        open_avg_px = detail.get('openAvgPx')
                        if open_avg_px:
                            try:
                                cost_price = float(open_avg_px)
                            except (ValueError, TypeError):
                                logger.warning(f"{self.exchange_name} 无法解析{currency}的成本价: {open_avg_px}")

                        # 如果是标准符号格式，转换回标准符号
                        for standard_symbol in batch_symbols:
                            base = standard_symbol.split('/')[0]
                            if base == currency:
                                result[standard_symbol] = {
                                    'balance': balance,
                                    'cost_price': cost_price
                                }

                # 添加适当的延迟，避免API请求过于频繁
                if i + batch_size < len(symbols):
                    await asyncio.sleep(0.5)

            return result

        except Exception as e:
            logger.error(f"{self.exchange_name} 查询余额及成本价失败: {str(e)}")
            # 出错时返回空字典，避免影响后续逻辑
            return {}
