"""
OKX WebSocket持仓监控 - 使用ccxt pro实时获取合约和现货持仓数据
"""
import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Callable, Any
import ccxt.pro as ccxtpro

from crypt_carry.core.providers.okx.okx_symbol_utils import build_internal_symbol, to_internal_symbol, \
    to_standard_symbol
from crypt_carry.core.providers.okx.okx_position_utils import standardize_position_data
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager
from crypt_carry.core.providers.okx.okx_symbol_utils import to_exchange_instrument_id

logger = logging.getLogger(__name__)


class OkxPositionWebsocket:
    """OKX WebSocket持仓监控器，通过ccxt pro实时获取合约和现货持仓数据"""

    def __init__(self, exchange: ccxtpro.okx, market_limit_manager: MarketLimitManager,
                 all_pairs: List[str],
                 base_pair: List[str],
                 quotes: List[str]):
        """初始化OKX WebSocket持仓监控器
        
        Args:
            exchange: ccxt pro的OKX实例
            market_limit_manager: 市场限制管理器实例
            quotes: 计价货币列表
        """
        self.exchange = exchange
        self.market_limit_manager = market_limit_manager

        # 缓存最新的持仓数据
        self.positions_cache = []
        self.balances_cache = {}

        # 最后更新时间戳
        self.last_positions_update = 0
        self.last_balances_update = 0

        # WebSocket连接状态
        self.is_running = False
        self.all_pairs = all_pairs
        self.base_pair = base_pair
        self.quotes = quotes  # 计价货币列表

        # 监控任务
        self.position_monitor_task = None
        self.balance_monitor_task = None

    async def start(self, quotes: List[str] = None):
        """启动WebSocket连接和订阅
        
        Args:
            quotes: 计价货币列表，如 ["USDT"]
        """
        if quotes:
            self.quotes = quotes

        self.is_running = True
        logger.info("启动OKX WebSocket持仓监控器")

        # 创建监听任务
        self.position_monitor_task = asyncio.create_task(self._monitor_positions())
        self.balance_monitor_task = asyncio.create_task(self._monitor_balances())

    async def stop(self):
        """停止WebSocket连接和订阅"""
        self.is_running = False
        logger.info("停止OKX WebSocket持仓监控器")

        # 取消所有任务
        if self.position_monitor_task:
            self.position_monitor_task.cancel()

        if self.balance_monitor_task:
            self.balance_monitor_task.cancel()

        # 关闭交易所实例
        if self.exchange:
            await self.exchange.close()
            self.exchange = None

    async def _monitor_positions(self):
        """监控持仓更新"""
        while self.is_running:
            try:
                # 使用ccxt pro的watchPositions方法监听持仓更新
                self.exchange.options['defaultType'] = 'swap'
                # self.exchange.options['watchPositions'] = {
                #     'fetchParams': {
                #         'updateInterval': '1000'  # 1秒更新频率
                #     }
                # }
                positions = await self.exchange.watch_positions()
                await self._handle_position_update(positions)

            except Exception as e:
                if self.is_running:
                    logger.error(f"监控OKX持仓数据出错: {str(e)}")
                    await asyncio.sleep(5)  # 出错后等待5秒再试

    async def _monitor_balances(self):
        """监控账户余额更新"""
        while self.is_running:
            try:
                # 使用ccxt pro的watchBalance方法监听余额更新
                # 先切换到现货账户
                self.exchange.options['defaultType'] = 'spot'
                # self.exchange.options['watchBalance'] = {
                #     'fetchParams': {
                #         'updateInterval': '1000'  # 1秒更新频率 
                #     }
                # }
                spot_balance = await self.exchange.watch_balance()
                await self._handle_balance_update(spot_balance)
                # await asyncio.sleep(1)  # 设定 1 秒的间隔，避免高频处理
            except Exception as e:
                if self.is_running:
                    logger.error(f"监控OKX账户余额出错: {str(e)}")
                    await asyncio.sleep(5)  # 出错后等待5秒再试

    async def _handle_position_update(self, positions):
        """处理持仓更新
        
        Args:
            positions: ccxt格式的持仓数据，可能是列表或字典
        """
        try:
            # 更新时间戳
            self.last_positions_update = time.time()

            # 处理持仓数据
            all_positions = []

            # 检查positions的类型，并相应处理
            if isinstance(positions, list):
                # 如果是列表，直接遍历
                positions_list = positions
            elif isinstance(positions, dict):
                # 如果是字典，提取所有position列表
                positions_list = []
                for symbol_positions in positions.values():
                    if isinstance(symbol_positions, list):
                        positions_list.extend(symbol_positions)
                    else:
                        positions_list.append(symbol_positions)
            else:
                logger.warning(f"未知的持仓数据格式: {type(positions)}")
                return

            logger.debug(f"获取到持仓数据: {json.dumps(positions_list)}")
            spot_symbols = []
            future_symbols = []
            for base_symbol in self.base_pair:
                standard_symbol = to_standard_symbol(base_symbol)
                future_symbol = to_exchange_instrument_id(standard_symbol, is_spot=False)
                spot_symbols.append(standard_symbol)
                future_symbols.append(future_symbol)

            # 处理每个持仓
            for position in positions_list:
                # 只处理有效持仓
                amount = position.get('contracts', 0)
                if amount == 0:
                    continue

                # 获取持仓方向
                side = position.get('side', '').upper()  # ccxt统一使用'long'或'short'

                # 获取交易对
                symbol = position.get('symbol', '')
                # standard_symbol = to_standard_symbol(symbol)

                # 构建标准化持仓数据
                standardized_position = {
                    'symbol': symbol,
                    'side': side.upper(),
                    'is_spot': False,
                    'contracts': abs(amount),
                    'leverage': float(position.get('leverage', 1)),
                    'entryPrice': float(position.get('entryPrice', 0)),
                    'liquidationPrice': float(position.get('liquidationPrice', 0)),
                    'marginRatio': float(position.get('marginRatio', 0)),
                    'marginMode': position.get('marginMode', ''),  # cross or isolated
                    'markPrice': float(position.get('markPrice', 0)),
                }

                # 标准化持仓数据
                if self.market_limit_manager:
                    standardized_position = standardize_position_data(standardized_position, self.market_limit_manager)

                all_positions.append(standardized_position)

            # 更新缓存
            self.positions_cache = all_positions
            logger.info(f"已更新 {len(all_positions)} 个合约持仓的缓存")

        except Exception as e:
            logger.error(f"处理持仓更新数据出错: {str(e)}")

    async def _handle_balance_update(self, balances):
        """处理账户余额更新
        
        Args:
            balances: ccxt格式的余额数据
        """
        try:
            if 'total' not in balances:
                return

            # 更新时间戳
            self.last_balances_update = time.time()
            logger.debug(f"处理账户余额更新数据: {json.dumps(balances)}")

            spot_symbols = []
            future_symbols = []
            for base_symbol in self.all_pairs:
                standard_symbol = to_standard_symbol(base_symbol)
                future_symbol = to_exchange_instrument_id(standard_symbol, is_spot=False)
                spot_symbols.append(standard_symbol)
                future_symbols.append(future_symbol)

            # 处理余额数据
            new_balances = {}

            for currency, amount_info in balances['total'].items():
                # 跳过计价货币
                if currency in self.quotes:
                    continue

                # 获取总余额 (可用+冻结)
                total_amount = float(amount_info) if isinstance(amount_info, (int, float, str)) else 0

                for standard_symbol in spot_symbols:
                    base = standard_symbol.split('/')[0]
                    if currency == base:
                        market_limit = self.market_limit_manager.get_market_limit(standard_symbol)
                        # 如果无法获取市场限制，则使用一个小的默认值
                        min_trading_amount = market_limit.min_amount if market_limit else 0.0001

                        # 只处理有效余额的币种
                        if abs(total_amount) < min_trading_amount:
                            continue

                        new_balances[standard_symbol] = total_amount

            # 更新缓存
            self.balances_cache = new_balances
            logger.info(f"已更新 {len(new_balances)} 个币种的余额缓存")

        except Exception as e:
            logger.error(f"处理账户余额更新消息出错: {str(e)}")

    async def get_positions(self) -> List[Dict]:
        """获取最新的持仓数据
        
        Returns:
            List[Dict]: 标准化格式的持仓数据列表
        """
        # 检查缓存是否有效（5分钟内更新的数据视为有效）
        if time.time() - self.last_positions_update > 300:
            logger.warning("持仓数据缓存已过期或未初始化, 强制更新")
            try:
                # 强制获取一次持仓数据
                positions = await self.exchange.watch_positions()
                await self._handle_position_update(positions)

            except Exception as e:
                logger.error(f"强制更新持仓数据失败: {str(e)}")

        # 处理现货持仓
        spot_positions = []
        spot_balances = await self.get_balances()

        for symbol, balance in spot_balances.items():
            if balance <= 0:
                continue

            standardized_position = {
                'symbol': to_standard_symbol(symbol),
                'side': 'LONG',  # 现货只有多头
                'is_spot': True,
                'contracts': balance,  # 现货中contracts与amount相同
                'amount': balance,  # 现货中amount与contracts相同
                'leverage': 1,  # 现货默认杠杆为1
                'entryPrice': 0  # 现货没有开仓价格概念
            }

            spot_positions.append(standardized_position)

        # 合并合约和现货持仓
        all_positions = self.positions_cache + spot_positions
        return all_positions

    async def get_balances(self) -> Dict[str, float]:
        """获取最新的余额数据
        
        Returns:
            Dict[str, float]: 币种余额字典，格式为 {symbol: amount}
        """
        # 检查缓存是否有效（5分钟内更新的数据视为有效）
        if time.time() - self.last_balances_update > 300:
            logger.warning("余额数据缓存已过期或未初始化，尝试强制更新")
            try:
                # 强制获取一次余额数据
                self.exchange.options['defaultType'] = 'spot'
                balances = await self.exchange.fetch_balance()
                await self._handle_balance_update(balances)

            except Exception as e:
                logger.error(f"强制更新余额数据失败: {str(e)}")

        return self.balances_cache
