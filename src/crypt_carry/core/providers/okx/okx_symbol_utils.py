"""OKX 交易对格式转换工具

内部统一格式:
- 现货: BTC/USDT
- 合约: BTC/USDT:USDT

OKX格式:
- 现货: BTC-USDT
- 合约: BTC-USDT-SWAP
"""
import logging
from typing import Optional

logger = logging.getLogger(__name__)

"""OKX交易对工具函数"""


def to_standard_symbol(symbol: str) -> str:
    """将任意格式的交易对转换为标准格式（不带:USDT后缀）
    
    Args:
        symbol: 交易对，支持以下格式：
               - 现货格式：BTC/USDT
               - 合约格式：BTC/USDT:USDT
               
    Returns:
        str: 标准格式的交易对，如 BTC/USDT
    """
    return symbol.split(':')[0] if ':' in symbol else symbol


def to_internal_symbol(symbol: str) -> str:
    """将OKX的交易对格式转换为内部标准格式
    
    Args:
        symbol: OKX的交易对格式，如:
            - 现货: BTC-USDT
            - 合约: BTC-USDT-SWAP
            
    Returns:
        str: 内部标准格式
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
    """
    try:
        # 已经是标准格式
        if '/' in symbol:
            return symbol

        # 分割交易对
        parts = symbol.split('-')

        # 处理合约格式 BTC-USDT-SWAP
        if len(parts) == 3 and parts[2] == 'SWAP':
            base, quote = parts[0], parts[1]
            return f"{base}/{quote}:{quote}"

        # 处理现货格式 BTC-USDT
        elif len(parts) == 2:
            base, quote = parts
            return f"{base}/{quote}"

        logger.warning(f"无法识别的OKX交易对格式: {symbol}")
        return symbol
    except Exception as e:
        logger.error(f"转换OKX交易对格式失败: {symbol}, {str(e)}")
        return symbol


def to_exchange_instrument_id(symbol: str, is_spot: bool = True) -> str:
    """将内部标准格式转换为OKX交易对格式
    
    Args:
        symbol: 内部标准格式，如:
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
        is_spot: 是否为现货交易对
            
    Returns:
        str: OKX交易对格式
            - 现货: BTC-USDT
            - 合约: BTC-USDT-SWAP
    """
    try:
        # 已经是OKX格式
        if '-' in symbol:
            return symbol

        # 处理合约格式 BTC/USDT:USDT
        if ':' in symbol:
            base = symbol.split('/')[0]
            quote = symbol.split(':')[0].split('/')[1]
            return f"{base}-{quote}-SWAP"

        # 处理现货格式 BTC/USDT
        if '/' in symbol:
            base, quote = symbol.split('/')
            if is_spot:
                return f"{base}-{quote}"
            return f"{base}-{quote}-SWAP"

        logger.warning(f"无法识别的内部交易对格式: {symbol}")
        return symbol
    except Exception as e:
        logger.error(f"转换为OKX交易对格式失败: {symbol}, {str(e)}")
        return symbol


def build_internal_symbol(base: str, quote: str, is_futures: bool = False) -> str:
    """根据基础货币和计价货币构建内部统一格式的交易对
    
    Args:
        base: 基础货币，如 BTC
        quote: 计价货币，如 USDT
        is_futures: 是否为合约交易对
            
    Returns:
        str: 内部统一格式
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
    """
    try:
        spot_symbol = f"{base}/{quote}"
        if is_futures:
            return f"{spot_symbol}:{quote}"
        return spot_symbol
    except Exception as e:
        logger.error(f"构建内部统一格式失败: base={base}, quote={quote}, is_futures={is_futures}, {str(e)}")
        return ""
