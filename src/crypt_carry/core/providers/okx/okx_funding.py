"""OKX资金费率管理器"""
import logging
from datetime import datetime
from typing import List, Optional

import aiohttp

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_cache import FundingRateCache
from crypt_carry.core.exchange.funding.funding_rate_manager import FundingRateManager
from crypt_carry.core.providers.okx.okx_symbol_utils import to_exchange_instrument_id, to_standard_symbol
from crypt_carry.utils.retry import async_retry
from util.log_throttler import LogThrottler

logger = logging.getLogger(__name__)

class OkxFundingManager(FundingRateManager):
    """OKX资金费率管理器"""

    def __init__(self, config: ExchangeConfig, perpetual_pairs: List[str]):
        """初始化OKX资金费率管理器
        
        Args:
            config: 交易所配置
            perpetual_pairs: 永续合约交易对列表，内部标准格式，如 ["BTC/USDT", "ETH/USDT"]
        """
        super().__init__(config)
        # 将交易对转换为内部标准格式
        self.perpetual_pairs = perpetual_pairs
        self.cache = FundingRateCache()

        # 初始化日志频率控制器
        self.log_throttler = LogThrottler.get_instance(f"{self.exchange_name}_okx_funding_manager")
        self._5m_log_key = f"{self.exchange_name}_fetch_latest_funding_rates"
        self.log_throttler.set_interval(self._5m_log_key, 5 * 60)

    async def initialize(self):
        """异步初始化方法"""
        logger.debug(f"{self.exchange_name} 初始化资金费率管理器")
        await self.init_funding_rates(self.perpetual_pairs)

    def get_current_rate(self, symbol: str) -> Optional[float]:
        """获取当前资金费率"""
        try:
            return self.cache.get_funding_rate(self.exchange_name, symbol)
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取当前资金费率失败: {str(e)}")
            return None

    def get_settlement_interval(self, symbol: str) -> Optional[int]:
        """获取交易对的结算周期（小时）
        
        Args:
            symbol: 交易对，内部标准格式
            
        Returns:
            Optional[int]: 结算周期（小时），如 2/4/8 小时，如果未知则返回 None
        """
        standard_symbol = to_standard_symbol(symbol)
        return self.cache.get_settlement_interval(self.exchange_name, standard_symbol)

    @async_retry(max_retries=5, initial_delay=1.0)
    async def fetch_historical_rates(self, symbol: str) -> None:
        """获取历史资金费率数据
        
        Args:
            symbol: 交易对，内部标准格式，如 BTC/USDT
        """
        try:
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None
            logger.debug(f"代理设置: {proxy_url}, symbol:{symbol}")

            async with aiohttp.ClientSession() as session:
                # 转换为OKX API格式
                instrument_id = to_exchange_instrument_id(symbol, is_spot=False)
                logger.debug(f"获取 {self.exchange_name} {symbol} 历史资金费率, 请求symbol: {instrument_id}")

                url = f'{self.config.base_config["API_URLS"]["OKX"]["REST"]}/public/funding-rate-history'
                params = {
                    'instId': instrument_id,
                    'limit': 50
                }

                headers = {}

                async with session.get(url, params=params, headers=headers, proxy=proxy_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('code') == '0':
                            rates = []
                            for item in data['data']:
                                timestamp = int(item['fundingTime'])
                                rate = float(item['realizedRate'])
                                rates.append({
                                    'timestamp': timestamp,
                                    'rate': rate
                                })

                            # 计算结算周期
                            settlement_interval = self._calculate_settlement_interval_from_rates(rates)

                            # 为每条记录添加结算周期
                            for rate in rates:
                                rate['settlement_interval'] = settlement_interval

                            # 更新缓存
                            standard_symbol = to_standard_symbol(symbol)
                            self.cache.update_funding_rates(self.exchange_name, standard_symbol, rates)
                            logger.debug(
                                f"{self.exchange_name} {symbol} 更新历史资金费率成功: {len(rates)}条, 结算周期: {settlement_interval}小时")
                        else:
                            error_msg = data.get('msg', '未知错误')
                            logger.error(f"{self.exchange_name} {symbol} 获取历史资金费率失败: {data}")
                            raise Exception(f"API错误: {error_msg}")
                    else:
                        logger.error(f"{self.exchange_name} {symbol} 获取历史资金费率失败: HTTP {response.status}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP 错误 {response.status}",
                            headers=response.headers
                        )
                        
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 获取历史资金费率失败: {str(e)}")
            raise

    @async_retry(max_retries=3, initial_delay=3.0)
    async def fetch_latest_funding_rates(self, symbol: str):
        """获取最新资金费率数据
        
        Args:
            symbol: 交易对
        """
        try:
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None

            async with aiohttp.ClientSession() as session:
                url = f'{self.config.base_config["API_URLS"]["OKX"]["REST"]}/public/funding-rate'

                # 转换为OKX API格式
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=False)
                logger.debug(f"获取 {self.exchange_name} {symbol} 最新资金费率, 请求symbol: {instrument_id}")

                params = {'instId': instrument_id}

                async with session.get(url, params=params, proxy=proxy_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        standard_symbol = to_standard_symbol(symbol)
                        rate = 0.0
                        current_timestamp = int(datetime.now().timestamp() * 1000)
                        next_funding_time = current_timestamp + (8 * 60 * 60 * 1000)

                        # 从缓存中获取结算周期，如果不存在则默认为8小时
                        settlement_interval = self.cache.get_settlement_interval(self.exchange_name, standard_symbol)
                        if settlement_interval is None:
                            settlement_interval = 8  # 默认8小时
                            logger.debug(
                                f"{self.exchange_name} {symbol} 缓存中无结算周期数据，使用默认值: {settlement_interval}小时")
                        
                        if data.get('code') == '0':
                            # 检查data列表是否为空
                            if not data.get('data') or len(data['data']) == 0:
                                logger.warning(f"{self.exchange_name} {symbol} 获取最新资金费率返回空数据，可能是新上线交易对")
                            else:
                                item = data['data'][0]
                                rate = float(item['fundingRate'])
                                timestamp = int(item['fundingTime'])
                                next_funding_time = int(item['nextFundingTime'])
                            # 更新缓存
                            self.cache.update_funding_rate(
                                exchange=self.exchange_name,
                                standard_symbol=standard_symbol,
                                rate=rate,
                                timestamp=timestamp,
                                next_funding_time=next_funding_time,
                                settlement_interval=settlement_interval
                            )

                            message = f"{self.exchange_name} {symbol} 更新最新资金费率成功: {rate}, 结算周期: {settlement_interval}小时"
                            self.log_throttler.log_if_allowed(logger_obj=logger, key=self._5m_log_key, message=message)
                        else:
                            error_msg = data.get('msg', '未知错误')
                            logger.error(f"{self.exchange_name} {symbol} 获取最新资金费率失败: {error_msg}")
                            raise Exception(f"API错误: {error_msg}")
                    else:
                        logger.error(f"{self.exchange_name} {symbol} 获取最新资金费率失败: HTTP {response.status}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP 错误 {response.status}",
                            headers=response.headers
                        )

        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 获取最新资金费率失败: {e}")
            raise

    def _calculate_settlement_interval_from_rates(self, rates):
        """从资金费率数据计算结算周期

        Args:
            rates: 资金费率数据列表

        Returns:
            int: 结算周期（小时），默认为8小时
        """
        # 如果历史数据少于2条，无法计算间隔，默认返回8小时
        if len(rates) < 2:
            return 8

        # 只计算最近两条数据之间的间隔
        current_ts = rates[0]['timestamp']
        previous_ts = rates[1]['timestamp']
        interval_ms = abs(current_ts - previous_ts)  # 使用绝对值避免时间顺序问题
        interval_hours = round(interval_ms / (60 * 60 * 1000))

        # 标准化为2/4/8小时
        if interval_hours <= 3:
            return 2
        elif interval_hours <= 6:
            return 4
        else:
            return 8
