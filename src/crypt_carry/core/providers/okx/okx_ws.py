import asyncio
import json
import logging
from typing import Callable, Set, List

import websockets

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.providers.okx.okx_symbol_utils import to_exchange_instrument_id
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class OkxWebSocket:
    def __init__(self, config: ExchangeConfig, message_handler: Callable = None):
        """初始化WebSocket客户端"""
        self.ws = None
        self.config = config
        self.message_handler = message_handler
        self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"  # OKX公共WebSocket地址
        self.is_connected = False
        self.spot_pairs: Set[str] = set()
        self.perpetual_pairs: Set[str] = set()
        self.tasks = []
        self._lock = asyncio.Lock()
        # 从配置中获取代理设置
        self.proxy = config.http_proxy if hasattr(config, 'http_proxy') else None
        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()


    async def connect(self):
        """建立WebSocket连接"""
        try:
            # 添加超时设置，防止连接过程无限等待
            connect_kwargs = {
                'ping_interval': 20,  # 定期发送ping来保持连接
                'ping_timeout': 10,  # ping超时时间
                'close_timeout': 10,  # 关闭连接的超时时间
                'max_size': 10 * 1024 * 1024,  # 允许的最大消息大小
                'max_queue': 1000,  # 连接队列大小
                'open_timeout': 20  # 连接建立的超时时间
            }
            
            if self.proxy:
                logger.info(f"{self.config.exchange_name} 使用代理连接: {self.proxy}")
                self.ws = await asyncio.wait_for(
                    websockets.connect(self.ws_url, proxy=self.proxy, **connect_kwargs),
                    timeout=30
                )
            else:
                logger.info(f"{self.config.exchange_name} 直接连接")
                self.ws = await asyncio.wait_for(
                    websockets.connect(self.ws_url, **connect_kwargs),
                    timeout=30
                )
            self.is_connected = True
            logger.info(f"{self.config.exchange_name} WebSocket连接成功")
            return True
        except asyncio.TimeoutError:
            logger.error(f"{self.config.exchange_name} WebSocket连接超时")
            self.is_connected = False
            # 向钉钉发送告警
            await self.ding_talk_manager.send_message(f"{self.config.exchange_name} WebSocket连接超时")
            raise
        except Exception as e:
            logger.error(f"{self.config.exchange_name} WebSocket连接失败: {str(e)}")
            self.is_connected = False
            # 向钉钉发送告警
            await self.ding_talk_manager.send_message(f"{self.config.exchange_name} WebSocket连接失败: {str(e)}")
            raise

    async def subscribe_market_data(self, spot_pairs: List[str], perpetual_pairs: List[str]) -> bool:
        """订阅市场数据，支持重复调用以更新订阅列表"""
        try:
            # 如果已有任务在运行，先取消
            for task in self.tasks:
                if not task.done():
                    task.cancel()
            self.tasks.clear()
            
            # 转换为交易所格式并存储
            new_spot_pairs = {to_exchange_instrument_id(symbol, is_spot=True) for symbol in spot_pairs}
            new_perpetual_pairs = {to_exchange_instrument_id(symbol, is_spot=False) for symbol in perpetual_pairs}

            # 更新存储的交易对
            self.spot_pairs = new_spot_pairs
            self.perpetual_pairs = new_perpetual_pairs
            
            logger.info(
                f"{self.config.exchange_name} 将订阅 {len(self.spot_pairs)} 个现货交易对和 {len(self.perpetual_pairs)} 个永续合约")

            # 构建订阅消息
            channels = (
                    [{"channel": "bbo-tbt", "instId": pair} for pair in self.spot_pairs] +
                    [{"channel": "bbo-tbt", "instId": pair} for pair in self.perpetual_pairs]
            )

            if not channels:
                logger.warning(f"{self.config.exchange_name} 没有交易对需要订阅")
                return True
                
            sub_msg = {
                "op": "subscribe",
                "args": channels
            }

            # 如果连接不存在或已断开，则创建新连接
            if not self.is_connected or not self.ws:
                logger.info(f"{self.config.exchange_name} 连接不存在或已断开，正在创建新连接...")
                await self.connect()

            # 发送订阅请求
            await self.ws.send(json.dumps(sub_msg))
            logger.info(f"{self.config.exchange_name} 已发送订阅请求，订阅 {len(channels)} 个交易对")

            # 如果没有监听任务，则启动一个
            if not self.tasks:
                self.tasks.append(asyncio.create_task(self._listen()))
            
            return True
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 订阅市场数据失败: {str(e)}")
            return False

    async def _listen(self):
        """监听WebSocket消息"""
        while self.is_connected:
            try:
                # 使用锁确保同一时间只有一个协程调用recv
                async with self._lock:
                    message = await self.ws.recv()
                data = json.loads(message)

                # 忽略订阅确认消息
                if 'event' in data and data['event'] == 'subscribe':
                    continue

                # 处理BBO更新
                if 'arg' in data and data['arg'].get('channel') == 'bbo-tbt':
                    await self._handle_bbo_update(data)
            except websockets.ConnectionClosed:
                logger.warning(f"{self.config.exchange_name} WebSocket连接断开，正在重连...")
                # 保存当前的交易对，避免使用可能已损坏的实例变量
                current_spot_pairs = list(self.spot_pairs)
                current_perpetual_pairs = list(self.perpetual_pairs)

                # 重置连接标志，确保不会导致递归问题
                self.is_connected = False

                # 创建一个新的任务来处理重连，避免在当前上下文中直接重连
                asyncio.create_task(self._reconnect(current_spot_pairs, current_perpetual_pairs, new_task=False))
                # 退出当前监听循环
                break
            except Exception as e:
                logger.error(f"{self.config.exchange_name} WebSocket消息处理错误: {str(e)}")
                await asyncio.sleep(1)

    async def _handle_bbo_update(self, data):
        """处理BBO更新"""
        symbol = data['arg']['instId']
        update_data = data['data'][0]

        # 提取最优买价和卖价
        best_bid = float(update_data['bids'][0][0]) if update_data['bids'] else None
        best_ask = float(update_data['asks'][0][0]) if update_data['asks'] else None
        timestamp = int(update_data['ts'])
        is_spot = symbol in self.spot_pairs

        # 构建BBO数据
        bbo_data = {
            'symbol': symbol,
            'timestamp': timestamp,
            'last': best_bid if is_spot else best_ask
        }

        # 调用消息处理器
        if self.message_handler:
            await self.message_handler(bbo_data)

    async def _reconnect(self, spot_pairs: List[str], perpetual_pairs: List[str], new_task: bool = True):
        """重新连接并恢复订阅
        
        Args:
            spot_pairs: 现货交易对列表
            perpetual_pairs: 合约交易对列表
            new_task: 是否在新任务中执行重连，默认为True。当从_listen方法中调用时设为False
        """
        logger.info("*" * 50)
        # 当需要在新任务中执行时，创建一个新任务并返回
        if new_task:
            logger.info(f"{self.config.exchange_name} 创建新任务执行重连...")
            asyncio.create_task(self._reconnect(spot_pairs, perpetual_pairs, new_task=False))
            return

        logger.info(f"{self.config.exchange_name} 开始执行重连流程...")

        # 1. 重置状态和清理资源
        self.is_connected = False

        # 清理旧连接
        if self.ws:
            try:
                await self.ws.close()
            except Exception as e:
                logger.warning(f"{self.config.exchange_name} 关闭WebSocket时出错: {str(e)}")
            finally:
                self.ws = None

        # 清理旧任务
        for task in self.tasks:
            if not task.done():
                task.cancel()
        self.tasks.clear()

        # 等待一小段时间确保资源已释放
        await asyncio.sleep(1)

        # 2. 更新交易对
        self.spot_pairs = spot_pairs
        self.perpetual_pairs = perpetual_pairs

        # 3. 重试逻辑
        try:
            # 设置重试参数
            max_retries = 5
            delay = 1
            reconnect_success = False

            # 进入重试循环
            for attempt in range(max_retries):
                try:
                    # 重新连接
                    logger.info(f"{self.config.exchange_name} 第 {attempt + 1} 次尝试重新连接...")
                    await self.connect()

                    # 重新订阅
                    success = await self.subscribe_market_data(self.spot_pairs, self.perpetual_pairs)
                    if not success:
                        raise Exception("订阅失败")

                    reconnect_success = True
                    self.is_connected = True
                    logger.info(f"{self.config.exchange_name} 重连成功")
                    break  # 成功连接后跳出循环
                except Exception as e:
                    logger.error(f"{self.config.exchange_name} 第 {attempt + 1} 次重连失败: {str(e)}")

                    # 清理资源
                    if self.ws:
                        try:
                            await self.ws.close()
                        except:
                            pass
                        self.ws = None
                    self.is_connected = False

                    # 等待后继续下一次重试
                    if attempt < max_retries - 1:
                        wait_time = delay * (2 ** attempt)  # 指数退避策略
                        logger.info(f"{self.config.exchange_name} 等待 {wait_time} 秒后进行下一次重试...")
                        await asyncio.sleep(wait_time)

            # 所有重试结束后的处理
            if not reconnect_success:
                error_msg = f"{self.config.exchange_name} WebSocket重连失败，已超过最大尝试次数 {max_retries}"
                logger.error(error_msg)
                await self.ding_talk_manager.send_message(
                    error_msg,
                    prefix=self.config.exchange_name,
                    is_at_all=True
                )
            logger.info("*" * 50)
        except Exception as e:
            # 处理重连过程中的异常
            logger.error(f"{self.config.exchange_name} 重连过程中发生异常: {str(e)}")
            await self.ding_talk_manager.send_message(
                f"{self.config.exchange_name} WebSocket重连异常: {str(e)}",
                prefix=self.config.exchange_name,
                is_at_all=True
            )

    async def close(self):
        """关闭WebSocket连接"""
        try:
            logger.info(f"{self.config.exchange_name} OkxWebSocket 正在关闭...")
            for task in self.tasks:
                task.cancel()
            self.tasks.clear()

            if self.ws and self.is_connected:
                await self.ws.close()

            self.is_connected = False
            logger.info(f"{self.config.exchange_name} WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 关闭WebSocket连接失败: {str(e)}")