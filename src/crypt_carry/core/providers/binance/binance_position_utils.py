"""
Binance持仓数据处理工具
用于处理Binance的持仓数据转换
"""
import logging
from typing import Dict, Optional, List, Any
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager

logger = logging.getLogger(__name__)


def convert_contracts_to_amount(contracts: float, symbol: str, market_limit_manager: MarketLimitManager) -> float:
    """
    将合约张数转换为实际数量
    
    Args:
        contracts: 合约张数
        symbol: 交易对名称
        market_limit_manager: 市场限制管理器实例
        
    Returns:
        float: 转换后的实际数量
    """
    try:
        # 获取该交易对的市场限制信息
        market_limit = market_limit_manager.get_market_limit(symbol)

        # 如果找不到市场限制信息，使用默认合约面值
        if market_limit is None:
            logger.warning(f"找不到交易对 {symbol} 的市场限制信息，使用默认合约面值1")
            return contracts

        # 使用合约面值转换
        amount = contracts * market_limit.contract_size
        return amount

    except Exception as e:
        logger.error(f"合约张数转换失败: {str(e)}")
        # 失败时返回原始值
        return contracts


def convert_amount_to_contracts(amount: float, symbol: str, market_limit_manager: MarketLimitManager) -> float:
    """
    将实际数量转换为合约张数
    
    Args:
        amount: 实际数量
        symbol: 交易对名称
        market_limit_manager: 市场限制管理器实例
        
    Returns:
        float: 转换后的合约张数
    """
    try:
        # 获取该交易对的市场限制信息
        market_limit = market_limit_manager.get_market_limit(symbol)

        # 如果找不到市场限制信息，使用默认合约面值
        if market_limit is None:
            logger.warning(f"找不到交易对 {symbol} 的市场限制信息，使用默认合约面值1")
            return amount

        # 使用合约面值转换
        if market_limit.contract_size <= 0:
            logger.warning(f"交易对 {symbol} 的合约面值为 {market_limit.contract_size}，使用默认值1")
            return amount

        contracts = amount / market_limit.contract_size

        # 调整到合法张数
        if market_limit:
            contracts = market_limit.adjust_amount(contracts)

        return contracts

    except Exception as e:
        logger.error(f"实际数量转换失败: {str(e)}")
        # 失败时返回原始值
        return amount


def standardize_position_data(position: Dict[str, Any], market_limit_manager: MarketLimitManager) -> Dict[str, Any]:
    """
    标准化持仓数据，将contracts转换为amount
    
    Args:
        position: 持仓数据
        market_limit_manager: 市场限制管理器实例
        
    Returns:
        Dict[str, Any]: 标准化后的持仓数据
    """
    try:
        # 创建新的字典避免修改原始数据
        standardized = position.copy()

        # 如果是合约持仓且有contracts字段
        if not position.get('is_spot', False) and 'contracts' in position:
            symbol = position.get('symbol', '')
            contracts = float(position.get('contracts', 0))

            # 添加amount字段
            standardized['amount'] = convert_contracts_to_amount(contracts, symbol, market_limit_manager)
        else:
            # 现货持仓直接复制contracts到amount
            standardized['amount'] = float(position.get('contracts', 0))

        return standardized

    except Exception as e:
        logger.error(f"标准化持仓数据失败: {str(e)}")
        # 失败时添加默认amount字段
        position_copy = position.copy()
        position_copy['amount'] = float(position.get('contracts', 0))
        return position_copy
