"""
Binance订单操作客户端
"""
import logging
from typing import Dict, Optional, List

import ccxt

from crypt_carry.core.providers.binance.binance_symbol_utils import to_internal_symbol, \
    to_exchange_instrument_id
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager

logger = logging.getLogger(__name__)


class BinanceOrderClient:
    """Binance订单操作客户端"""

    def __init__(self, exchange: ccxt.binance, exchange_name: str, market_limit_manager: MarketLimitManager):
        """初始化
        
        Args:
            exchange: ccxt交易所实例
            exchange_name: 交易所名称
            market_limit_manager: 市场限制管理器
        """
        self.exchange = exchange
        self.exchange_name = exchange_name
        self.market_limit_manager = market_limit_manager

    async def create_order(
            self,
            symbol: str,
            order_type: str,
            side: str,
            quantity: float = None,
            price: Optional[float] = None,
            is_spot: bool = True,
            position_side: Optional[str] = None,
            reduce_only: bool = False,
    ) -> Dict:
        """创建订单
        Args:
            symbol: 交易对
            order_type: 订单类型 limit/market
            side: 订单方向 buy/sell
            quantity: 下单数量
                - 现货: 表示币的数量
                - 合约: 表示币的数量，会自动转换为对应的合约张数
            price: 下单价格
            is_spot: 是否是现货
            position_side: 持仓方向 long/short
            reduce_only: 是否只减仓
        """
        try:
            # 获取市场限制
            market_limit = self.market_limit_manager.get_market_limit(symbol)
            if not market_limit:
                logger.error(f"未找到交易对 {symbol} 的市场限制信息")
                return None

            # 根据is_spot 使用symbol_utils工具 获取适当的symbol
            if not is_spot:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=False)
                symbol = to_internal_symbol(instrument_id, is_futures=True)
            else:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=True)
                symbol = to_internal_symbol(instrument_id, is_futures=False)

            # 基本参数
            params = {
                'symbol': symbol,
                'side': side.upper(),  # Binance使用大写
                'type': order_type.upper(),
            }

            # 处理合约订单
            if not is_spot:
                # 处理合约数量
                if quantity is not None:
                    # 计算合约张数 = 币数量 / 每张合约代表的币数量
                    contract_size = market_limit.contract_size
                    if contract_size <= 0:
                        logger.error(f"合约面值异常: {contract_size}")
                        return None

                    contract_lots = quantity / contract_size
                    # 向下取整确保不会超出预期数量
                    contract_lots = market_limit.adjust_amount(contract_lots, round_up=False)

                    if contract_lots <= 0:
                        logger.error(f"计算得到的合约张数为0 - 币数量: {quantity}, 合约面值: {contract_size}")
                        return None

                    logger.info(f"币数量 {quantity} 转换为合约张数: {contract_lots} (每张={contract_size})")
                    params['amount'] = contract_lots

                # 设置持仓方向
                if position_side:
                    params['positionSide'] = position_side.upper()
                # 设置是否只减仓
                if reduce_only:
                    params['reduceOnly'] = True

            # 处理现货订单
            else:
                # 调整现货数量
                if quantity is not None:
                    adjusted_quantity = market_limit.adjust_amount(quantity)
                    params['amount'] = adjusted_quantity

            # 处理价格
            if price is not None:
                adjusted_price = market_limit.adjust_price(price)
                params['price'] = adjusted_price

            # 验证订单参数
            if price and params.get('amount'):
                if not market_limit.validate_order(price=price, amount=params['amount']):
                    logger.error(
                        f"订单参数验证失败 - "
                        f"数量: {params['amount']} "
                        f"(限制: {market_limit.min_amount} - {market_limit.max_amount}), "
                        f"价格: {price} "
                        f"(限制: {market_limit.min_price} - {market_limit.max_price})"
                    )
                    return None

            # 创建订单
            logger.debug(f"创建订单 - params: {params}")
            response = await self.exchange.create_order(**params)
            logger.debug(f"订单创建成功 - response: {response}")
            return response

        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None

    async def fetch_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """查询订单
        
        统一的订单返回格式:
        {
            'info': {...},              # 交易所原始返回数据
            'id': str,                  # 订单ID
            'clientOrderId': str,       # 客户端订单ID
            'timestamp': int,           # 创建时间戳(毫秒)
            'datetime': str,            # ISO格式的创建时间
            'lastTradeTimestamp': int,  # 最后成交时间戳
            'lastUpdateTimestamp': int, # 最后更新时间戳
            'symbol': str,              # 交易对
            'type': str,                # 订单类型 limit/market
            'timeInForce': str,         # 有效期 GTC/IOC/FOK
            'side': str,                # 订单方向 buy/sell
            'price': float,             # 下单价格
            'average': float,           # 成交均价
            'cost': float,              # 成交金额
            'amount': float,            # 币种数量(无论现货/合约)
            'contract_lots': float,     # 合约张数(仅合约订单)
            'filled': float,            # 已成交币种数量
            'remaining': float,         # 剩余币种数量
            'status': str,              # 订单状态
            'fee': {                    # 手续费
                'cost': float,          # 手续费金额
                'currency': str         # 手续费币种
            },
            'trades': list,             # 成交记录
            'reduceOnly': bool,         # 是否只减仓
        }
        """
        try:
            # 根据is_spot 使用symbol_utils工具 获取适当的symbol
            if not is_spot:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=False)
                symbol = to_internal_symbol(instrument_id, is_futures=True)
            else:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=True)
                symbol = to_internal_symbol(instrument_id, is_futures=False)
            
            # 查询订单
            response = await self.exchange.fetch_order(order_id, symbol)

            # 获取市场限制信息
            market_limit = self.market_limit_manager.get_market_limit(symbol)
            contract_size = market_limit.contract_size if market_limit else 0.01

            # 统一订单返回格式
            order = {
                'info': response.get('info', {}),
                'id': str(response.get('id', '')),
                'clientOrderId': str(response.get('clientOrderId', '')),
                'timestamp': int(response.get('timestamp', 0) or 0),
                'datetime': response.get('datetime', ''),
                'lastTradeTimestamp': int(response.get('lastTradeTimestamp', 0) or 0),
                'lastUpdateTimestamp': int(response.get('lastUpdateTimestamp', 0) or 0),
                'symbol': response.get('symbol', ''),
                'type': response.get('type', '').lower(),
                'timeInForce': response.get('timeInForce'),
                'side': response.get('side', '').lower(),
                'price': float(response.get('price', 0) or 0),
                'average': float(response.get('average', 0) or 0),
                'cost': float(response.get('cost', 0) or 0),
                'trades': response.get('trades', []),
                'reduceOnly': response.get('reduceOnly', False),
            }

            # 处理数量字段 - 区分现货和合约
            if not is_spot:
                # 合约订单 - amount是合约张数，需要转换为币种数量
                contract_lots = float(response.get('amount', 0) or 0)
                coin_amount = contract_lots * contract_size
                filled_lots = float(response.get('filled', 0) or 0)
                filled_amount = filled_lots * contract_size
                remaining_lots = float(response.get('remaining', 0) or 0)
                remaining_amount = remaining_lots * contract_size

                order['contract_lots'] = contract_lots
                order['amount'] = coin_amount
                order['filled'] = filled_amount
                order['remaining'] = remaining_amount
            else:
                # 现货订单 - amount已经是币种数量
                order['amount'] = float(response.get('amount', 0) or 0)
                order['filled'] = float(response.get('filled', 0) or 0)
                order['remaining'] = float(response.get('remaining', 0) or 0)
                order['contract_lots'] = 0  # 现货没有合约张数概念

            # 设置订单状态
            order['status'] = response.get('status', '').lower()

            # 设置手续费
            order['fee'] = {
                'cost': float(response.get('fee', {}).get('cost', 0) or 0),
                'currency': response.get('fee', {}).get('currency', '')
            }

            return order
            
        except Exception as e:
            logger.error(f"{self.exchange_name} 查询订单失败: {str(e)}")
            return None

    async def cancel_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """取消订单
        
        Args:
            order_id: 订单ID
            symbol: 交易对
            is_spot: 是否现货
            
        Returns:
            Dict: 取消结果
        """
        try:
            # 根据is_spot 使用symbol_utils工具 获取适当的symbol
            if not is_spot:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=False)
                symbol = to_internal_symbol(instrument_id, is_futures=True)
            else:
                instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=True)
                symbol = to_internal_symbol(instrument_id, is_futures=False)
                
            # 取消订单
            result = await self.exchange.cancel_order(order_id, symbol)
            return result
            
        except Exception as e:
            # 如果是订单已完成的错误，返回成功
            error_msg = str(e)
            if any(msg in error_msg.lower() for msg in [
                "order does not exist",  # 订单不存在
                "order was filled",  # 订单已完成
                "order has been filled",  # 订单已完成
                "status is end"  # 订单已结束
            ]):
                logger.info(f"{self.exchange_name} 订单 {order_id} 已完成，取消失败")
                return {
                    'id': order_id,
                    'status': 'closed',
                    'info': {'msg': 'Order has been filled or does not exist'}
                }
            logger.error(f"{self.exchange_name} 取消订单失败: {error_msg}")
            raise

    async def fetch_open_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询当前未完成的订单
        
        Args:
            symbol: 交易对（可选，如果不指定则查询所有交易对）
            is_spot: 是否现货
            
        Returns:
            List[Dict]: 未成交订单列表
        """
        try:
            if symbol:
                # 根据is_spot 使用symbol_utils工具 获取适当的symbol
                if not is_spot:
                    instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=False)
                    symbol = to_internal_symbol(instrument_id, is_futures=True)
                else:
                    instrument_id = to_exchange_instrument_id(symbol=symbol, is_spot=True)
                    symbol = to_internal_symbol(instrument_id, is_futures=False)

            # 获取未成交订单
            open_orders = await self.exchange.fetch_open_orders(symbol)
            return open_orders
            
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取未成交订单失败: {str(e)}")
            raise
