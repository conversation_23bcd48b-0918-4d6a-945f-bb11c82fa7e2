"""币安市场数据管理器"""
from typing import Dict, List, Optional, Tuple
import time
import logging
import asyncio
import ccxt

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.providers.binance.binance_symbol_utils import (
    build_internal_symbol,
    to_standard_symbol,
)
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.utils.retry import async_retry

logger = logging.getLogger(__name__)


class BinanceMarketManager:
    """币安市场数据管理器"""

    def __init__(self, config: ExchangeConfig, exchange: ccxt.binance):
        """初始化币安市场数据管理器
        
        Args:
            config: 交易所配置
            exchange: ccxt交易所实例
        """
        self.exchange_name = config.exchange_name
        self.config = config
        self.market_manager = MarketDataManager.get_instance()
        self.exchange = exchange

        # 初始化交易对列表
        self.base_pair = []
        self.spot_pairs = []
        self.perpetual_pairs = []
        self.quotes = []  # 由load_markets_with_retry设置

    @async_retry(max_retries=3, initial_delay=1.0, backoff_factor=2.0)
    async def load_markets_with_retry(self) -> bool:
        """加载市场数据并初始化交易对
            
        Returns:
            bool: 是否成功
        """
        # 使用集合存储交易对，避免重复
        base_pair_set = set()
        perpetual_pairs_set = set()
        spot_pairs_set = set()

        # 合约交易量阈值 (100万USDT)
        VOLUME_THRESHOLD = 1000000  # 单位: USDT

        try:
            # 加载市场信息
            markets = await self.exchange.load_markets()

            # 获取配置的交易对列表
            symbols = self.config.trading_config["EXCHANGE_PAIRS"].get(self.exchange_name, {}).get("symbol", [])
            self.quotes = self.config.trading_config.get(self.exchange_name, {}).get('quote', ['USDT'])

            logger.info(f"{self.exchange_name} 配置的交易币种: {symbols}")
            logger.info(f"{self.exchange_name} 配置的计价币种: {self.quotes}")

            # 第一步: 找出所有现货和永续合约交易对
            paired_markets = {}  # 格式: {base_quote: {'spot': spot_symbol, 'swap': swap_symbol}}

            for symbol, market in markets.items():
                try:
                    # 检查是否是配置的交易对
                    if market['base'] not in symbols and len(symbols) > 0:
                        continue

                    # 只考虑self.quotes计价的交易对
                    if market['quote'] not in self.quotes:
                        continue

                    base_quote = f"{market['base']}/{market['quote']}"

                    if market.get('spot'):  # 现货
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['spot'] = symbol
                    elif market.get('linear') and market.get('swap'):  # 永续合约
                        if base_quote not in paired_markets:
                            paired_markets[base_quote] = {}
                        paired_markets[base_quote]['swap'] = symbol
                        # 保存合约面值信息
                        if 'contractSize' in market and market['contractSize'] is not None:
                            paired_markets[base_quote]['contractSize'] = float(market['contractSize'])
                        else:
                            # 如果无法获取合约面值，默认使用0.01（一般币种的标准）
                            paired_markets[base_quote]['contractSize'] = 0.01
                except Exception as e:
                    logger.error(f"处理市场数据时出错 {symbol}: {str(e)}")

            # 第二步: 筛选同时有现货和永续合约的交易对
            valid_pairs = {}
            for base_quote, symbols_dict in paired_markets.items():
                if 'spot' in symbols_dict and 'swap' in symbols_dict:
                    valid_pairs[base_quote] = symbols_dict

            logger.info(f"{self.exchange_name} 找到同时有现货和永续合约的交易对: {len(valid_pairs)}")

            # 第三步: 获取合约交易对的24小时交易量并筛选
            # 使用协程并发获取所有交易对的交易量
            high_volume_pairs = await self._fetch_volumes_concurrently(valid_pairs, VOLUME_THRESHOLD)

            # 交易量从高到低排序
            high_volume_pairs.sort(key=lambda x: x[2], reverse=True)
            logger.info(f"{self.exchange_name} 找到 {len(high_volume_pairs)} 个高交易量交易对")

            # 第四步: 将筛选出的交易对添加到交易列表中
            for base_quote, symbols_dict, volume in high_volume_pairs:
                try:
                    base, quote = base_quote.split('/')

                    # 构造标准格式的交易对
                    spot_standard_symbol = build_internal_symbol(base, quote, is_futures=False)
                    future_standard_symbol = build_internal_symbol(base, quote, is_futures=True)
                    standard_symbol = to_standard_symbol(spot_standard_symbol)

                    # 添加到集合中（自动去重）
                    base_pair_set.add(standard_symbol)
                    perpetual_pairs_set.add(future_standard_symbol)
                    spot_pairs_set.add(spot_standard_symbol)

                    logger.debug(
                        f"添加交易对: {standard_symbol}, 现货: {spot_standard_symbol}, 合约: {future_standard_symbol}")

                except Exception as e:
                    logger.error(f"处理高交易量交易对 {base_quote} 时出错: {str(e)}")
                    continue

            # 将集合转换为列表
            self.base_pair = list(base_pair_set)
            self.perpetual_pairs = list(perpetual_pairs_set)
            self.spot_pairs = list(spot_pairs_set)

            # 检查是否找到交易对
            if not self.perpetual_pairs:
                logger.warning(f"{self.exchange_name} 没有找到符合条件的永续合约交易对")
            if not self.spot_pairs:
                logger.warning(f"{self.exchange_name} 没有找到符合条件的现货交易对")

            logger.info(
                f"{self.exchange_name} 加载市场数据成功 - "
                f"基础交易对: {len(self.base_pair)}个, "
                f"永续合约: {len(self.perpetual_pairs)}个, "
                f"现货: {len(self.spot_pairs)}个"
            )

            # 详细打印所有选中的交易对
            if logger.isEnabledFor(logging.DEBUG):
                for i, pair in enumerate(high_volume_pairs):
                    base_quote, symbols_dict, volume = pair
                    logger.debug(f"#{i + 1} 交易对: {base_quote}, 交易量: {volume} USDT")

            return True

        except Exception as e:
            logger.error(f"{self.exchange_name} 加载市场数据失败: {str(e)}")
            from crypt_carry.utils.retry import RetryableError
            raise RetryableError(f"{self.exchange_name} load_markets_with_retry 失败: {str(e)}") from e

    @async_retry(max_retries=3, initial_delay=0.5, backoff_factor=2.0)
    async def _fetch_ticker_with_retry(self, swap_symbol, contract_size=0.01):
        """获取交易对的ticker数据，并计算交易量
        
        Args:
            swap_symbol: 交易对符号
            contract_size: 合约面值
            
        Returns:
            tuple: (交易对符号, 24小时交易量(USDT))
        
        Raises:
            RetryableError: 当需要重试的错误发生时
        """
        try:
            ticker = await self.exchange.fetch_ticker(swap_symbol)

            # 获取最新价格
            last_price = ticker['last'] if 'last' in ticker and ticker['last'] is not None else 1

            # 计算实际交易量 (以USDT计价)
            if 'baseVolume' in ticker and ticker['baseVolume'] is not None:
                # 使用公式: baseVolume × 合约面值 × 最新价格
                base_volume = ticker['baseVolume']
                volume_usd = base_volume * contract_size * last_price
                logger.debug(
                    f"{swap_symbol} 交易量计算: {base_volume} × {contract_size} × {last_price} = {volume_usd} USDT")
            elif 'quoteVolume' in ticker and ticker['quoteVolume'] is not None:
                # 如果API直接提供了按计价货币的交易量
                volume_usd = ticker['quoteVolume']
                logger.debug(f"{swap_symbol} 使用quoteVolume作为交易量: {volume_usd} USDT")
            else:
                logger.warning(f"无法获取{swap_symbol}的交易量数据，跳过")
                return None, 0

            return swap_symbol, volume_usd

        except Exception as e:
            logger.warning(f"获取 {swap_symbol} 交易量失败: {str(e)}")
            # 抛出可重试异常，让装饰器处理重试逻辑
            from crypt_carry.utils.retry import RetryableError
            raise RetryableError(f"获取 {swap_symbol} 交易量失败: {str(e)}") from e

    async def _fetch_volumes_concurrently(self, valid_pairs, volume_threshold, batch_size=15):
        """并发获取多个交易对的交易量
        
        Args:
            valid_pairs: 有效的交易对字典 {base_quote: {'spot': spot_symbol, 'swap': swap_symbol}}
            volume_threshold: 交易量阈值
            batch_size: 批处理大小，控制并发数量
            
        Returns:
            list: 高交易量交易对列表 [(base_quote, symbols, volume)]
        """
        high_volume_pairs = []
        total_pairs = len(valid_pairs)

        # 将交易对按批次处理，避免过多并发请求
        items = list(valid_pairs.items())
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]

        logger.info(f"开始获取 {total_pairs} 个交易对的交易量，分 {len(batches)} 批处理")

        for batch_idx, batch in enumerate(batches):
            tasks = []

            # 创建每个交易对的获取任务
            for base_quote, symbols_dict in batch:
                try:
                    swap_symbol = symbols_dict['swap']
                    contract_size = symbols_dict.get('contractSize', 0.01)

                    # 创建获取交易量的任务
                    task = asyncio.create_task(
                        self._fetch_ticker_with_retry(swap_symbol, contract_size)
                    )
                    tasks.append((base_quote, symbols_dict, task))
                except Exception as e:
                    logger.error(f"创建获取 {base_quote} 交易量任务时出错: {str(e)}")

            # 等待所有任务完成
            for base_quote, symbols_dict, task in tasks:
                try:
                    swap_symbol, volume_usd = await task

                    if swap_symbol and volume_usd >= volume_threshold:
                        high_volume_pairs.append((base_quote, symbols_dict, volume_usd))
                        logger.debug(f"添加高交易量交易对: {swap_symbol}, 交易量: {volume_usd} USDT")
                except Exception as e:
                    logger.error(f"处理 {base_quote} 交易量结果时出错: {str(e)}")

            # 打印进度
            processed = min((batch_idx + 1) * batch_size, total_pairs)
            logger.info(f"进度: {processed}/{total_pairs} ({(processed / total_pairs * 100):.1f}%)")

            # 在批次之间短暂暂停，避免可能的限流
            if batch_idx < len(batches) - 1:
                await asyncio.sleep(0.5)

        return high_volume_pairs

    def get_trading_pairs(self) -> Tuple[List[str], List[str], List[str]]:
        """获取交易对列表
        
        Returns:
            Tuple[List[str], List[str], List[str]]: (基础交易对, 现货交易对, 永续合约交易对)
        """
        return self.base_pair, self.spot_pairs, self.perpetual_pairs

    def update_market_data(self, symbol: str, data: Dict):
        """更新市场数据
        
        Args:
            symbol: 交易对符号
            data: 市场数据，包含timestamp和价格信息
        """
        self.market_manager.update_market_data(self.exchange_name, symbol, data)

    def get_market_data(self, symbol: str) -> Optional[Dict]:
        """获取特定交易对的市场数据"""
        return self.market_manager.get_market_data(self.exchange_name, symbol)

    def get_basis_data(self, symbol: str) -> Optional[Dict]:
        """获取基差数据"""
        return self.market_manager.get_basis_data(self.exchange_name, symbol)

    def get_price(self, symbol: str, market_type: str = 'spot') -> Optional[float]:
        """获取特定交易对的价格"""
        try:
            market_data = self.get_market_data(symbol)
            if market_data:
                price_key = f"{market_type}_price"
                return market_data.get(price_key)
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取价格失败: {str(e)}")
        return None

    def get_all_market_data(self) -> Dict[str, Dict]:
        """获取所有市场数据"""
        return self.market_manager.get_all_market_data(self.exchange_name)

    def get_symbols(self) -> List[str]:
        """获取所有交易对列表"""
        return self.market_manager.get_symbols(self.exchange_name)
