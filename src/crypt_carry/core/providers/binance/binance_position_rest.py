import ccxt.pro as ccxtpro
import logging
import asyncio
from crypt_carry.core.providers.binance.binance_symbol_utils import to_exchange_instrument_id, to_standard_symbol
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager

logger = logging.getLogger(__name__)


class BinancePositionRest:
    def __init__(self, exchange: ccxtpro.binance, exchange_name: str, market_limit_manager: MarketLimitManager,
                 all_pairs: list[str],
                 base_pair: list[str],
                 quotes: list[str]):
        self.exchange: ccxtpro.binance = exchange
        self.exchange_name = exchange_name
        self.market_limit_manager = market_limit_manager
        self.all_pairs = all_pairs
        self.base_pair = base_pair
        self.quotes = quotes
        self.positions_cache = []
        self.balances_cache = {}
        self.last_positions_update = 0
        self.last_balances_update = 0
        self.is_running = False
        self.position_monitor_task = None

    async def fetch_positions(self, symbol: str = None) -> list[dict]:
        """获取当前持仓数据
        
        Args:
            symbol: 交易对，如 BTC-USDT。如果为 None，则获取所有交易对的持仓。
            
        Returns:
            list[dict]: 持仓列表，每个持仓包含：
            {
                'symbol': str,      # 交易对
                'side': str,        # 方向，LONG 或 SHORT
                'is_spot': bool,    # 是否是现货
                'contracts': float, # 合约张数
                'amount': float,    # 实际数量
                'leverage': float,  # 杠杆倍数
                'entryPrice': float # 开仓均价
            }
        """
        try:
            # 准备查询的现货和合约交易对
            spot_symbols = []
            future_symbols = []

            if symbol:
                # 如果指定了具体交易对，只查询该交易对
                spot_symbol = to_standard_symbol(symbol)
                future_symbol = to_exchange_instrument_id(symbol, is_spot=False)

                if spot_symbol:
                    spot_symbols.append(spot_symbol)
                if future_symbol:
                    future_symbols.append(future_symbol)
            else:
                # 否则查询所有基础交易对
                for base_symbol in self.base_pair:
                    standard_spot_symbol = to_standard_symbol(base_symbol)
                    future_symbol = to_exchange_instrument_id(standard_spot_symbol, is_spot=False)
                    spot_symbols.append(standard_spot_symbol)
                    future_symbols.append(future_symbol)

            # 初始化结果列表
            all_positions = []

            # 获取合约持仓信息
            try:
                # 分批查询合约持仓，避免可能的API限制
                if future_symbols:
                    batch_size = 20  # Binance可能没有明确限制，但为安全起见设置一个合理值
                    all_futures_positions = []

                    # 按批次处理
                    for i in range(0, len(future_symbols), batch_size):
                        batch = future_symbols[i:i + batch_size]
                        logger.info(f"正在查询第{i // batch_size + 1}批合约持仓，共{len(batch)}个交易对")
                        try:
                            batch_positions = await self.exchange.fetch_positions(batch)
                            all_futures_positions.extend(batch_positions)
                            await asyncio.sleep(0.5)
                        except Exception as e:
                            logger.error(f"获取第{i // batch_size + 1}批合约持仓失败: {str(e)}")
                            # 继续处理下一批，不中断整个过程

                    # 处理和标准化持仓数据
                    for position in all_futures_positions:
                        # 只处理有效持仓
                        contracts = float(position.get('contracts', 0))
                        if contracts <= 0:
                            continue

                        # 将Binance特定格式转换为标准格式
                        side = 'LONG' if position.get('side') == 'long' else 'SHORT'
                        standardized_position = {
                            'symbol': position.get('symbol'),
                            'side': side,
                            'is_spot': False,
                            'contracts': contracts,
                            'leverage': float(position.get('leverage', 1)),
                            'entryPrice': float(position.get('entryPrice', 0))
                        }

                        # 添加CCXT标准化的风险相关字段
                        # liquidationPrice: 清算价格
                        # marginRatio: 保证金比率
                        # marginMode: 保证金模式
                        # markPrice: 标记价格
                        # maintMargin: 维持保证金
                        # initialMargin: 初始保证金
                        risk_fields = ['liquidationPrice', 'marginRatio', 'marginMode', 'markPrice', 'maintMargin',
                                       'initialMargin']
                        for field in risk_fields:
                            if field in position:
                                standardized_position[field] = position.get(field)

                        # 处理可能不存在的风险字段，从原始info中提取
                        if 'info' in position:
                            info = position['info']

                            # 处理清算价格
                            if 'liquidationPrice' not in standardized_position and 'liquidationPrice' in info:
                                standardized_position['liquidationPrice'] = float(info.get('liquidationPrice', 0))

                            # 处理保证金率
                            if 'marginRatio' not in standardized_position and 'marginRatio' in info:
                                standardized_position['marginRatio'] = float(info.get('marginRatio', 0))

                            # 处理标记价格
                            if 'markPrice' not in standardized_position and 'markPrice' in info:
                                standardized_position['markPrice'] = float(info.get('markPrice', 0))

                            # 处理保证金模式
                            if 'marginMode' not in standardized_position and 'marginType' in info:
                                standardized_position['marginMode'] = info.get('marginType', '')

                            # 维持保证金
                            if 'maintMargin' not in standardized_position and 'maintMargin' in info:
                                standardized_position['maintMargin'] = float(info.get('maintMargin', 0))

                        # 标准化持仓数据，添加amount字段
                        standardized_position = standardize_position_data(standardized_position,
                                                                          self.market_limit_manager)

                        all_positions.append(standardized_position)
            except Exception as e:
                logger.error(f"获取合约持仓数据失败: {str(e)}")

            # 获取现货账户数据，将其转换为持仓格式
            # 注意：对于现货，我们只能获得账户余额，没有杠杆等信息
            try:
                balances = await self.fetch_balances(spot_symbols)

                # 将现货余额转化为持仓格式
                for symbol, balance in balances.items():
                    # 跳过计价货币
                    if symbol in self.quotes:
                        continue

                    # 获取交易对的内部符号和标准符号
                    standard_symbol = to_standard_symbol(symbol)

                    # 获取该交易对的最小交易量限制
                    market_limit = self.market_limit_manager.get_market_limit(standard_symbol)

                    # 只有当持仓大于最小交易量时才记录
                    # 如果无法获取市场限制，则使用一个小的默认值
                    min_trading_amount = market_limit.min_amount if market_limit else 0.000001

                    # 只处理有效余额的币种
                    if abs(balance) < min_trading_amount:
                        continue

                    # 构建标准化持仓数据
                    standardized_position = {
                        'symbol': to_standard_symbol(symbol),  # 确保使用标准格式
                        'side': 'LONG',  # 现货只有多头
                        'is_spot': True,
                        'contracts': balance,  # 现货中contracts与amount相同
                        'amount': balance,  # 现货中amount与contracts相同
                        'leverage': 1,  # 现货默认杠杆为1
                        'entryPrice': 0  # 现货没有开仓价格概念
                    }

                    all_positions.append(standardized_position)
            except Exception as e:
                logger.error(f"获取现货持仓数据失败: {str(e)}")

            logger.debug(f"{self.exchange_name} 查询持仓成功: {all_positions}")
            return all_positions

        except Exception as e:
            error_msg = f"{self.exchange_name} 查询持仓失败: {str(e)}"
            logger.error(error_msg)
            # 返回包含错误信息的字典，而不是空列表
            return {
                'error': True,
                'error_message': error_msg,
                'error_type': 'NETWORK_ERROR',
                'positions': []
            }

    async def fetch_balances(self, symbols: list[str] = None) -> dict[str, float]:
        """查询当前现货余额
        
        Args:
            symbols: 币种列表（可选，如果不指定则查询所有币种）
            
        Returns:
            Dict[str, float]: 币种余额字典，key为币种，value为余额
        """
        try:
            # 对于Binance，使用spot交易所客户端
            balances = await self.exchange.fetch_balance()
            logger.debug(f"成功获取余额信息")

            # 使用total余额而不是free余额
            total_balances = balances.get('total', {})

            # 如果指定了币种，则只返回指定币种的余额
            if symbols:
                filtered_balances = {}
                for standard_symbol in symbols:
                    base = standard_symbol.split('/')[0]
                    if base in total_balances:
                        filtered_balances[standard_symbol] = total_balances[base]
                return filtered_balances

            # 否则返回所有余额
            return {k: v for k, v in total_balances.items() if v > 0}

        except Exception as e:
            logger.error(f"{self.exchange_name} 查询余额失败: {str(e)}")
            # 出错时返回空字典，避免影响后续逻辑
            return {}
