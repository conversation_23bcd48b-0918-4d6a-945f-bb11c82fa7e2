"""Binance 交易对格式转换工具

内部统一格式:
- 现货: BTC/USDT
- 合约: BTC/USDT:USDT

Binance格式:
- 现货: BTCUSDT
- 合约: BTCUSDT
"""
import logging
from typing import Optional

logger = logging.getLogger(__name__)


def to_standard_symbol(symbol: str) -> str:
    """将任意格式的交易对转换为标准格式（不带:USDT后缀）

    Args:
        symbol: 交易对，支持以下格式：
               - 现货格式：BTC/USDT
               - 合约格式：BTC/USDT:USDT

    Returns:
        str: 标准格式的交易对，如 BTC/USDT
    """
    return symbol.split(':')[0] if ':' in symbol else symbol


def to_internal_symbol(symbol: str, is_futures: bool = False) -> str:
    """将Binance的交易对格式转换为内部标准格式
    
    Args:
        symbol: Binance的交易对格式，如:
            - 现货: BTCUSDT
            - 合约: BTCUSDT
            
    Returns:
        str: 内部标准格式
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
    """
    try:
        # 已经是标准格式
        if '/' in symbol:
            return symbol

        # 处理 BUSDUSDT 这种特殊情况
        if symbol == 'BUSDUSDT':
            return 'BUSD/USDT'

        # 处理其他情况
        for quote in ['USDT', 'BUSD', 'BTC', 'ETH']:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                spot = f"{base}/{quote}"
                if is_futures:
                    return f"{spot}:{quote}"
                return spot

        logger.warning(f"无法识别的Binance交易对格式: {symbol}")
        return symbol
    except Exception as e:
        logger.error(f"转换Binance交易对格式失败: {symbol}, {str(e)}")
        return symbol


def to_exchange_instrument_id(symbol: str, is_spot: bool = True) -> str:
    """将内部标准格式转换为Binance交易对格式
    
    Args:
        symbol: 内部标准格式，如:
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
        is_spot: 是否为现货交易对
            
    Returns:
        str: Binance交易对格式
            - 现货: BTCUSDT
            - 合约: BTCUSDT
    """
    try:
        # 已经是Binance格式
        if '/' not in symbol:
            return symbol

        # 移除:USDT后缀并分割基础货币和计价货币
        base_quote = symbol.split(':')[0]
        base, quote = base_quote.split('/')

        # Binance的现货和合约格式相同
        return f"{base}{quote}"

    except Exception as e:
        logger.error(f"转换为Binance交易对格式失败: {symbol}, {str(e)}")
        return symbol.lower()


def build_internal_symbol(base: str, quote: str, is_futures: bool = False) -> str:
    """根据基础货币和计价货币构建内部统一格式的交易对
    
    Args:
        base: 基础货币，如 BTC
        quote: 计价货币，如 USDT
        is_futures: 是否为合约交易对
            
    Returns:
        str: 内部统一格式
            - 现货: BTC/USDT
            - 合约: BTC/USDT:USDT
    """
    try:
        spot_symbol = f"{base}/{quote}"
        if is_futures:
            return f"{spot_symbol}:{quote}"
        return spot_symbol
    except Exception as e:
        logger.error(f"构建内部统一格式失败: base={base}, quote={quote}, is_futures={is_futures}, {str(e)}")
        return ""
