"""Binance WebSocket 实现"""
import asyncio
import json
from typing import Dict, Optional, List, Callable, Set

import aiohttp
from aiohttp import ClientWebSocketResponse

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
import logging

from crypt_carry.core.providers.binance.binance_symbol_utils import to_exchange_instrument_id
from crypt_carry.utils.retry import async_retry, RetryableError

logger = logging.getLogger(__name__)

class BinanceWebSocket:
    def __init__(self, config: ExchangeConfig, message_handler: Callable = None):
        self.config = config
        self.spot_ws: Optional[ClientWebSocketResponse] = None
        self.futures_ws: Optional[ClientWebSocketResponse] = None
        self.spot_session: Optional[aiohttp.ClientSession] = None
        self.futures_session: Optional[aiohttp.ClientSession] = None
        
        # WebSocket URLs
        self.spot_ws_url = self.config.base_config["API_URLS"]["BINANCE"]["SPOT_WS"]
        self.futures_ws_url = self.config.base_config["API_URLS"]["BINANCE"]["FUTURES_WS"]
        
        self.message_handler = message_handler
        # 使用集合来存储已订阅的交易对，避免重复
        self.subscribed_channels: Dict[str, Set[str]] = {
            'spot': set(),
            'futures': set(),
            'funding_rate': set()
        }
        
        self.is_connected = False
        self.reconnect_delay = 5
        self._lock = asyncio.Lock()  # 添加锁以保护订阅操作

    @async_retry(
        max_retries=3,
        initial_delay=0.5,
        backoff_factor=2.0,
        timeout=5.0,
        error_type="Binance建立WebSocket连接"
    )
    async def connect(self):
        """建立现货和合约的 WebSocket 连接"""
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None
            logger.info(f"代理设置: {proxy_url}")
            logger.info(f"现货WS地址: {self.spot_ws_url}")
            logger.info(f"合约WS地址: {self.futures_ws_url}")

            # 建立现货连接
            self.spot_session = aiohttp.ClientSession(connector=connector)
            self.spot_ws = await self.spot_session.ws_connect(
                self.spot_ws_url,
                proxy=proxy_url,
                heartbeat=30
            )
            logger.info(f"{self.config.exchange_name} 现货 WebSocket 连接成功")

            # 建立合约连接
            self.futures_session = aiohttp.ClientSession(connector=connector)
            self.futures_ws = await self.futures_session.ws_connect(
                self.futures_ws_url,
                proxy=proxy_url,
                heartbeat=30
            )
            logger.info(f"{self.config.exchange_name} 合约 WebSocket 连接成功")

            self.is_connected = True

            # 启动监听任务
            asyncio.create_task(self._listen_spot())
            asyncio.create_task(self._listen_futures())

        except Exception as e:
            logger.error(f"{self.config.exchange_name} WebSocket 连接失败: {str(e)}")
            raise RetryableError(f"{self.config.exchange_name} WebSocket 连接失败: {str(e)}")

    async def subscribe_spot(self, symbols: List[str]):
        """订阅现货市场数据"""
        if not self.is_connected:
            logger.warning(f"{self.config.exchange_name} WebSocket未连接，无法订阅现货数据")
            return False

        try:
            async with self._lock:  # 使用锁保护订阅操作
                # 格式化交易对并去重
                spot_pairs = list(set(to_exchange_instrument_id(symbol=symbol, is_spot=True) for symbol in symbols))

                # 过滤掉已经订阅的交易对
                new_pairs = [pair for pair in spot_pairs
                             if pair not in self.subscribed_channels['spot']]

                if not new_pairs:
                    logger.debug(f"{self.config.exchange_name} 所有现货交易对已订阅")
                    return True

                # 为每个交易对订阅行情信息
                for pair in new_pairs:
                    subscription = {
                        "method": "SUBSCRIBE",
                        "params": [
                            f"{pair.lower()}@bookTicker"
                        ],
                        "id": len(self.subscribed_channels['spot']) + 1
                    }
                    await self.spot_ws.send_str(json.dumps(subscription))
                    logger.debug(f"{self.config.exchange_name} 订阅现货交易对: {pair}")
                    self.subscribed_channels['spot'].add(pair)

                logger.info(f"{self.config.exchange_name} 成功订阅 {len(new_pairs)} 个现货交易对")
                return True
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 订阅现货数据失败: {str(e)}")
            return False

    async def subscribe_futures(self, symbols: List[str]):
        """订阅合约市场数据"""
        if not self.is_connected:
            logger.warning(f"{self.config.exchange_name} WebSocket未连接，无法订阅合约数据")
            return False

        try:
            async with self._lock:  # 使用锁保护订阅操作
                # 格式化交易对并去重
                futures_pairs = list(set(to_exchange_instrument_id(symbol=symbol, is_spot=False) for symbol in symbols))

                # 过滤掉已经订阅的交易对
                new_pairs = [pair for pair in futures_pairs
                             if pair not in self.subscribed_channels['futures']]

                if not new_pairs:
                    logger.debug(f"{self.config.exchange_name} 所有合约交易对已订阅")
                    return True

                # 为每个交易对订阅行情信息
                for pair in new_pairs:
                    subscription = {
                        "method": "SUBSCRIBE",
                        "params": [
                            f"{pair.lower()}@bookTicker"
                        ],
                        "id": len(self.subscribed_channels['futures']) + 1
                    }
                    await self.futures_ws.send_str(json.dumps(subscription))
                    logger.debug(f"{self.config.exchange_name} 订阅合约交易对: {pair}")
                    self.subscribed_channels['futures'].add(pair)

                logger.info(f"{self.config.exchange_name} 成功订阅 {len(new_pairs)} 个合约交易对")
                return True
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 订阅合约数据失败: {str(e)}")
            return False

    async def subscribe_market_data(self, spot_pairs: List[str], perpetual_pairs: List[str]) -> bool:
        """订阅市场数据
        
        Args:
            spot_pairs: 现货交易对列表
            perpetual_pairs: 永续合约交易对列表
            
        Returns:
            bool: 订阅是否成功
        """
        try:
            spot_result = True
            futures_result = True

            if spot_pairs:
                spot_result = await self.subscribe_spot(spot_pairs)

            if perpetual_pairs:
                futures_result = await self.subscribe_futures(perpetual_pairs)

            logger.debug(
                f"{self.config.exchange_name} 订阅市场数据完成: "
                f"现货{len(spot_pairs)}个, 合约{len(perpetual_pairs)}个"
            )
            return spot_result and futures_result
            
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 订阅市场数据失败: {str(e)}")
            return False

    async def _listen_spot(self):
        """监听现货 WebSocket"""
        logger.info(f"{self.config.exchange_name} 开始监听现货 WebSocket")
        try:
            async for msg in self.spot_ws:
                try:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        # 处理响应消息
                        if 'result' in data:
                            # 订阅响应
                            continue
                        # 处理行情消息
                        elif 's' in data:
                            await self.message_handler(data, 'spot')
                        # 记录其他类型的消息
                        else:
                            logger.debug(f"{self.config.exchange_name} 现货WebSocket收到未处理消息: {data}")
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        logger.error(f"{self.config.exchange_name} 现货WebSocket错误: {msg}")
                        break
                    elif msg.type == aiohttp.WSMsgType.CLOSED:
                        logger.warning(f"{self.config.exchange_name} 现货WebSocket已关闭")
                        break
                except Exception as e:
                    logger.error(f"{self.config.exchange_name} 处理现货WebSocket消息时发生错误: {e}")
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 现货WebSocket监听失败: {e}")
        finally:
            # 尝试重新连接
            if self.is_connected:
                logger.warning(f"{self.config.exchange_name} 现货WebSocket断开，尝试重连")
                asyncio.create_task(self._reconnect_spot())

    async def _listen_futures(self):
        """监听合约 WebSocket"""
        logger.info(f"{self.config.exchange_name} 开始监听合约 WebSocket")
        try:
            async for msg in self.futures_ws:
                try:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        # 处理响应消息
                        if 'result' in data:
                            # 订阅响应
                            continue
                        # 处理行情消息 (确保数据包含交易对信息)
                        elif 's' in data:
                            await self.message_handler(data, 'futures')
                        # 记录其他类型的消息
                        else:
                            logger.debug(f"{self.config.exchange_name} 合约WebSocket收到未处理消息: {data}")
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        logger.error(f"{self.config.exchange_name} 合约WebSocket错误: {msg}")
                        break
                    elif msg.type == aiohttp.WSMsgType.CLOSED:
                        logger.warning(f"{self.config.exchange_name} 合约WebSocket已关闭")
                        break
                except Exception as e:
                    logger.error(f"{self.config.exchange_name} 处理合约WebSocket消息时发生错误: {e}")
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 合约WebSocket监听失败: {e}")
        finally:
            # 尝试重新连接
            if self.is_connected:
                logger.warning(f"{self.config.exchange_name} 合约WebSocket断开，尝试重连")
                asyncio.create_task(self._reconnect_futures())

    @async_retry(max_retries=3, initial_delay=1.0, backoff_factor=2.0)
    async def _reconnect_spot(self):
        """重新连接现货 WebSocket"""
        try:
            # 关闭现有连接
            if self.spot_ws and not self.spot_ws.closed:
                await self.spot_ws.close()
            if self.spot_session and not self.spot_session.closed:
                await self.spot_session.close()

            # 创建新的会话和连接
            connector = aiohttp.TCPConnector(ssl=False)
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None

            self.spot_session = aiohttp.ClientSession(connector=connector)
            self.spot_ws = await self.spot_session.ws_connect(
                self.spot_ws_url,
                proxy=proxy_url,
                heartbeat=30
            )

            logger.info(f"{self.config.exchange_name} 现货WebSocket重连成功")

            # 重新订阅
            spot_pairs = list(self.subscribed_channels['spot'])
            self.subscribed_channels['spot'].clear()
            if spot_pairs:
                await self.subscribe_spot(spot_pairs)

            # 重新启动监听
            asyncio.create_task(self._listen_spot())

        except Exception as e:
            logger.error(f"{self.config.exchange_name} 重连现货WebSocket失败: {e}")
            # 使用RetryableError让async_retry装饰器处理重试
            raise RetryableError(f"重连现货WebSocket失败: {e}")

    @async_retry(max_retries=3, initial_delay=1.0, backoff_factor=2.0)
    async def _reconnect_futures(self):
        """重新连接合约 WebSocket"""
        try:
            # 关闭现有连接
            if self.futures_ws and not self.futures_ws.closed:
                await self.futures_ws.close()
            if self.futures_session and not self.futures_session.closed:
                await self.futures_session.close()

            # 创建新的会话和连接
            connector = aiohttp.TCPConnector(ssl=False)
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None

            self.futures_session = aiohttp.ClientSession(connector=connector)
            self.futures_ws = await self.futures_session.ws_connect(
                self.futures_ws_url,
                proxy=proxy_url,
                heartbeat=30
            )

            logger.info(f"{self.config.exchange_name} 合约WebSocket重连成功")

            # 重新订阅
            futures_pairs = list(self.subscribed_channels['futures'])
            self.subscribed_channels['futures'].clear()
            if futures_pairs:
                await self.subscribe_futures(futures_pairs)

            # 重新启动监听
            asyncio.create_task(self._listen_futures())

        except Exception as e:
            logger.error(f"{self.config.exchange_name} 重连合约WebSocket失败: {e}")
            # 使用RetryableError让async_retry装饰器处理重试
            raise RetryableError(f"重连合约WebSocket失败: {e}")

    async def close(self):
        """关闭WebSocket连接"""
        try:
            # 设置连接状态为False
            self.is_connected = False

            # 关闭现货WebSocket
            if self.spot_ws and not self.spot_ws.closed:
                await self.spot_ws.close()

            # 关闭合约WebSocket
            if self.futures_ws and not self.futures_ws.closed:
                await self.futures_ws.close()

            # 关闭会话
            if self.spot_session and not self.spot_session.closed:
                await self.spot_session.close()

            if self.futures_session and not self.futures_session.closed:
                await self.futures_session.close()

            logger.info(f"{self.config.exchange_name} WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"{self.config.exchange_name} 关闭WebSocket连接时发生错误: {e}")
