"""Binance资金费率管理"""
import logging
from typing import List, Optional

import aiohttp

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_cache import FundingRateCache
from crypt_carry.core.exchange.funding.funding_rate_manager import FundingRateManager
from crypt_carry.core.providers.binance.binance_symbol_utils import to_exchange_instrument_id, to_standard_symbol
from crypt_carry.utils.retry import async_retry

logger = logging.getLogger(__name__)

class BinanceFundingManager(FundingRateManager):
    """Binance资金费率管理器"""

    def __init__(self, config: ExchangeConfig, perpetual_pairs: List[str]):
        """初始化Binance资金费率管理器
        
        Args:
            config: 交易所配置
            perpetual_pairs: 永续合约交易对列表，内部标准格式，如 ["BTC/USDT", "ETH/USDT"]
        """
        super().__init__(config)
        self.perpetual_pairs = perpetual_pairs
        self.cache = FundingRateCache()

    async def initialize(self):
        """异步初始化方法"""
        logger.info(f"{self.exchange_name} 初始化资金费率管理器")
        await self.init_funding_rates(self.perpetual_pairs)

    def get_current_rate(self, symbol: str) -> Optional[float]:
        """获取当前资金费率"""
        try:
            return self.cache.get_funding_rate(self.exchange_name, symbol)
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取当前资金费率失败: {str(e)}")
            return None

    @async_retry(max_retries=3, initial_delay=2.0)
    async def fetch_historical_rates(self, symbol: str) -> None:
        """获取历史资金费率数据
        
        Args:
            symbol: 交易对，内部标准格式，如 BTC/USDT:USDT
        """
        try:
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None
            
            async with aiohttp.ClientSession() as session:
                # 转换为Binance API格式
                instrument_id = to_exchange_instrument_id(symbol, is_spot=False)
                logger.debug(f"获取 {self.exchange_name} {symbol} 历史资金费率, 请求symbol: {instrument_id}")

                url = f'{self.config.base_config["API_URLS"]["BINANCE"]["FUTURES"]}/fapi/v1/fundingRate'
                params = {
                    'symbol': instrument_id,
                    'limit': 50
                }

                headers = {}

                async with session.get(url, params=params, headers=headers, proxy=proxy_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        rates = []
                        for item in data:
                            timestamp = int(item['fundingTime'])
                            rate = float(item['fundingRate'])
                            rates.append({
                                'timestamp': timestamp,
                                'rate': rate
                            })

                        # 更新缓存
                        standard_symbol = to_standard_symbol(symbol)
                        self.cache.update_funding_rates(self.exchange_name, standard_symbol, rates)
                        logger.info(f"{self.exchange_name} {symbol} 更新历史资金费率成功: {len(rates)}条")
                    else:
                        logger.error(f"{self.exchange_name} {symbol} 获取历史资金费率失败: HTTP {response.status}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP 错误 {response.status}",
                            headers=response.headers
                        )
                        
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 获取历史资金费率失败: {str(e)}")
            raise

    @async_retry(max_retries=3, initial_delay=2.0)
    async def fetch_latest_funding_rates(self, symbol: str):
        """获取最新资金费率数据
        
        Args:
            symbol: 交易对
        """
        try:
            proxy_url = self.config.http_proxy if self.config.proxy_enabled else None

            # 转换为Binance API格式
            exchange_symbol = to_exchange_instrument_id(symbol, is_spot=False)
            logger.debug(f"获取 {self.exchange_name} {symbol} 最新资金费率, 请求symbol: {exchange_symbol}")

            async with aiohttp.ClientSession() as session:
                url = f'{self.config.base_config["API_URLS"]["BINANCE"]["FUTURES"]}/fapi/v1/premiumIndex'
                params = {'symbol': exchange_symbol}

                async with session.get(url, params=params, proxy=proxy_url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()

                        # 单个交易对的情况下，返回的是单个对象而不是列表
                        if isinstance(data, dict):
                            rate = float(data.get('lastFundingRate', 0))
                            timestamp = int(data.get('time', 0))
                            next_funding_time = int(data.get('nextFundingTime', 0))

                            # 更新缓存
                            await self.update_funding_rate(
                                symbol=symbol,
                                rate=rate,
                                timestamp=timestamp,
                                next_funding_time=next_funding_time
                            )
                    else:
                        logger.error(f"{self.exchange_name} {symbol} 获取最新资金费率失败: HTTP {response.status}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP 错误 {response.status}",
                            headers=response.headers
                        )
                    
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 获取最新资金费率失败: {e}")
            raise
