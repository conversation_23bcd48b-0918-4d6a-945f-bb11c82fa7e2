import asyncio
import logging
import time
from typing import Dict, List, Optional

import ccxt.pro as ccxt

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_scheduler import FundingRateScheduler
from crypt_carry.core.providers.binance.binance_funding import BinanceFundingManager
from crypt_carry.core.providers.binance.binance_market import BinanceMarketManager
from crypt_carry.core.providers.binance.binance_order_client import BinanceOrderClient
from crypt_carry.core.providers.binance.binance_position_rest import BinancePositionRest
from crypt_carry.core.providers.binance.binance_position_utils import standardize_position_data
from crypt_carry.core.providers.binance.binance_symbol_utils import (
    build_internal_symbol,
    to_exchange_instrument_id,
    to_standard_symbol,
)
from crypt_carry.core.providers.binance.binance_ws import BinanceWebSocket
from crypt_carry.core.providers.binance.binance_position_ws import BinancePositionWebsocket
from crypt_carry.core.exchange.market.market_limits import MarketLimitManager
from crypt_carry.utils.data_directory import DataDirectory

# 创建logger，使用模块名作为logger名称
logger = logging.getLogger(__name__)


class BinanceClient(ExchangeClient):
    """币安交易所客户端"""

    def __init__(self, config: ExchangeConfig):
        """初始化Binance客户端

        Args:
            config: 交易所配置
        """
        super().__init__(config)

        # 初始化数据目录
        data_dir = DataDirectory(self.config.base_config)

        # 初始化交易对列表
        self.base_pair = []
        self.spot_pairs = []
        self.perpetual_pairs = []
        self.all_pair = []

        # 初始化CCXT交易所实例
        self.exchange = ccxt.binance({
            # 'apiKey': self.config.api_key,
            # 'secret': self.config.api_secret,
            'timeout': self.config.timeout,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future'
            }
        })

        self.funding_manager: Optional[BinanceFundingManager] = None
        self.funding_scheduler: Optional[FundingRateScheduler] = None

        if self.config.proxy_enabled:
            # 只设置 httpsProxy，因为 Binance API 使用 HTTPS
            self.exchange.httpsProxy = self.config.http_proxy

        # 初始化市场数据管理器（将exchange实例传递给市场管理器）
        self.market_manager = BinanceMarketManager(self.config, self.exchange)

        # 初始化市场限制管理器
        self.market_limit_manager = MarketLimitManager(self.exchange, self.exchange_name)

        # 初始化订单客户端
        self.order_client = BinanceOrderClient(self.exchange, self.exchange_name, self.market_limit_manager)

        # 初始化WebSocket客户端
        self.ws_manager = BinanceWebSocket(self.config, self.handle_ws_message)
        
        # 初始化持仓管理器
        self.position_rest = BinancePositionRest(
            self.exchange,
            self.exchange_name,
            self.market_limit_manager,
            self.all_pair,  # 修正属性名
            self.base_pair,
            self.config.trading_quotes
        )


        # 订阅状态标志
        self.funding_rate_subscription_ready = False
        self.perpetual_subscription_ready = False
        self.spot_subscription_ready = False

        # 价格日志控制
        self.last_price_log: Dict = {}
        self.last_log_time: Dict = {}
        self.log_interval = 300  # 日志打印间隔(秒)
        self.price_change_threshold = 0.001  # 价格变化阈值(0.1%)

    async def start(self):
        """启动Binance客户端"""
        try:
            # 加载市场数据（通过市场管理器）
            if not await self.load_markets_with_retry():
                logger.error(f"{self.exchange_name} 加载市场数据失败")
                return False

            # 初始化历史资金费率
            self.funding_manager = BinanceFundingManager(self.config, self.perpetual_pairs)
            await self.funding_manager.initialize()

            # 启动资金费率定时任务
            self.funding_scheduler = FundingRateScheduler({
                self.exchange_name: self.funding_manager
            }, self.perpetual_pairs)
            await self.funding_scheduler.start()

            # 初始化市场限制信息
            logger.info(f"{self.config.exchange_name} 初始化市场限制信息, \n"
                        f"perpetual_pairs永续合约交易对: {self.perpetual_pairs}\n"
                        f"spot_pairs现货交易对: {self.spot_pairs}")
            # 只传入基础交易对,让 MarketLimitManager 处理合约和现货格式转换
            unique_pairs = list(set(self.perpetual_pairs + self.spot_pairs))
            await self.market_limit_manager.init_market_limits(unique_pairs)

            # 启动WebSocket连接
            await self.ws_manager.connect()

            # 初始化持仓管理器
            self.position_manager = BinancePositionWebsocket(
                self.exchange,
                self.market_limit_manager,
                self.base_pair,
                self.quotes
            )
            await self.position_manager.start()

            # 订阅数据
            await self.subscribe_market_data()

            logger.info(f"{self.exchange_name} 客户端启动成功")
            return True

        except Exception as e:
            logger.error(f"{self.exchange_name} 启动失败: {str(e)}")
            return False

    async def stop(self):
        """停止Binance客户端"""
        try:

            # 停止资金费率管理器
            # await self.funding_manager.stop()

            # 停止资金费率定时任务
            if hasattr(self, 'funding_scheduler'):
                await self.funding_scheduler.stop()

            # 关闭WebSocket连接
            await self.ws_manager.close()

            logger.info(f"{self.exchange_name} 客户端已停止")
        except Exception as e:
            logger.error(f"{self.exchange_name} 停止失败: {str(e)}")

    async def subscribe_market_data(self):
        """订阅市场数据"""
        try:
            # 确保WebSocket已连接
            if not self.ws_manager.is_connected:
                await self.ws_manager.connect()

            # 获取所有交易对
            if not self.spot_pairs and not self.perpetual_pairs:
                logger.warning(f"{self.exchange_name} 没有可订阅的交易对")
                return False

            # 订阅市场数据
            if await self.ws_manager.subscribe_market_data(spot_pairs=self.spot_pairs,
                                                           perpetual_pairs=self.perpetual_pairs):
                logger.info(f"{self.exchange_name} 市场数据订阅成功")
                return True
            else:
                logger.error(f"{self.exchange_name} 市场数据订阅失败")
                return False
        except Exception as e:
            logger.error(f"{self.exchange_name} 订阅市场数据失败: {str(e)}")
            return False

    async def handle_ws_message(self, message: Dict, ws_type: str = 'spot'):
        """
        处理WebSocket消息

        Args:
            message: WebSocket消息
            ws_type: WebSocket类型,spot或futures

        现货消息结构:
        {
            'A': '11969.80000000',  # 卖一累计量
            'B': '10033.40000000',  # 买一累计量
            'a': '0.89520000',      # 卖一价
            'b': '0.89510000',      # 买一价
            's': 'ADAUSDT',         # 交易对
            'u': 10477497051        # 更新ID
        }

        合约格式:
        {
            'e': 'bookTicker',           // 事件类型
            'u': 6191240244188,          // 更新ID
            's': 'SOLUSDT',              // 交易对
            'b': '197.6700',             // 买一价
            'B': '505',                  // 买一量
            'a': '197.6800',             // 卖一价
            'A': '571',                  // 卖一量
            'T': 1734757724967,          // 交易时间
            'E': 1734757724967           // 事件时间
        }
        """
        try:
            # 获取时间戳
            if ws_type == 'futures':
                # 合约消息有T字段作为时间戳
                timestamp = int(message.get('T', time.time() * 1000))
            else:
                # 现货消息使用当前时间
                timestamp = int(time.time() * 1000)

            # 获取交易对信息
            symbol = message.get('s')
            if not symbol:
                return

            # 获取价格
            best_bid = float(message.get('b', 0))
            best_ask = float(message.get('a', 0))

            if best_bid > 0 and best_ask > 0:
                # 计算中间价
                price = (best_bid + best_ask) / 2

                # 构建更新数据
                update_data = {
                    'timestamp': timestamp,
                    f"{'futures' if ws_type == 'futures' else 'spot'}_price": price
                }

                # 更新市场数据
                # 转换为标准格式
                # 从symbol类似"BTCUSDT"解析出base和quote
                symbol_str = symbol
                for quote in ['USDT', 'BUSD', 'BTC', 'ETH']:
                    if symbol_str.endswith(quote):
                        base = symbol_str[:-len(quote)]
                        transformed_symbol = build_internal_symbol(base, quote, is_futures=ws_type == 'futures')
                        standard_symbol = to_standard_symbol(transformed_symbol)
                        self.market_manager.update_market_data(standard_symbol, update_data)
                        break

                # 记录价格更新
                logger.debug(
                    f"{self.exchange_name} {standard_symbol} - {ws_type} 价格更新: "
                    f"买一: {best_bid}, 卖一: {best_ask}, 中间价: {price}"
                )

        except Exception as e:
            logger.error(f"{self.exchange_name} 处理消息失败: {str(e)}")

    async def handle_message(self, message: Dict, message_type: str = None):
        """处理WebSocket消息（实现抽象方法）"""
        try:
            await self.handle_ws_message(message, message_type)
        except Exception as e:
            logger.error(f"{self.exchange_name} 处理消息失败: {str(e)}")

    async def load_markets_with_retry(self) -> bool:
        """加载市场数据

        Returns:
            bool: 是否成功
        """
        try:
            # 委托给市场管理器进行市场数据加载，与OKX实现保持一致
            if await self.market_manager.load_markets_with_retry():
                # 获取已加载的交易对
                self.base_pair, self.spot_pairs, self.perpetual_pairs = self.market_manager.get_trading_pairs()
                self.quotes = self.market_manager.quotes

                return True
            return False
        except Exception as e:
            logger.error(f"{self.exchange_name} 加载市场数据失败: {str(e)}")
            return False

    async def fetch_spot_balance(self) -> Dict[str, float]:
        """获取现货账户USDT和USDC余额"""
        try:
            # 设置市场类型为现货
            self.exchange.options['defaultType'] = 'spot'

            # 获取余额
            balance = await self.exchange.fetch_balance()
            balances = {
                'USDT': float(balance.get('total', {}).get('USDT', 0)),
                'USDC': float(balance.get('total', {}).get('USDC', 0))
            }
            logger.info(f"{self.exchange_name} 现货账户余额: {balances}")
            return balances
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取现货账户余额失败: {str(e)}")
            return {'USDT': 0.0, 'USDC': 0.0}

    async def fetch_futures_balance(self) -> Dict[str, float]:
        """获取合约账户USDT和USDC余额"""
        try:
            # 设置市场类型为合约
            self.exchange.options['defaultType'] = 'future'

            # 获取余额
            balance = await self.exchange.fetch_balance()
            balances = {
                'USDT': float(balance.get('total', {}).get('USDT', 0)),
                'USDC': float(balance.get('total', {}).get('USDC', 0))
            }
            logger.info(f"{self.exchange_name} 合约账户余额: {balances}")
            return balances
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取合约账户余额失败: {str(e)}")
            return {'USDT': 0.0, 'USDC': 0.0}

    async def connect(self):
        """建立WebSocket连接"""
        try:
            await self.ws_manager.connect()
            logger.info(f"{self.exchange_name} WebSocket连接成功")
        except Exception as e:
            logger.error(f"{self.exchange_name} WebSocket连接失败: {str(e)}")
            raise

    async def ping(self):
        """发送ping消息保持连接"""
        try:
            # 调用父类的ping方法记录日志
            await super().ping()

            # 对于Binance，直接使用fetch_time作为ping的替代方案
            server_time = await self.exchange.fetch_time()
            logger.debug(f"{self.exchange_name} 服务器时间: {server_time}")
            return {'status': 'ok', 'timestamp': server_time}
        except Exception as e:
            logger.error(f"{self.exchange_name} ping失败: {str(e)}")
            # 即使ping失败也不抛出异常，只记录错误
            return {'status': 'error', 'error': str(e)}

    async def close(self):
        """关闭所有连接"""
        try:
            # 停止资金费率定时任务
            if hasattr(self, 'funding_scheduler'):
                await self.funding_scheduler.stop()

            # 关闭WebSocket连接
            if self.ws_manager:
                await self.ws_manager.close()

            # 关闭交易所连接
            if hasattr(self.exchange, 'close'):
                await self.exchange.close()

            logger.info(f"{self.exchange_name} 所有连接已关闭")
        except Exception as e:
            logger.error(f"{self.exchange_name} 关闭连接失败: {str(e)}")

    def get_perpetual_pairs(self) -> List[str]:
        """获取永续合约交易对列表

        Returns:
            List[str]: 永续合约交易对列表
        """
        return self.perpetual_pairs

    async def create_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
            quantity: float = None,
        price: Optional[float] = None,
            is_spot: bool = True,
            position_side: Optional[str] = None,
            reduce_only: bool = False,
    ) -> Dict:
        """创建订单"""
        return await self.order_client.create_order(
            symbol=symbol,
            order_type=order_type,
            side=side,
            quantity=quantity,
            price=price,
            is_spot=is_spot,
            position_side=position_side,
            reduce_only=reduce_only,
        )

    async def fetch_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """查询订单信息"""
        return await self.order_client.fetch_order(order_id, symbol, is_spot)

    async def cancel_order(self, order_id: str, symbol: str, is_spot: bool = True) -> Dict:
        """取消订单"""
        return await self.order_client.cancel_order(order_id, symbol, is_spot)

    async def fetch_open_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询当前未完成的订单"""
        return await self.order_client.fetch_open_orders(symbol, is_spot)

    async def fetch_closed_orders(self, symbol: str = None, is_spot: bool = True) -> List[Dict]:
        """查询历史订单"""
        return await self.order_client.fetch_closed_orders(symbol, is_spot)

    async def fetch_positions_by_ws(self) -> List[Dict]:
        """通过WebSocket获取持仓数据"""
        return await self.position_manager.get_positions()

    async def fetch_positions_by_rest(self) -> List[Dict]:
        return await self.position_rest.fetch_positions_rest()

    def get_standard_symbol(self, symbol: str) -> str:
        """将交易所特定的交易对格式转换为标准化格式
        Args:
            symbol: 任意格式的交易对，可能是交易所特定格式或内部格式

        Returns:
            str: 标准化格式的交易对，如 BTC/USDT
        """
        try:
            return to_standard_symbol(symbol)
        except Exception as e:
            logger.error(f"转换交易对格式错误: {str(e)}, 原始交易对: {symbol}")
            # 如果转换失败，返回原始交易对
            return symbol

    async def reload_detect_exclude_pairs(self):
        """重新加载需要排除检测的交易对列表"""
        try:
            # 通过市场管理器重新加载排除列表
            if hasattr(self.market_manager, 'load_detect_exclude_pairs'):
                self.detect_exclude_pairs = await self.market_manager.load_detect_exclude_pairs()
                logger.info(f"{self.exchange_name} 重新加载排除交易对: {self.detect_exclude_pairs}")
            else:
                # 如果市场管理器没有这个方法，从配置中获取
                self.detect_exclude_pairs = self.config.trading_detect_exclude
                logger.info(f"{self.exchange_name} 从配置加载排除交易对: {self.detect_exclude_pairs}")
        except Exception as e:
            logger.error(f"{self.exchange_name} 重新加载排除交易对失败: {str(e)}")
            # 设置为空列表作为备选
            self.detect_exclude_pairs = []
