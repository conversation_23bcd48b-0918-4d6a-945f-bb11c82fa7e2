"""
交易机会检测器 - 负责识别和评估资金费率套利交易机会
"""
import logging
from typing import Dict, List

from crypt_carry.constant.direction_type import DirectionType
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_cache import FundingRateCache
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.core.trading.scoring.opportunity_scorer import OpportunityScorer
from util.formatter import format_funding_rate

logger = logging.getLogger(__name__)


class OpportunityDetector:
    """交易机会检测器，负责识别和评估资金费率套利交易机会"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化交易机会检测器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name

        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)

        # 获取策略配置
        self.strategy_config = self.config.get_strategy_config()
        self.scoring_config = self.config.get_config_value('EXCHANGE_CONFIG',
                                                           f"{self.exchange_name.upper()}.SCORING",
                                                           {})

        # 初始化资金费率管理器
        self.funding_rate_cache = FundingRateCache()

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

        # 初始化机会评分器
        self.opportunity_scorer = OpportunityScorer()

    async def detect_opportunities(self) -> List[Dict]:
        """检测交易机会
        
        Returns:
            List[Dict]: 交易机会列表，每个机会包含：
            {
                'symbol': str,              # 交易对
                'in_position': bool,        # 是否已有持仓
                'trade_info': {
                    'direction': str,       # 交易方向 @direction_type
                    'spot_price': float,    # 现货价格
                    'futures_price': float, # 期货价格
                    'basis_rate': float,    # 基差率
                    'funding_rate': float,  # 资金费率
                    'expected_profit': float # 预期收益
                },
                'ewma_score': float,        # EWMA得分
                'weight_score': float       # 权重得分
            }
        """
        # 获取所有可交易的交易对
        trading_symbols = self.funding_rate_cache.get_symbols_by_exchange(self.exchange_name)

        # 获取交易所配置
        allow_negative_trading = self.strategy_config.get('NEGATIVE_TRADING', False)
        opportunities = []

        logger.info(f"{self.exchange_name} 开始检测交易机会, 排除交易对: {self.exchange_client.detect_exclude_pairs}")
        for standard_symbol in trading_symbols:
            try:
                # 检查是否在交易对排除列表中
                if standard_symbol in self.exchange_client.detect_exclude_pairs:
                    logger.warning(f"{self.exchange_name} {standard_symbol} 在交易对排除列表中，跳过")
                    continue

                # 获取资金费率,标准化后的资金费率,基差率
                funding_rate = self.funding_rate_cache.get_current_rate(self.exchange_name, standard_symbol)
                normalized_funding_rate = self.funding_rate_cache.get_normal_funding_rate(self.exchange_name,
                                                                                          standard_symbol)
                basis_data = self.market_data_manager.get_basis_data(self.exchange_name, standard_symbol)

                if not funding_rate or not basis_data:
                    logger.debug(f"{self.exchange_name} {standard_symbol} 无资金费率或基差率数据，跳过")
                    continue

                basis_rate = basis_data.get('basis_rate', 0)

                # 计算权重得分 - 使用标准化后的资金费率
                weight_score = self.opportunity_scorer.calculate_score(
                    basis_rate=basis_rate,
                    funding_rate=normalized_funding_rate
                )

                # 计算EWMA得分
                ewma_score = self.opportunity_scorer.calculate_ewma_score(
                    symbol=standard_symbol,
                    current_score=weight_score
                )

                # 根据资金费率的正负确定交易方向
                # 资金费率为正时，做多现货做空期货 DirectionType.LONG_SPOT_SHORT_FUTURES
                # 资金费率为负时，做多期货做空现货 DirectionType.SHORT_SPOT_LONG_FUTURES
                direction = (
                    DirectionType.LONG_SPOT_SHORT_FUTURES
                    if normalized_funding_rate > 0
                    else DirectionType.SHORT_SPOT_LONG_FUTURES
                )

                # 检查是否允许负费率交易
                if not allow_negative_trading and direction == DirectionType.SHORT_SPOT_LONG_FUTURES:
                    logger.debug(
                        f"{self.exchange_name} {standard_symbol} 不允许负费率交易 "
                        f"(资金费率: {format_funding_rate(normalized_funding_rate)}), 跳过"
                    )
                    continue

                # 如果当前已有持仓，且方向相反，则跳过
                position_manager = PositionManager.get_instance()
                current_position = position_manager.get_position(self.exchange_name, standard_symbol)
                if current_position and current_position.direction != direction:
                    logger.debug(
                        f"{self.exchange_name} {standard_symbol} 当前持仓方向 {current_position.direction} "
                        f"与目标方向 {direction} 不一致，跳过"
                    )
                    continue

                # 创建交易机会对象
                opportunity = {
                    'symbol': standard_symbol,
                    'in_position': bool(current_position),
                    'trade_info': {
                        'direction': direction,
                        'spot_price': basis_data.get('spot_price', 0),
                        'futures_price': basis_data.get('futures_price', 0),
                        'basis_rate': basis_rate,
                        'funding_rate': funding_rate,
                        'expected_profit': abs(basis_rate) + abs(funding_rate),
                        'normalized_funding_rate': normalized_funding_rate,
                        'normalized_expected_profit': abs(basis_rate) + abs(normalized_funding_rate)
                    },
                    'ewma_score': ewma_score,
                    'weight_score': weight_score
                }

                opportunities.append(opportunity)

            except Exception as e:
                logger.error(f"{self.exchange_name} {standard_symbol} 计算交易机会失败: {str(e)}")
                continue

        # 按总得分排序
        opportunities.sort(key=lambda x: x['ewma_score'] + x['weight_score'], reverse=True)

        return opportunities
