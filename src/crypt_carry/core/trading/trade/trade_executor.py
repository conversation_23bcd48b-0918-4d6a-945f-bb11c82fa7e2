"""
交易执行器 - 负责执行具体的交易操作
"""
import logging
from typing import Dict

from crypt_carry.constant.trade_type import TradeType
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.funding.funding_rate_cache import FundingRateCache
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.exchange.order.order_executor import OrderExecutor
from crypt_carry.core.exchange.order.order_state import OrderState
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class TradeExecutor:
    """交易执行器，负责执行具体的交易操作"""
    _instances = {}  # 类变量，存储不同交易所的实例

    @classmethod
    def get_instance(cls, exchange_client: ExchangeClient):
        """获取特定交易所的交易执行器实例
        
        Args:
            exchange_client: 交易所客户端
            
        Returns:
            TradeExecutor: 交易执行器实例
        """
        exchange_name = exchange_client.exchange_name
        if exchange_name not in cls._instances:
            cls._instances[exchange_name] = cls(exchange_client)
        return cls._instances[exchange_name]
    
    def __init__(self, exchange_client: ExchangeClient):
        """初始化交易执行器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name

        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)

        # 初始化订单执行器
        self.order_executor = OrderExecutor(self.exchange_client)

        # 初始化持仓管理器
        self.position_manager = PositionManager.get_instance()

        # 初始化持仓监控器（延迟初始化，避免循环依赖）
        self.position_monitor = None

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

        # 初始化钉钉消息管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 初始化资金费率管理器
        self.funding_rate_cache = FundingRateCache()

        # 初始化待处理任务管理器（单例模式）
        self.pending_tasks_manager = PendingTasksManager.get_instance()

        # 获取交易配置
        self.strategy_config = self.config.get_strategy_config()
        self.max_positions = self.strategy_config.get('MAX_POSITIONS', 5)
        self.leverage = self.strategy_config.get('LEVERAGE', 1)
        self.use_balance = self.strategy_config.get('USE_BALANCE', 1000)
        self.max_position_value = self.strategy_config.get('MAX_POSITION_VALUE', 50000)
        self.min_position_value = self.strategy_config.get('MIN_POSITION_VALUE', 200)
        self.max_open_value = self.strategy_config.get('MAX_OPEN_VALUE', 5000)
        self.max_retries = self.strategy_config.get('MAX_RETRIES', 3)
        self.retry_delay = self.strategy_config.get('RETRY_DELAY', 1)

    async def open_new_position(self, symbol: str, direction: str) -> bool:
        """开启新仓位
        
        Args:
            symbol: 交易对
            direction: 交易方向 @direction_type

        说明:
            单个交易对资金分配(总额1000 USDT):
            假设杠杆倍数为N：
            - 开仓价值：1000 * N/(N+1) USDT
            - 现货端：1000 * N/(N+1) USDT (1倍杠杆)
            - 合约端：1000 * 1/(N+1) USDT (N倍杠杆，价值与现货相等)
        """
        # 获取账户余额和杠杆设置
        # total_balance = await self._get_total_balance()
        # total_balance = 300
        # position_balance = total_balance / self.max_positions  # 1000 USDT
        position_balance = self.use_balance
        # 根据杠杆倍数计算开仓价值
        # 设开仓价值为x，则：
        # 现货需要x USDT (1倍杠杆)
        # 合约需要x/leverage USDT (leverage倍杠杆)
        # 总资金限制：x + x/leverage = position_balance
        # 解得：x = position_balance * leverage/(leverage + 1)
        position_value = position_balance * self.leverage / (self.leverage + 1)  # 现货可用金额
        futures_margin = position_value / self.leverage  # 合约保证金

        # 获取账户余额
        spot_balance = await self.exchange_client.fetch_spot_balance()
        balance = float(spot_balance.get('FREE_USDT', 0))
        try:
            # 获取当前锁定的资金
            locked_funds = await self.pending_tasks_manager.get_locked_funds(self.exchange_name)
            available_balance = balance - locked_funds

            logger.info(f"{self.exchange_name} 现货账户余额: {balance:.2f} USDT, "
                        f"锁定资金: {locked_funds:.2f} USDT, "
                        f"可用余额: {available_balance:.2f} USDT")

            # 检查余额是否足够
            if available_balance < position_balance:
                logger.error(f"{self.exchange_name} 现货账户可用余额不足，无法开启新仓位, 等待释放资金")
                # 恢复任务,等待释放资金
                position = {
                    'symbol': symbol,
                    'direction': direction,
                    'trade_type': TradeType.CREATE
                }
                order_state = OrderState(
                    symbol=symbol,
                    futures_order_id="",
                    spot_order_id="",
                    position=position,
                    futures_fill_quantity=0,
                    spot_fill_quantity=0
                )
                await self.pending_tasks_manager.recover_task(self.exchange_name, order_state)
                # self.ding_talk_manager.send_message(f"{self.exchange_name} 现货账户可用余额不足，无法开启新仓位, 等待释放资金", prefix=self.exchange_name)
                return False

            # 计算目标仓位
            target_position = self._gen_open_position(
                symbol=symbol,
                spot_amt=position_value,  # 传入计算后的目标持仓价值
                future_amt=futures_margin * self.leverage,
                direction=direction
            )
            logger.info(f"准备开仓: {symbol}\n"
                        f"目标持仓价值: {position_value:.2f} USDT\n"
                        f"现货保证金: {target_position['spot_amt']:.2f} USDT, 实际名义价值:{target_position['spot_amt']:.2f} USDT \n"
                        f"合约保证金: {futures_margin:.2f} USDT, 实际名义价值:{target_position['future_amt']:.2f} USDT, 使用({self.leverage}倍杠杆)")

            # ======= 锁定资金，阻断后续交易 =======
            # 锁定现货端资金，指定交易对
            await self.pending_tasks_manager.lock_funds(self.exchange_name, position_value, symbol)

            await self._execute_spot_futures_trade(position=target_position)
        except Exception as e:
            error_msg = f"{self.exchange_name} {symbol} 开仓失败: {str(e)}"
            logger.error(error_msg)
            self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)
            # 发生异常时，确保释放锁定的资金
            await self.pending_tasks_manager.release_funds(self.exchange_name, position_value, symbol)
            logger.info(f"{self.exchange_name} {symbol} 异常处理中释放锁定资金 {position_value:.2f} USDT")
            return False

    async def close_position(self, symbol: str) -> bool:
        """平仓指定交易对的持仓
        
        Args:
            symbol: 交易对
            
        Returns:
            bool: 是否成功平仓
        """
        try:
            # 获取当前持仓
            current_position = self.position_manager.get_position(self.exchange_name, symbol)
            if not current_position:
                logger.warning(f"{self.exchange_name} {symbol} 未找到持仓信息，无需平仓")
                return

            # 获取当前市场数据
            basis_rate = self.market_data_manager.get_basis_rate(self.exchange_name, symbol)
            funding_rate = self.funding_rate_cache.get_current_rate(self.exchange_name, symbol)
            if basis_rate is None or funding_rate is None:
                logger.warning(
                    f"{self.exchange_name} {symbol} 未找到基差率或资金费率，跳过平仓 - "
                    f"basis_rate: {basis_rate}, funding_rate: {funding_rate}"
                )
                return

            # 计算平仓订单
            target_position = self._gen_close_position(current_position)

            # 执行平仓订单
            await self._execute_spot_futures_trade(position=target_position)
        except Exception as e:
            logger.error(f"{self.exchange_name} {symbol} 平仓失败: {str(e)}")
            raise

    async def _execute_spot_futures_trade(self, position: Dict) -> bool:
        # 定义订单失败回调
        async def on_order_failed(symbol: str, failed_position: Dict):
            """订单失败回调函数
            
            Args:
                symbol: 交易对
                failed_position: 失败的订单信息
            """
            # 获取当前重试次数
            retry_count = failed_position.get('retry_count', 0)

            # 如果超过最大重试次数,则终止重试
            if retry_count >= self.max_retries:
                error_msg = f"{self.exchange_name} {symbol} 订单执行失败,已达到最大重试次数 {self.max_retries}"
                logger.error(error_msg)
                self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)
                return

            try:
                logger.warning(f"{self.exchange_name} {symbol} 订单执行失败，第 {retry_count + 1} 次重试")

                # 增加重试计数
                failed_position['retry_count'] = retry_count + 1

                # 重新执行订单
                await self.order_executor.execute_orders(failed_position, on_order_failed)

            except Exception as e:
                error_msg = f"{self.exchange_name} {symbol} 订单重试失败,放弃本次套利.: {str(e)}"
                logger.error(error_msg)
                self.ding_talk_manager.send_message(error_msg, prefix=self.exchange_name)

        # 设置订单执行冷却期，防止由于现货和合约订单成交时差导致的错误平仓
        try:
            symbol = position['symbol']
            logger.info(f"{self.exchange_name} {symbol} 设置订单执行冷却期")
            # 延迟初始化PositionMonitor，避免循环依赖
            if self.position_monitor is None:
                from crypt_carry.core.trading.monitor.position_monitor import PositionMonitor
                self.position_monitor = PositionMonitor(self.exchange_client)
            self.position_monitor.set_order_cooldown(symbol)
        except Exception as e:
            logger.error(f"{self.exchange_name} {position['symbol']} 设置订单执行冷却期失败: {str(e)}")

        # 执行订单
        success = await self.order_executor.execute_orders(position, on_order_failed)

        # 发送成功消息
        message = (
            f"订单执行成功\n"
            f"交易所: {self.exchange_name}\n"
            f"交易对: {position['symbol']}\n"
            f"方向: {position['direction']}\n"
            f"类型: {position['trade_type']}\n"
            f"result: {success}"
        )
        logger.debug(message)

    def _gen_open_position(self, symbol: str, spot_amt: float, future_amt: float, direction: str) -> Dict:
        """计算目标仓位
        
        Args:
            symbol: str, 交易对
            spot_amt: float, 现货可用余额
            future_amt: float, 合约可用余额
            direction: str, 交易方向
            
        Returns:
            Dict: 目标仓位信息
                {
                    'symbol': symbol,
                    'direction': direction,
                    'trade_type': TradeType.CREATE,
                    'spot_amt': spot_amt,  # 现货可用余额,平仓时使用
                    'future_amt': future_amt,  # 合约可用余额,平仓时使用
                    'spot_quantity': float,  # 现货数量,平仓时使用
                    'future_quantity': float  # 合约数量，平仓时使用
                }
        """
        return {
            'symbol': symbol,
            'direction': direction,
            'trade_type': TradeType.CREATE,
            'spot_amt': spot_amt,  # 现货可用余额
            'future_amt': future_amt,  # 合约可用余额
        }

    def _gen_close_position(self, current_position) -> Dict:
        """计算平仓订单
        
        Args:
            current_position: 当前持仓信息，Position类型，包含：
                - symbol: 交易对
                - direction: 持仓方向
                - spot_position: 现货持仓数量
                - futures_position: 合约持仓数量
                - spot_entry_price: 现货开仓价格
                - futures_entry_price: 合约开仓价格
                
        Returns:
            Dict: 平仓订单信息
                {
                    'symbol': str,
                    'direction': str,
                    'trade_type': str,
                    'spot_amt': spot_amt,  # 现货可用余额,平仓时使用
                    'future_amt': future_amt,   # 合约可用余额,平仓时使用
                    'spot_quantity': float,  # 现货数量,平仓时使用
                    'future_quantity': float  # 合约数量，平仓时使用
                }
        """
        symbol = current_position.symbol
        direction = current_position.direction

        # 使用当前持仓数量作为平仓数量
        spot_quantity = abs(current_position.spot_position)
        future_quantity = abs(current_position.futures_position)

        return {
            'symbol': symbol,
            'direction': direction,
            'trade_type': TradeType.CLOSE,
            'spot_quantity': spot_quantity,  # 现货数量,平仓时使用
            'future_quantity': future_quantity  # 合约数量，平仓时使用
        }
