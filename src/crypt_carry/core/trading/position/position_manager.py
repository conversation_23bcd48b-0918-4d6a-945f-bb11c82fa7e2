import logging
from datetime import datetime, timezone
from typing import Dict, Optional, Set, List

logger = logging.getLogger(__name__)


class Position:
    """套利持仓类，包含现货和合约两个方向的持仓信息"""

    def __init__(self, symbol: str, quantity: float):
        """初始化持仓
        
        Args:
            symbol: 交易对，内部标准格式，如 BTC/USDT
            quantity: 持仓数量
        """
        self.symbol = symbol
        self.quantity = quantity
        self.entry_time = datetime.now(timezone.utc)

        # 持仓方向
        self.direction = ""
        # 开仓时的信息
        self.entry_basis_rate = 0.0
        self.real_basis_rate = 0.0  # 建仓后真实的基差率
        self.entry_funding_rate = 0.0
        self.entry_spot_price = 0.0
        self.entry_futures_price = 0.0

        # 现货持仓信息
        self.spot_exchange = ""
        self.spot_position = 0.0
        self.spot_entry_price = 0.0

        # 合约持仓信息
        self.futures_exchange = ""
        self.futures_position = 0.0
        self.futures_entry_price = 0.0

        # 持仓状态
        self.is_complete = False  # 标记现货和合约是否都已开仓

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'entry_time': self.entry_time,
            'direction': self.direction,
            'entry_basis_rate': self.entry_basis_rate,
            'real_basis_rate': self.real_basis_rate,
            'entry_funding_rate': self.entry_funding_rate,
            'entry_spot_price': self.entry_spot_price,
            'entry_futures_price': self.entry_futures_price,
            'spot_exchange': self.spot_exchange,
            'spot_position': self.spot_position,
            'spot_entry_price': self.spot_entry_price,
            'futures_exchange': self.futures_exchange,
            'futures_position': self.futures_position,
            'futures_entry_price': self.futures_entry_price,
            'is_complete': self.is_complete
        }


class PositionManager:
    """持仓管理器，管理所有套利持仓"""

    _instance = None

    @classmethod
    def get_instance(cls) -> 'PositionManager':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """初始化"""
        if PositionManager._instance is not None:
            raise Exception("PositionManager 是单例类，请使用 get_instance() 获取实例")
            
        # 使用字典存储持仓信息，key为交易所
        self.positions: Dict[str, Dict[str, Position]] = {}
        self.position_history: Dict[str, Dict[str, List[Dict]]] = {}

    def get_current_positions(self, exchange: str = None) -> Set[Position]:
        """获取当前所有完整持仓的交易对
        
        Args:
            exchange: 交易所名称，如果为None则返回所有交易所的持仓
            
        Returns:
            Set[Position]: 当前所有完整持仓的交易对
        """
        positions = set()
        for symbol, position in self.positions.get(exchange, {}).items():
            if position.is_complete:
                positions.add(position)
        return positions

    def get_position(self, exchange: str, symbol: str) -> Optional[Position]:
        """获取指定交易对的持仓信息
        
        Args:
            exchange: 交易所名称
            symbol: 交易对，任意格式
            
        Returns:
            Optional[Position]: 持仓信息
        """
        return self.positions.get(exchange, {}).get(symbol)

    def get_position_by_market(self, exchange: str, symbol: str, market_type: str) -> Optional[Dict]:
        """获取指定市场的持仓信息
        
        Args:
            exchange: 交易所名称
            symbol: 交易对，任意格式
            market_type: 市场类型 (spot/futures)
            
        Returns:
            Optional[Dict]: 持仓信息
        """
        position = self.get_position(exchange, symbol)
        if not position:
            return None

        if market_type == 'spot' and position.spot_exchange == exchange:
            return {
                'position': position.spot_position,
                'entry_price': position.spot_entry_price
            }
        elif market_type == 'futures' and position.futures_exchange == exchange:
            return {
                'position': position.futures_position,
                'entry_price': position.futures_entry_price
            }
        return None

    def update_position(self, exchange: str, symbol: str,
                        spot_price: Optional[float] = None, spot_size: Optional[float] = None,
                        futures_price: Optional[float] = None, futures_size: Optional[float] = None,
                        direction: Optional[str] = None,
                        basis_rate: Optional[float] = None,
                        funding_rate: Optional[float] = None):
        """更新持仓信息
        
        Args:
            exchange: 交易所名称
            symbol: 交易对，任意格式
            spot_price: 现货价格（可选）
            spot_size: 现货仓位大小（可选）
            futures_price: 期货价格（可选）
            futures_size: 期货仓位大小（可选）
            direction: 持仓方向（可选）
            basis_rate: 基差率（可选）
            funding_rate: 资金费率（可选）
        """
        try:

            # 如果不存在该交易对的持仓，创建新持仓
            if exchange not in self.positions:
                self.positions[exchange] = {}
                self.position_history[exchange] = {}
            if symbol not in self.positions[exchange]:
                self.positions[exchange][symbol] = Position(symbol, abs(spot_size))

            position = self.positions[exchange][symbol]
            position.direction = direction
            # 更新持仓信息
            position.spot_exchange = exchange
            position.futures_exchange = exchange
            position.spot_position = spot_size
            position.futures_position = futures_size
            if spot_price:
                position.entry_spot_price = spot_price
            if futures_price:
                position.entry_futures_price = futures_price

            # 更新费率信息
            if basis_rate is not None:
                position.entry_basis_rate = basis_rate
            if funding_rate is not None:
                position.entry_funding_rate = funding_rate

            # 检查是否完整持仓
            position.is_complete = bool(position.spot_position and position.futures_position)

            # 添加到历史记录
            if symbol not in self.position_history[exchange]:
                self.position_history[exchange][symbol] = []
            self.position_history[exchange][symbol].append({
                'direction': direction,
                'spot_size': position.spot_position,
                'futures_size': position.futures_position,
                'entry_time': position.entry_time,
                'basis_rate': position.entry_basis_rate,
                'funding_rate': position.entry_funding_rate
            })

            logger.debug(
                f"更新持仓 - {exchange}: {symbol}: "
                f"direction: {direction}, "
                f"现货({position.spot_exchange}): {position.spot_position:.4f} @ {position.spot_entry_price:.2f}, "
                f"合约({position.futures_exchange}): {position.futures_position:.4f} @ {position.futures_entry_price:.2f}"
            )

        except Exception as e:
            logger.error(f"更新持仓失败: {exchange}, {symbol}, {str(e)}")

    def has_position(self, exchange: str, symbol: str) -> bool:
        """检查是否有该交易对的持仓"""
        position = self.get_position(exchange, symbol)
        if not position:
            return False
        return abs(position.spot_position) > 0 or abs(position.futures_position) > 0

    def check_position_balance(self, exchange: str, symbol: str) -> bool:
        """检查现货和合约持仓是否平衡"""
        position = self.get_position(exchange, symbol)
        if not position:
            return True

        # 允许0.1%的误差
        return abs(position.spot_position + position.futures_position) < abs(position.spot_position) * 0.001

    def clear_position(self, exchange: str) -> None:
        """清空指定交易所的所有持仓"""
        if exchange in self.positions:
            self.positions[exchange].clear()
            self.position_history[exchange].clear()
            logger.debug(f"清空 {exchange} 的所有持仓")
            

    def close_position(self, exchange: str, symbol: str) -> None:
        """关闭指定交易对的持仓
        
        Args:
            exchange: 交易所名称
            symbol: 交易对，任意格式
        """

        if exchange in self.positions and symbol in self.positions[exchange]:
            position = self.positions[exchange][symbol]
            logger.info(
                f"关闭持仓 - {exchange}: {symbol}: "
                f"现货({position.spot_exchange}): {position.spot_position:.4f}, "
                f"合约({position.futures_exchange}): {position.futures_position:.4f}"
            )
            del self.positions[exchange][symbol]

    def get_rebalance_orders(self, exchange: str, target_positions: Dict[str, Dict]) -> Dict[str, Dict]:
        """计算需要的调仓订单
        
        Args:
            exchange: 交易所名称
            target_positions: 目标持仓，格式为 {symbol: {exchange_market: size}}
            
        Returns:
            Dict[str, Dict]: 调仓订单，格式为 {symbol: {exchange_market: size}}
        """
        if not target_positions:
            return {}

        rebalance_orders = {}

        for symbol, target in target_positions.items():
            # 获取当前持仓
            current_position = self.get_position(exchange, symbol)

            # 初始化该交易对的调仓订单
            rebalance_orders[symbol] = {}

            for exchange_market, target_pos in target.items():
                # 确定当前持仓
                exchange, market_type = exchange_market.split('_')
                current_market_pos = self.get_position_by_market(exchange, symbol, market_type)
                current_pos = current_market_pos['position'] if current_market_pos else 0

                # 计算需要调整的仓位
                delta = target_pos - current_pos
                if abs(delta) > 0:
                    rebalance_orders[symbol][exchange_market] = delta

            # 如果该交易对没有需要调整的仓位，删除该条目
            if not rebalance_orders[symbol]:
                del rebalance_orders[symbol]

        return rebalance_orders

    def get_position_history(self, exchange: str, symbol: str) -> List[Dict]:
        """获取仓位历史"""
        if exchange not in self.position_history:
            return []
        return self.position_history[exchange].get(symbol, [])

    def get_last_entry_time(self, exchange: str, symbol: str) -> float:
        """获取最近一次开仓时间"""
        history = self.get_position_history(exchange, symbol)
        if not history:
            return 0
        return history[-1]['entry_time']
