"""
持仓调整器,负责计算和执行持仓调整
"""
import logging
from typing import Dict, List, Tuple

from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.utils.ding_talk_manager import DingTalkManager
from util.formatter import format_funding_rate

logger = logging.getLogger(__name__)


class PositionAdjuster:
    """持仓调整器"""

    def __init__(self):
        self.position_manager = PositionManager.get_instance()
        self.ding_talk_manager = DingTalkManager.get_instance()

    def calculate_adjustments(self, exchange: str, max_positions: int, opportunities: List[Dict]) -> Tuple[
        List[str], List[Dict]]:
        """计算需要调整的持仓
        
        Args:
            exchange: 交易所名称
            opportunities: 交易机会列表，按得分从高到低排序
            max_positions: 最大持仓数量
            
        Returns:
            Tuple[List[str], List[Dict]]: (需要移除的持仓, 需要添加的持仓)
                - positions_to_remove: List[str] 需要移除的交易对符号列表，例如 ['BTC/USDT', 'ETH/USDT']
                - positions_to_add: List[Dict] 需要添加的交易机会列表，每个机会包含：
                    {
                        'symbol': str,  # 交易对符号，如 'BTC/USDT'
                        'ewma_score': float,  # EWMA得分
                        'weight_score': float,  # 权重得分
                        'trade_info': {
                            'direction': str,  # 交易方向，@DirectionType
                            'spot_price': float,  # 现货价格
                            'futures_price': float,  # 合约价格
                            'basis_rate': float,  # 基差率
                            'funding_rate': float,  # 资金费率
                            'expected_profit': float  # 预期收益
                            'normalized_funding_rate': float,  # 标准化资金费率
                            'normalized_expected_profit': float  # 标准化预期收益
                        }
                    }
        """
        try:
            # 获取当前持仓
            current_positions = self.position_manager.get_current_positions(exchange)
            current_symbols = set()
            if current_positions:
                for position in current_positions:
                    current_symbols.add(position.symbol)
            logger.info(f"{exchange} 当前持仓: {current_symbols}")
            # 获取得分最高的机会
            top_opportunities = opportunities[:max_positions]
            top_symbols = {opp['symbol'] for opp in top_opportunities}

            # 计算需要移除和添加的持仓
            positions_to_remove = list(current_symbols - top_symbols)
            positions_to_add = [
                opp for opp in top_opportunities
                if opp['symbol'] not in current_symbols
            ]

            # 情况1: 当前持仓数量超过最大限制，需要减仓到最大限制
            if len(current_symbols) > max_positions:
                logger.info(f"{exchange} 当前持仓数量 {len(current_symbols)} 超过最大限制 {max_positions}，需要减仓")

                # 对当前持仓按照得分排序（需要构建字典将持仓和得分关联起来）
                current_symbol_scores = {}
                for symbol in current_symbols:
                    # 查找该持仓对应的机会及得分
                    opp = next((o for o in opportunities if o['symbol'] == symbol), None)
                    # 如果在机会列表中找到，使用其得分；否则使用-1分
                    score = opp['ewma_score'] if opp else -1
                    current_symbol_scores[symbol] = score

                # 按得分降序排序当前持仓
                sorted_positions = sorted(current_symbol_scores.keys(),
                                          key=lambda s: current_symbol_scores[s],
                                          reverse=True)

                # 保留得分最高的max_positions个持仓，移除其余的
                to_keep = set(sorted_positions[:max_positions])
                to_remove = list(current_symbols - to_keep)

                # 记录减仓信息
                for symbol in to_remove:
                    score = current_symbol_scores.get(symbol, 0)
                    logger.info(f"{exchange} 计划减仓: {symbol}, EWMA得分: {score:.6f}")

                msg = (
                    f"{exchange} 持仓调整计划 - {exchange}:\n"
                    f"  当前持仓: {len(current_symbols)} 个 {current_symbols}\n"
                    f"  计划减仓: {len(to_remove)} 个 {to_remove}\n"
                    f"  计划新增: 0 个 (等待持仓数量降至最大限制)"
                )
                logging.info(msg)
                # 发送钉钉消息
                self.ding_talk_manager.send_message(message=msg, prefix=exchange)
                return to_remove, []

            # 情况2: 当前持仓数量小于最大限制，只返回需要轮入的
            if len(current_symbols) < max_positions:
                # 计算还可以新增的持仓数量
                remaining_slots = max_positions - len(current_symbols)
                # 只取前 remaining_slots 个机会
                positions_to_add = positions_to_add[:remaining_slots]
                for opp in positions_to_add:
                    logger.info(
                        f"{exchange} 新增持仓: {opp['symbol']}, "
                        f"最新资金费率:{format_funding_rate(opp['trade_info']['funding_rate'])}, "
                        f"标准化资金费率:{format_funding_rate(opp['trade_info']['normalized_funding_rate'])}, "
                        f"(EWMA得分:{opp['ewma_score']:.6f}, "
                        f"权重得分:{opp['weight_score']:.6f})"
                    )
                return [], positions_to_add

            logger.debug(f"{exchange} 需要轮动. positions_add : {positions_to_add}")
            logger.debug(f"{exchange} 需要轮动. positions_remove : {positions_to_remove}")
            # 如果需要轮动，确保轮入和轮出的数量相等
            if positions_to_remove and positions_to_add:
                # 按EWMA得分排序
                positions_to_add.sort(key=lambda x: x['ewma_score'], reverse=True)

                # 截取等量的轮入和轮出交易对
                min_count = min(len(positions_to_remove), len(positions_to_add))
                positions_to_remove = positions_to_remove[:min_count]
                positions_to_add = positions_to_add[:min_count]

                # 筛选真正需要轮动的交易对
                final_positions_to_remove = []
                final_positions_to_add = []

                # 记录轮动日志
                for out_symbol in positions_to_remove:
                    logger.info(f"{exchange} 准备轮出: {out_symbol}")
                    # 找到对应的机会信息
                    out_opp = next(
                        (opp for opp in opportunities if opp['symbol'] == out_symbol),
                        None
                    )

                    # 即使交易对不在当前机会列表中，也允许轮出
                    # 如果找不到机会信息，则创建一个默认值供后续使用
                    if not out_opp:
                        logger.info(f"{out_symbol} 不再是有利机会，将继续轮出")
                        # 创建一个最简单的机会信息对象，仅包含必要信息
                        out_opp = {
                            'symbol': out_symbol,
                            'ewma_score': 0,  # 默认EWMA得分为零
                            'weight_score': 0,  # 默认权重得分为零
                            'trade_info': {
                                'funding_rate': 0,  # 资金费率默认为零
                                'expected_profit': 0,  # 预期收益默认为零
                                'direction': '',  # 空字符串表示无方向
                                'spot_price': 0,
                                'futures_price': 0,
                                'basis_rate': 0,
                                'normalized_funding_rate': 0,
                                'normalized_expected_profit': 0,
                            }
                        }

                    # 获取要轮入的机会
                    in_opp = next(
                        (opp for opp in positions_to_add
                         if opp['symbol'] not in [p['symbol'] for p in final_positions_to_add]),
                        None
                    )
                    if not in_opp:
                        logger.warning(f"没有可用的轮入机会，跳过轮动 {out_symbol}")
                        continue

                    logger.info(f"{exchange} 准备轮动: 轮出 {out_symbol} -> 轮入 {in_opp['symbol']}")
                    # 比较资金费率和预期收益
                    out_funding_rate = abs(out_opp['trade_info']['normalized_funding_rate'])
                    out_expected_profit = out_opp['trade_info']['normalized_expected_profit']

                    in_funding_rate = abs(in_opp['trade_info']['normalized_funding_rate'])
                    in_expected_profit = in_opp['trade_info']['normalized_expected_profit']

                    # 截断到小数点后5位再比较，避免因极小差异而产生不必要的轮动
                    out_funding_rate_rounded = round(out_funding_rate, 5)
                    in_funding_rate_rounded = round(in_funding_rate, 5)

                    # 只有当轮入的收益显著更好时才进行轮动
                    if in_funding_rate_rounded > out_funding_rate_rounded:
                        final_positions_to_remove.append(out_symbol)
                        final_positions_to_add.append(in_opp)
                        logger.info(
                            f"{exchange} 轮动: 轮出 {out_symbol}(资金费率:{out_funding_rate:.4%}) -> "
                            f"轮入 {in_opp['symbol']}(资金费率:{in_funding_rate:.4%}), "
                            f"EWMA得分: {out_opp['ewma_score']:.6f}->{in_opp['ewma_score']:.6f}"
                        )
                    else:
                        logger.info(
                            f"{exchange} 跳过轮动: {out_symbol}(资金费率:{out_funding_rate:.4%}) -> "
                            f"{in_opp['symbol']}(资金费率:{in_funding_rate:.4%}), "
                            f"EWMA得分: {out_opp['ewma_score']:.6f}->{in_opp['ewma_score']:.6f}, "
                            f"收益未明显改善"
                        )
                msg = (
                    f"{exchange} 持仓调整计划 - {exchange}:\n"
                    f"  当前持仓: {len(current_symbols)} 个 {current_symbols}\n"
                    f"  计划移除: {len(final_positions_to_remove)} 个 {final_positions_to_remove}\n"
                    f"  计划添加: {len(final_positions_to_add)} 个 "
                    f"{[pos['symbol'] for pos in final_positions_to_add]}"
                )
                logging.info(msg)
                # 发送钉钉消息
                self.ding_talk_manager.send_message(message=msg, prefix=exchange)
                return final_positions_to_remove, final_positions_to_add

            else:
                msg = (
                    f"{exchange} 持仓调整计划 - {exchange}:\n"
                    f"  当前持仓: {len(current_symbols)} 个 {current_symbols}\n"
                    f"  计划移除: {len(positions_to_remove)} 个 {positions_to_remove}\n"
                    f"  计划添加: {len(positions_to_add)} 个 "
                    f"{[pos['symbol'] for pos in positions_to_add]}"
                )
                logging.info(msg)
                # 发送钉钉消息
                self.ding_talk_manager.send_message(message=msg, prefix=exchange)
                return positions_to_remove, positions_to_add

        except Exception as e:
            logger.error(f"计算持仓调整失败: {str(e)}")
            raise
