"""
持仓监控器 - 负责监控和管理交易持仓情况
"""
import asyncio
import logging
import time
from typing import List, Dict, Set

from crypt_carry.constant.direction_type import DirectionType
from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.monitor.order_cooldown_manager import OrderCooldownManager
from crypt_carry.core.trading.monitor.pair_data_loader import PairDataLoader
from crypt_carry.core.trading.monitor.position_force_checker import <PERSON>si<PERSON><PERSON><PERSON><PERSON>Che<PERSON>
from crypt_carry.core.trading.monitor.position_risk_checker import <PERSON>si<PERSON><PERSON><PERSON><PERSON>he<PERSON>
from crypt_carry.core.trading.position.position_adjuster import PositionAdjuster
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.core.trading.trade.trade_executor import TradeExecutor
from crypt_carry.utils.calc import calculate_basis_rate
from crypt_carry.utils.ding_talk_manager import DingTalkManager
from util.log_throttler import LogThrottler

logger = logging.getLogger(__name__)


class PositionMonitor:
    """持仓监控器，负责监控和管理交易持仓情况"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化持仓监控器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client: ExchangeClient = exchange_client
        self.exchange_name = exchange_client.exchange_name

        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)

        # 初始化持仓管理器
        self.position_manager = PositionManager.get_instance()

        # 初始化市场数据管理器
        self.market_data_manager = MarketDataManager.get_instance()

        # 初始化交易执行器
        self.trade_executor = TradeExecutor(exchange_client)

        # 初始化持仓调整器
        self.position_adjuster = PositionAdjuster()

        # 初始化持仓强平检查器
        self.force_checker = PositionForceChecker(self.exchange_name)

        # 初始化钉钉通知管理器
        self.ding_talk_manager = DingTalkManager.get_instance()

        # 初始化新的组件
        self.pair_data_loader = PairDataLoader(exchange_client)
        self.risk_checker = PositionRiskChecker(exchange_client)
        self.cooldown_manager = OrderCooldownManager.get_instance(self.exchange_name)

        # 初始化日志频率控制器
        self.log_throttler = LogThrottler.get_instance(f"{self.exchange_name}_position_monitor")
        self._10m_log_key = f"{self.exchange_name}_position_monitor_mapping"
        self.log_throttler.set_interval(self._10m_log_key, 5 * 60)

        # 初始化监控任务
        self.monitor_task = None
        self.is_running = False
        self.monitor_interval = 3  # 5秒监控一次
        self.last_monitor_time = 0


    async def _monitor_positions(self):
        """持仓监控循环"""
        logger.info(f"{self.exchange_name} 持仓监控循环已启动")

        while self.is_running:
            try:
                # 确保WebSocket连接
                if not self.exchange_client.ws_manager.is_connected:
                    logger.warning(f"{self.exchange_name} WebSocket未连接，尝试重新连接...")
                    # try:
                    #     # 取消注释并启用重连代码
                    #     await self.exchange_client.ws_manager.connect()
                    #     logger.info(f"{self.exchange_name} WebSocket已重新连接")
                    # except Exception as e:
                    #     logger.error(f"{self.exchange_name} WebSocket重连失败: {str(e)}")
                    #     # 避免频繁重试，等待一段时间
                    #     await asyncio.sleep(5)
                    #     continue  # 跳过当前循环，避免在WebSocket未连接时执行其他操作

                # 执行持仓监控
                await self.do_monitor()

            except Exception as e:
                logger.error(f"{self.exchange_name} 持仓监控异常: {str(e)}")
                self.ding_talk_manager.send_message(
                    f"{self.exchange_name} 持仓监控异常: {str(e)}",
                    prefix=self.exchange_name,
                    is_at_all=True
                )

            # 间隔等待
            await asyncio.sleep(self.monitor_interval)

    async def do_monitor(self):
        """执行持仓监控"""
        logger.debug(f"{self.exchange_name} 开始执行持仓监控")

        # 检查是否在全局冷却期内
        if self.cooldown_manager.is_in_global_cooldown():
            return

        # 获取持仓信息
        try:
            # positions = await self.exchange_client.fetch_positions_by_ws()
            positions = await self.exchange_client.fetch_positions_by_rest()
            if not positions:
                logger.debug(f"{self.exchange_name} 没有持仓")
                self.last_monitor_time = time.time()
                return
        except Exception as e:
            logger.error(f"{self.exchange_name} 获取持仓信息失败: {str(e)}")
            return

        # 收集交易对并检查是否有新增交易对
        base_pairs: Set[str] = set()
        spot_pairs: Set[str] = set()
        perpetual_pairs: Set[str] = set()

        spot_positions: Dict[str, int] = {}
        futures_positions: Dict[str, int] = {}
        raw_positions: List[Dict] = []
        
        for position in positions:
            symbol = position.get('symbol')
            # LONG方向的持仓为正数，SHORT方向的持仓为负数
            quantity = 0
            if position.get('side') == 'LONG':
                quantity = position.get('amount', 0)
            elif position.get('side') == 'SHORT':
                quantity = -position.get('amount', 0)

            if position.get('is_spot'):
                spot_positions[symbol] = quantity
                spot_pairs.add(symbol)
                base_pairs.add(symbol)
            else:
                futures_positions[symbol] = quantity
                perpetual_pairs.add(symbol)
                # 保存原始合约持仓数据用于风险评估
                raw_positions.append(position)

        logger.debug(f"{self.exchange_name} 现货持仓: {spot_positions}")
        logger.debug(f"{self.exchange_name} 合约持仓: {futures_positions}")

        # 检查是否有新交易对并进行动态加载
        skip_check = await self._check_and_load_new_pairs(base_pairs, spot_pairs, perpetual_pairs)
        if skip_check:
            return

        # 检查合约持仓风险
        risk_positions = await self.risk_checker.check_position_risk(raw_positions)
        if risk_positions:
            logger.warning(f"{self.exchange_name} 检测到持仓风险: {risk_positions}")
            # 提取交易对符号
            risk_symbols = [pos.get('symbol') for pos in risk_positions if pos.get('symbol')]
            # 使用自动平仓功能
            await self.risk_checker.auto_close_positions(risk_symbols, reason="持仓风险")

        # 检查持仓平衡情况
        severe_mismatch_symbols = await self._check_position_balance(spot_positions, futures_positions, positions)

        # 处理持仓严重不匹配的交易对
        if severe_mismatch_symbols:
            logger.warning(f"{self.exchange_name} 检测到持仓严重不匹配: {severe_mismatch_symbols}")
            # 使用自动平仓功能
            await self.risk_checker.auto_close_positions(severe_mismatch_symbols, reason="持仓严重不匹配")

        logger.debug(f"{self.exchange_name} 持仓监控完成")
        self.last_monitor_time = time.time()

    async def _check_and_load_new_pairs(self, base_pairs: Set[str], spot_pairs: Set[str],
                                        perpetual_pairs: Set[str]) -> bool:
        """
        检查是否有新交易对需要添加到exchange_client的列表中，并触发动态加载操作
        
        Args:
            base_pairs: 基础交易对集合
            spot_pairs: 现货交易对集合
            perpetual_pairs: 合约交易对集合
            
        Returns:
            bool: 如果有新交易对被添加并需要跳过本次检查则返回True，否则返回False
        """
        # 检查是否有新交易对需要添加到exchange_client的列表中
        existing_base_pairs = set(self.exchange_client.base_pair) if self.exchange_client.base_pair else set()
        existing_spot_pairs = set(self.exchange_client.spot_pairs)
        existing_perpetual_pairs = set(self.exchange_client.perpetual_pairs)
        existing_all_pairs = set(self.exchange_client.all_pair)

        # 找出新增的交易对
        added_base_pairs = base_pairs - existing_base_pairs
        added_spot_pairs = spot_pairs - existing_spot_pairs
        added_perpetual_pairs = perpetual_pairs - existing_perpetual_pairs

        # 将新交易对添加到exchange_client的列表中
        if added_base_pairs:
            if self.exchange_client.base_pair is None:
                self.exchange_client.base_pair = list(added_base_pairs)
            else:
                self.exchange_client.base_pair.extend(added_base_pairs)

        if added_spot_pairs:
            self.exchange_client.spot_pairs.extend(added_spot_pairs)

        if added_perpetual_pairs:
            self.exchange_client.perpetual_pairs.extend(added_perpetual_pairs)

        # 更新all_pair列表
        added_all_pairs = (added_spot_pairs | added_perpetual_pairs) - existing_all_pairs
        if added_all_pairs:
            self.exchange_client.all_pair.extend(added_all_pairs)

        # 如果有新交易对加入，则触发动态加载操作
        if added_base_pairs or added_spot_pairs or added_perpetual_pairs:
            logger.info("**************** 新交易对检测 ***************")
            logger.info(f"{self.exchange_name} 检测到新的交易对，触发动态加载：")
            if added_base_pairs:
                logger.info(f"新增基础交易对: {added_base_pairs}")
            if added_spot_pairs:
                logger.info(f"新增现货交易对: {added_spot_pairs}")
            if added_perpetual_pairs:
                logger.info(f"新增合约交易对: {added_perpetual_pairs}")
            logger.info("**************** 新交易对检测 ***************")

            # 触发动态加载操作
            asyncio.create_task(self.pair_data_loader.reload_pairs_data(
                list(self.exchange_client.base_pair),
                list(self.exchange_client.spot_pairs),
                list(self.exchange_client.perpetual_pairs)
            ))
            logger.info(f"{self.exchange_name} 新交易对加载完成,等待下次检查持仓风险")
            # 因为发现新交易对，未避免后续检查持仓风险时出错，直接返回True表示需要跳过本次检查
            return True

        return False

    async def _check_position_balance(self, spot_positions: Dict[str, int], futures_positions: Dict[str, int],
                                      positions: List[Dict]) -> List[str]:
        """检查持仓平衡情况
        
        Args:
            spot_positions: 现货持仓信息，格式为 {symbol: quantity}
            futures_positions: 合约持仓信息，格式为 {symbol: quantity}
            positions: 原始持仓数据列表，包含价格信息
            
        Returns:
            严重不匹配的交易对列表
        """
        # 检查持仓差异，需要比较现货和期货的持仓量
        # 使用position_manager辅助管理持仓信息
        severe_mismatch_symbols = []
        position_mismatch_threshold = 0.3  # 持仓不匹配的阈值，超过该值会触发自动平仓
        
        # 对比每个交易对的持仓
        # 创建映射字典，将标准交易对映射到实际的现货和合约交易对
        symbol_mapping = {}  # 格式: {standard_symbol: {'spot': spot_symbol, 'futures': futures_symbol}}

        # 创建价格映射字典
        price_mapping = {}  # 格式: {symbol: entry_price}
        for position in positions:
            symbol = position.get('symbol')
            entry_price = position.get('entryPrice', 0)
            if symbol and entry_price:
                price_mapping[symbol] = entry_price
                
        # 处理现货持仓，构建映射关系
        for symbol in spot_positions.keys():
            standard_symbol = symbol.split(':')[0]  # 移除可能的合约后缀
            if standard_symbol not in symbol_mapping:
                symbol_mapping[standard_symbol] = {'spot': None, 'futures': None}
            symbol_mapping[standard_symbol]['spot'] = symbol

        # 处理合约持仓，构建映射关系
        for symbol in futures_positions.keys():
            standard_symbol = symbol.split(':')[0]  # 移除合约后缀
            if standard_symbol not in symbol_mapping:
                symbol_mapping[standard_symbol] = {'spot': None, 'futures': None}
            symbol_mapping[standard_symbol]['futures'] = symbol

        # 使用日志节流器控制日志输出频率
        self.log_throttler.log_if_allowed(
            logger_obj=logger, key=self._10m_log_key,
            message=f"{self.exchange_name} 交易对映射关系: {symbol_mapping}"
        )
        if not symbol_mapping:
            logger.warning(f"{self.exchange_name} 未找到任何交易对映射关系")
            return []

        # 清空持仓
        if not self.cooldown_manager.is_in_global_cooldown():
            self.position_manager.clear_position(self.exchange_name)

        # 遍历所有标准交易对
        for standard_symbol, symbols in symbol_mapping.items():
            try:
                logger.debug(
                    f"standard_symbol:{standard_symbol}, is_in_cooldown:{self.cooldown_manager.is_in_cooldown(standard_symbol)}")
                if (self.cooldown_manager.is_in_cooldown(standard_symbol)):
                    logger.warning(f"{self.exchange_name} {standard_symbol} 仍在冷却期内，跳过持仓平衡检查")
                    continue

                # 获取该标准交易对对应的现货和合约实际交易对
                spot_symbol = symbols['spot']
                futures_symbol = symbols['futures']

                # 获取持仓数量
                spot_position = spot_positions.get(spot_symbol, 0) if spot_symbol else 0
                futures_position = futures_positions.get(futures_symbol, 0) if futures_symbol else 0

                # 获取价格信息
                spot_price = price_mapping.get(spot_symbol, 0) if spot_symbol else 0
                futures_price = price_mapping.get(futures_symbol, 0) if futures_symbol else 0

                # 记录匹配到的交易对和持仓
                logger.debug(f"{self.exchange_name} {standard_symbol} 持仓匹配 - "
                             f"现货({spot_symbol or '未匹配'}): {spot_position}, "
                             f"合约({futures_symbol or '未匹配'}): {futures_position}")

                
                direction = DirectionType.LONG_SPOT_SHORT_FUTURES if spot_position > 0 or futures_position < 0 else DirectionType.SHORT_SPOT_LONG_FUTURES

                # 计算基差率
                basis_rate = 0.0
                if spot_price > 0 and futures_price > 0:
                    basis_rate = calculate_basis_rate(spot_price=spot_price, futures_price=futures_price)
                
                # 更新持仓
                self.position_manager.update_position(
                    exchange=self.exchange_name,
                    symbol=standard_symbol,
                    spot_price=spot_price,
                    spot_size=spot_position,
                    futures_price=futures_price,
                    futures_size=futures_position,
                    direction=direction,
                    basis_rate=basis_rate
                )

                # 检查持仓差异
                if spot_position != 0 or futures_position != 0:
                    # 计算差异
                    position_diff = abs(spot_position + futures_position)  # 理论上应该是0（一个正一个负）

                    # 当一方持仓为0但另一方不为0时，直接认为持仓不匹配
                    is_mismatch = False
                    ratio_diff = 0
                    is_severe_mismatch = False

                    if (spot_position == 0 and futures_position != 0) or (spot_position != 0 and futures_position == 0):
                        # 一方为0，另一方不为0，直接标记为不匹配
                        is_mismatch = True
                        ratio_diff = 1.0  # 设置一个足够大的差异值确保触发告警
                        is_severe_mismatch = True  # 单边持仓是严重不匹配
                    else:
                        # 取绝对值较小的作为基准
                        min_position = min(abs(spot_position), abs(futures_position))

                        # 计算相对差异
                        if min_position > 0:
                            ratio_diff = position_diff / min_position
                            is_mismatch = abs(ratio_diff) > 0.01
                            # 当差异超过30%时，标记为严重不匹配
                            is_severe_mismatch = abs(ratio_diff) > position_mismatch_threshold

                    # 当差异大于1%或一方为0时发出告警
                    if is_mismatch:
                        msg = (
                            f"{self.exchange_name} {standard_symbol} 持仓不匹配:\n"
                            f"现货持仓({spot_symbol or '未匹配'}): {spot_position}\n"
                            f"合约持仓({futures_symbol or '未匹配'}): {futures_position}\n"
                            f"差异数量: {position_diff:.6f}\n"
                            f"相对差异: {ratio_diff:.2%}\n"
                            f"冷却信息: {self.cooldown_manager.symbol_cooldowns}"
                        )
                        logger.warning(msg)

                        # 添加是否需要自动平仓的提示
                        if is_severe_mismatch:
                            msg += f"\n⚠️ 差异超过{position_mismatch_threshold * 100}%阈值，系统将触发自动平仓"
                            severe_mismatch_symbols.append(standard_symbol)

                            # 发送钉钉告警
                            self.ding_talk_manager.send_message(msg, prefix=self.exchange_name, is_at_all=True)
                    else:
                        logger.debug(f"{self.exchange_name} {standard_symbol} 持仓正常匹配 - "
                                    f"现货({spot_symbol or '未匹配'}): {spot_position}, "
                                    f"合约({futures_symbol or '未匹配'}): {futures_position}")
            except Exception as e:
                logger.error(f"{self.exchange_name} {standard_symbol} 检查持仓差异失败: {str(e)}")

        return severe_mismatch_symbols

    def set_order_cooldown(self, symbol: str) -> None:
        """设置交易对的订单执行冷却期
        
        Args:
            symbol: 交易对
        """
        logger.info(f"{self.exchange_name} {symbol} set_order_cooldown: 设置订单执行冷却期")
        self.cooldown_manager.set_order_cooldown(symbol)

    def set_cooldown(self) -> None:
        """设置全局订单执行冷却期"""
        self.cooldown_manager.set_global_cooldown()
    

    async def start_monitor(self):
        """启动持仓监控"""
        if self.is_running:
            logger.warning(f"{self.exchange_name} 持仓监控已在运行中")
            return

        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_positions())
        logger.info(f"{self.exchange_name} 启动持仓监控")
    
    async def stop_monitor(self):
        """停止持仓监控"""
        if not self.is_running:
            logger.warning(f"{self.exchange_name} 持仓监控未在运行")
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info(f"{self.exchange_name} 停止持仓监控")
