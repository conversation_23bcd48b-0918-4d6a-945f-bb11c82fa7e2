"""
持仓风险检查器 - 负责检查持仓风险和自动平仓
"""
import logging
from typing import List, Dict

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.core.trading.trade.trade_executor import TradeExecutor
from crypt_carry.utils.ding_talk_manager import DingTalkManager

logger = logging.getLogger(__name__)


class PositionRiskChecker:
    """持仓风险检查器，负责检查持仓风险和自动平仓"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化持仓风险检查器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name
        self.position_manager = PositionManager.get_instance()
        self.trade_executor = TradeExecutor(exchange_client)
        self.ding_talk_manager = DingTalkManager.get_instance()

    async def check_position_risk(self, positions: List[Dict]):
        """检查合约持仓风险
        
        Args:
            positions: 持仓列表
        """
        try:
            # 风险检查逻辑
            risk_positions = []

            # 检查杠杆、爆仓风险等
            for position in positions:
                symbol = position.get('symbol', '')
                if not symbol:
                    continue

                leverage = position.get('leverage', 0)
                margin_ratio = position.get('marginRatio', 0)
                liquidation_price = position.get('liquidationPrice', 0)

                # 风险判断示例
                is_high_risk = (leverage > 20 or margin_ratio > 0.8)

                if is_high_risk:
                    risk_positions.append(position)
                    logger.warning(f"{self.exchange_name} {symbol} 持仓风险较高 - "
                                   f"杠杆: {leverage}x, 保证金率: {margin_ratio:.2%}, "
                                   f"强平价: {liquidation_price}")

            # 如有高风险持仓，发送通知
            if risk_positions:
                msg = f"{self.exchange_name} 检测到 {len(risk_positions)} 个高风险持仓，请注意风险控制"
                logger.warning(msg)
                self.ding_talk_manager.send_message(msg, prefix=self.exchange_name, is_at_all=True)

            return risk_positions
        except Exception as e:
            logger.error(f"{self.exchange_name} 检查持仓风险失败: {str(e)}")
            return []

    async def auto_close_positions(self, symbols: List[str], reason: str = "自动平仓"):
        """自动平仓指定的交易对
        
        Args:
            symbols: 需要平仓的交易对列表
            reason: 平仓原因
        """
        if not symbols:
            return

        logger.warning(f"{self.exchange_name} {reason}，触发自动平仓: {symbols}")

        # 发送平仓前警告消息
        warning_message = f"⚠️ {self.exchange_name} {reason}，系统将自动平仓以下交易对:\n{', '.join(symbols)}"
        self.ding_talk_manager.send_message(warning_message, prefix=f"{self.exchange_name}-紧急平仓", is_at_all=True)

        # 延迟初始化交易执行器，避免循环依赖
        if self.trade_executor is None:
            try:
                from crypt_carry.core.trading.trade.trade_executor import TradeExecutor
                self.trade_executor: TradeExecutor = TradeExecutor.get_instance(self.exchange_client)
                logger.info(f"{self.exchange_name} 延迟初始化交易执行器成功")
            except Exception as e:
                error_msg = f"{self.exchange_name} 延迟初始化交易执行器失败: {str(e)}"
                logger.error(error_msg)
                self.ding_talk_manager.send_message(error_msg, prefix=f"{self.exchange_name}-系统错误", is_at_all=True)
                return

        # 逐个交易对平仓
        for symbol in symbols:
            try:
                standard_symbol = self.exchange_client.get_standard_symbol(symbol)
                logger.warning(f"{self.exchange_name} 开始对 {standard_symbol} 执行自动平仓")
                await self.trade_executor.close_position(standard_symbol)
                logger.info(f"{self.exchange_name} {symbol} 自动平仓完成")
            except Exception as e:
                error_message = f"{self.exchange_name} {symbol} 自动平仓失败: {str(e)}"
                logger.error(error_message)
                self.ding_talk_manager.send_message(error_message, prefix=f"{self.exchange_name}-平仓失败",
                                                    is_at_all=True)

        # 发送平仓完成消息
        complete_message = f"✅ {self.exchange_name} {reason}自动平仓操作已完成"
        self.ding_talk_manager.send_message(complete_message, prefix=f"{self.exchange_name}-紧急平仓", is_at_all=True)
