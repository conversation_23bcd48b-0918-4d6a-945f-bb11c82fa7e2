"""
交易对数据加载器 - 负责动态加载交易对的资金费率和行情数据
"""
import logging
from typing import List

from crypt_carry.core.exchange.base.exchange_client import ExchangeClient
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager

logger = logging.getLogger(__name__)


class PairDataLoader:
    """交易对数据加载器，负责动态加载交易对的资金费率和行情数据"""

    def __init__(self, exchange_client: ExchangeClient):
        """初始化交易对数据加载器
        
        Args:
            exchange_client: 交易所客户端
        """
        self.exchange_client = exchange_client
        self.exchange_name = exchange_client.exchange_name
        self.market_data_manager = MarketDataManager.get_instance()
        self.funding_manager = None
        self.funding_scheduler = None

    async def reload_pairs_data(self, base_pairs: List[str], spot_pairs: List[str], perpetual_pairs: List[str]):
        """发现新交易对时，重新加载资金费率和行情数据
        
        Args:
            base_pairs: 基础交易对列表
            spot_pairs: 现货交易对列表
            perpetual_pairs: 合约交易对列表
        """
        try:
            logger.info(f"{self.exchange_name} 开始重新加载新交易对数据...")

            # 1. 订阅新交易对的行情数据
            if spot_pairs or perpetual_pairs:
                logger.info(f"{self.exchange_name} 订阅新交易对行情数据: 现货={spot_pairs}, 合约={perpetual_pairs}")
                try:
                    # 直接调用交易所客户端的订阅方法
                    if hasattr(self.exchange_client.ws_manager, '_reconnect'):
                        logger.info(f"{self.exchange_name} 使用直接的WS管理器订阅方法")
                        await self.exchange_client.ws_manager._reconnect(spot_pairs, perpetual_pairs, new_task=False)
                    else:
                        # 如果没有直接的WS管理器订阅方法，则通过客户端订阅
                        # 由于客户端通常会使用实例变量中的交易对列表，而我们已经更新了这些列表
                        # 所以直接调用客户端的订阅方法即可
                        logger.info(f"{self.exchange_name} 使用客户端订阅方法")
                        await self.exchange_client.subscribe_market_data()

                    logger.info(f"{self.exchange_name} 订阅新交易对行情数据成功")
                except Exception as e:
                    logger.error(f"{self.exchange_name} 订阅新交易对行情数据失败: {str(e)}")

            # 2. 加载资金费率数据
            if perpetual_pairs:
                logger.info(f"{self.exchange_name} 加载新交易对资金费率: {perpetual_pairs}")

                try:
                    # 根据交易所类型选择合适的资金费率管理器
                    # if self.exchange_name.upper() == 'OKX':
                    #     # 初始化资金费率管理器
                    #     logger.info(f"{self.exchange_name} 初始化资金费率管理器, 交易对: {perpetual_pairs}")
                    #     self.funding_manager = OkxFundingManager(self.exchange_client.config, perpetual_pairs)
                    #     await self.funding_manager.initialize()
                    # elif self.exchange_name.upper() == 'BINANCE':
                    #     # 初始化资金费率管理器
                    #     logger.info(f"{self.exchange_name} 初始化资金费率管理器, 交易对: {perpetual_pairs}")
                    #     self.funding_manager = BinanceFundingManager(self.exchange_client.config, perpetual_pairs)
                    #     await self.funding_manager.initialize()
                    # else:
                    #     logger.warning(f"不支持的交易所: {self.exchange_name}, 无法加载资金费率")

                    await self.exchange_client.funding_manager.initialize()
                    await self.exchange_client.funding_rate_scheduler.stop()
                    await self.exchange_client.funding_rate_scheduler.start()
                    # 如果资金费率管理器初始化成功，启动资金费率定时任务
                    # if self.funding_manager:
                    #     # 如果已经存在资金费率调度器，先停止
                    #     if self.funding_scheduler:
                    #         await self.funding_scheduler.stop()

                    #     # 启动资金费率定时任务
                    #     self.funding_scheduler = FundingRateScheduler({
                    #         self.exchange_name: self.funding_manager
                    #     }, perpetual_pairs)
                    #     await self.funding_scheduler.start()
                    logger.info(f"{self.exchange_name} 启动资金费率定时任务成功")
                except Exception as e:
                    logger.error(f"{self.exchange_name} 初始化资金费率管理器失败: {str(e)}")

            # 3. 更新市场数据管理器中的数据结构
            # 确保MarketDataManager已经初始化了相关交易对的数据结构
            # for pair in base_pairs:
            #     if pair not in self.market_data_manager.market_data.get(self.exchange_name, {}):
            #         self.market_data_manager.init_symbol_data(self.exchange_name, pair)
            #         logger.debug(f"{self.exchange_name} 初始化 {pair} 市场数据结构")

            logger.info(f"{self.exchange_name} 动态加载新交易对数据完成")
        except Exception as e:
            logger.error(f"{self.exchange_name} 动态加载新交易对数据失败: {str(e)}")
