"""
持仓强制检查器 - 负责处理强制触发持仓监控的逻辑
"""
import asyncio
import logging
from typing import Optional, Callable, Awaitable

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig

logger = logging.getLogger(__name__)


class PositionForceChecker:
    """持仓强制检查器，负责处理强制触发持仓监控的逻辑"""

    def __init__(self, exchange_name: str, monitor_callback: Optional[Callable[[], Awaitable[None]]] = None):
        """初始化持仓强制检查器
        
        Args:
            exchange_name: 交易所名称
            monitor_callback: 触发监控的回调函数
        """
        self.exchange_name = exchange_name
        self.monitor_callback = monitor_callback

        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)

        # 任务控制
        self.force_check_task = None
        self.is_running = False

    async def start_checker(self):
        """启动强制检查任务"""
        if self.force_check_task is not None:
            logger.warning(f"{self.exchange_name} 强制持仓检查任务已在运行中")
            return

        self.is_running = True
        logger.info(f"{self.exchange_name} 启动强制持仓检查任务")
        self.force_check_task = asyncio.create_task(self._check_force_position())

    async def stop_checker(self):
        """停止强制检查任务"""
        if self.force_check_task is None:
            logger.warning(f"{self.exchange_name} 强制持仓检查任务未运行")
            return

        self.is_running = False
        logger.info(f"{self.exchange_name} 停止强制持仓检查任务")

        if self.force_check_task:
            self.force_check_task.cancel()
            self.force_check_task = None

    async def _check_force_position(self):
        """检查FORCE_POSITION_CHECK标志，如果为True则执行持仓监控"""
        try:
            logger.info(f"{self.exchange_name} 启动强制持仓检查监控任务")

            while self.is_running:
                try:
                    # 强制从磁盘重新加载配置
                    self.config.reload_config()

                    # 检查是否需要强制执行持仓监控
                    force_check = self.config.get_strategy_value('FORCE_POSITION_CHECK', False)

                    if force_check:
                        logger.info(f"{self.exchange_name} 检测到强制触发持仓监控指令，立即开始检查")

                        # 执行持仓监控回调
                        if self.monitor_callback:
                            await self.monitor_callback()
                        else:
                            logger.warning(f"{self.exchange_name} 未设置监控回调函数，无法执行强制持仓监控")

                        # 重置标志位
                        await self._reset_force_check_flag()

                    # 每2秒检查一次配置
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"{self.exchange_name} 强制持仓检查任务出错: {str(e)}")
                    await asyncio.sleep(10)  # 出错后10秒重试

        except asyncio.CancelledError:
            logger.info(f"{self.exchange_name} 强制持仓检查任务已取消")
        except Exception as e:
            logger.error(f"{self.exchange_name} 强制持仓检查任务异常: {str(e)}")

    async def _reset_force_check_flag(self) -> None:
        """重置强制持仓检查标志"""
        try:
            logger.info(f"{self.exchange_name} 开始重置FORCE_POSITION_CHECK配置")

            # 使用 ExchangeConfig 设置策略值
            self.config.set_strategy_value('FORCE_POSITION_CHECK', False)

            # 验证配置是否已更新
            force_check = self.config.get_strategy_value('FORCE_POSITION_CHECK', True)
            if force_check:
                logger.warning(f"{self.exchange_name} FORCE_POSITION_CHECK配置更新失败，仍为{force_check}")
            else:
                logger.info(f"{self.exchange_name} 已成功重置FORCE_POSITION_CHECK配置为false")
        except Exception as e:
            logger.error(f"{self.exchange_name} 重置FORCE_POSITION_CHECK配置时出错: {str(e)}")
