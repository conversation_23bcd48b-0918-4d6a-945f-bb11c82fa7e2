"""
订单冷却管理器 - 负责管理订单执行冷却时间
"""
import logging
import time
from typing import Dict

from crypt_carry.core.exchange.base.exchange_config import ExchangeConfig

logger = logging.getLogger(__name__)


class OrderCooldownManager:
    """订单冷却管理器，负责管理订单执行冷却时间"""

    _instances = {}  # 类变量，用于存储不同交易所的实例

    @classmethod
    def get_instance(cls, exchange_name: str):
        """获取订单冷却管理器实例（单例模式）
        
        Args:
            exchange_name: 交易所名称
            
        Returns:
            OrderCooldownManager: 订单冷却管理器实例
        """
        if exchange_name not in cls._instances:
            cls._instances[exchange_name] = cls(exchange_name)
        return cls._instances[exchange_name]

    def __init__(self, exchange_name: str):
        """初始化订单冷却管理器
        
        Args:
            exchange_name: 交易所名称
        """
        self.exchange_name = exchange_name
        # 加载配置
        self.config = ExchangeConfig(self.exchange_name)
        self.global_cooldown_seconds = self.config.get_order_value('GLOBAL_COOLDOWN_SEC', 90)
        self.cooldown_seconds = self.config.get_order_value('ORDER_EXECUTION_COOLDOWN_SEC', 60)
        self.symbol_cooldowns: Dict[str, float] = {}  # 记录每个交易对的冷却时间
        self.global_cooldown_end_time: float = 0  # 全局冷却结束时间



    def set_global_cooldown(self) -> None:
        """设置全局订单执行冷却期"""
        # 设置冷却结束时间
        self.global_cooldown_end_time = time.time() + self.global_cooldown_seconds
        logger.warning(f"{self.exchange_name} order_cooldown_manager: 设置全局交易冷却期 {self.global_cooldown_seconds}秒")

    def is_in_global_cooldown(self) -> bool:
        """检查是否处于全局订单执行冷却期
        
        Returns:
            是否处于全局冷却期
        """
        if self.global_cooldown_end_time <= 0:
            return False

        # 判断当前时间是否在冷却期内
        if time.time() < self.global_cooldown_end_time:
            remaining = self.global_cooldown_end_time - time.time()
            logger.warning(f"{self.exchange_name} 处于全局交易冷却期，剩余 {remaining:.1f}秒")
            return True

        return False

    def set_order_cooldown(self, symbol: str) -> None:
        """设置交易对的订单执行冷却期
        
        Args:
            symbol: 交易对
        """
        # 设置冷却结束时间
        self.symbol_cooldowns[symbol] = time.time() + self.cooldown_seconds
        logger.info(f"{self.exchange_name} {symbol} order_cooldown_manager: 设置订单冷却期 {self.cooldown_seconds}秒")
        
    def is_in_cooldown(self, symbol: str) -> bool:
        """检查交易对是否处于订单执行冷却期
        
        Args:
            symbol: 交易对
            
        Returns:
            是否处于冷却期
        """
        # 优先检查全局冷却
        if self.is_in_global_cooldown():
            return True
        logger.debug(f"{self.exchange_name} {symbol} order_cooldown_manager: 检查订单冷却期")
        # 检查交易对冷却
        if symbol not in self.symbol_cooldowns:
            logger.debug(f"{self.exchange_name} {symbol} 未设置订单冷却期")
            return False

        cooldown_end_time = self.symbol_cooldowns[symbol]
        # 判断当前时间是否在冷却期内
        if time.time() < cooldown_end_time:
            remaining = cooldown_end_time - time.time()
            logger.info(f"{self.exchange_name} {symbol} 处于订单冷却期，剩余 {remaining:.1f}秒")
            return True

        # 冷却期已过，移除记录
        logger.info(f"{self.exchange_name} {symbol} 订单冷却期已过，移除记录")
        self.symbol_cooldowns.pop(symbol, None)
        return False
