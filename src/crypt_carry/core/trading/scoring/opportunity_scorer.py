"""
套利机会评分系统
"""
import json
import logging
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

from crypt_carry.config.config_loader import ConfigLoader
from crypt_carry.utils.data_directory import DataDirectory
from util.formatter import format_funding_rate, format_basis_rate

logger = logging.getLogger(__name__)


class OpportunityScorer:
    def __init__(self):
        self.base_config = ConfigLoader.get_base_config()
        self.trading_config = ConfigLoader.get_trading_config()
        # 交易配置
        self.strategy_config = self.trading_config.get("STRATEGY", {})
        self.ewma_alpha = float(self.strategy_config.get("EWMA_ALPHA", 0.7))
        self.max_score_history = self.strategy_config.get("MAX_SCORE_HISTORY", 100)
        # 评分配置
        self.scoring_config = self.trading_config.get('SCORING', {})
        self.funding_weight = float(self.scoring_config.get('FUNDING_WEIGHT', 0.7))
        self.basis_weight = float(self.scoring_config.get('BASIS_WEIGHT', 0.3))
        # 历史得分数据
        self.score_history = {}

        # 初始化数据目录
        data_dir = DataDirectory(self.base_config)
        scores_dir = data_dir.get_path("HISTORICAL_SCORES")
        self.scores_file = scores_dir / "ewma_scores.json"
        os.makedirs(scores_dir, exist_ok=True)

        # 加载历史得分
        self._load_historical_scores()

    def _is_data_valid(self, last_update: str) -> bool:
        """检查数据是否仍然有效（距离现在不超过8小时）"""
        try:
            last_update_time = datetime.fromisoformat(last_update)
            if last_update_time.tzinfo is None:
                last_update_time = last_update_time.replace(tzinfo=timezone.utc)
            current_time = datetime.now(timezone.utc)
            time_diff = current_time - last_update_time
            return time_diff.total_seconds() <= 8 * 3600
        except Exception as e:
            logger.error(f"检查数据有效性失败: {e}")
            return False

    def _load_historical_scores(self):
        """从文件加载历史EWMA得分"""
        try:
            if os.path.exists(self.scores_file):
                with open(self.scores_file, 'r') as f:
                    self.score_history = json.load(f)
                logger.info(f"成功加载历史得分数据，共 {len(self.score_history)} 条记录")
            else:
                logger.info("历史得分文件不存在，将创建新文件")
        except Exception as e:
            logger.error(f"加载历史得分失败: {e}")
            self.score_history = {}

    def _save_historical_scores(self):
        """保存EWMA得分到文件"""
        try:
            with open(self.scores_file, 'w') as f:
                json.dump(self.score_history, f)
            logger.debug("成功保存历史得分数据")
        except Exception as e:
            logger.error(f"保存历史得分失败: {e}")

    def calculate_ewma_score(self, symbol: str, current_score: float) -> float:
        """计算EWMA得分
        
        Args:
            symbol: 交易对符号
            current_score: 当前得分
            
        Returns:
            float: EWMA得分
        """
        try:
            # 获取历史得分
            history = self.score_history.get(symbol, {})
            last_score = float(history.get('score', 0))
            last_update = history.get('last_update', None)

            # 如果没有历史得分或历史得分已过期，直接使用当前得分
            if not last_update or not self._is_data_valid(last_update):
                ewma_score = float(current_score)
            else:
                # 计算EWMA得分
                ewma_score = self.ewma_alpha * float(current_score) + (1 - self.ewma_alpha) * last_score

            # 更新历史得分
            self.score_history[symbol] = {
                'score': ewma_score,
                'last_update': datetime.now(timezone.utc).isoformat()
            }

            # 保存历史得分
            self._save_historical_scores()
            return ewma_score
        except Exception as e:
            logger.error(f"计算EWMA得分失败: {e}")
            return 0.0

    def calculate_score(self, basis_rate: float, funding_rate: float) -> float:
        """计算综合得分
        
        Args:
            basis_rate: 基差率
            funding_rate: 资金费率
            
        Returns:
            float: 综合得分
        """
        try:
            # 计算基差得分和资金费率得分
            basis_score = abs(float(basis_rate))
            funding_score = abs(float(funding_rate))

            # 计算加权得分
            weight_score = basis_score * self.basis_weight + funding_score * self.funding_weight
            return weight_score
        except Exception as e:
            logger.error(f"计算套利得分失败: {str(e)}")
            return 0.0
