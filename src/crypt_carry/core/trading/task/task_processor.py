"""
任务处理器 - 负责处理待处理任务列表中的任务
"""
import asyncio
import logging
from typing import Dict, Callable, Optional

from crypt_carry.constant.direction_type import DirectionType
from crypt_carry.core.exchange.market.market_data_manager import MarketDataManager
from crypt_carry.core.trading.position.position_manager import PositionManager
from crypt_carry.core.trading.task.pending_tasks_manager import PendingTasksManager
from crypt_carry.core.trading.trade.trade_executor import TradeExecutor
from crypt_carry.utils.ding_talk_manager import DingTalkManager
from util.formatter import format_basis_rate, format_funding_rate
from util.log_throttler import LogThrottler

logger = logging.getLogger(__name__)


class TaskProcessor:
    """任务处理器，负责处理待处理任务列表中的任务"""

    def __init__(self,
                 exchange_name: str,
                 trade_executor: TradeExecutor,
                 pending_tasks_manager: PendingTasksManager,
                 position_manager: PositionManager,
                 market_data_manager: MarketDataManager,
                 funding_rate_cache,
                 ding_talk_manager: DingTalkManager,
                 strategy_config: Dict,
                 reset_force_rebalance_callback: Optional[Callable] = None):
        """初始化任务处理器
        
        Args:
            exchange_name: 交易所名称
            trade_executor: 交易执行器
            pending_tasks_manager: 待处理任务管理器
            position_manager: 持仓管理器
            market_data_manager: 市场数据管理器
            funding_rate_cache: 资金费率缓存
            ding_talk_manager: 钉钉通知管理器
            strategy_config: 策略配置
            reset_force_rebalance_callback: 重置强制换仓标志的回调函数
        """
        self.exchange_name = exchange_name
        self.trade_executor = trade_executor
        self.pending_tasks_manager = pending_tasks_manager
        self.position_manager = position_manager
        self.market_data_manager = market_data_manager
        self.funding_rate_cache = funding_rate_cache
        self.ding_talk_manager = ding_talk_manager
        self.strategy_config = strategy_config
        self.reset_force_rebalance_callback = reset_force_rebalance_callback

        # 日志节流器
        self.log_throttler = LogThrottler.get_instance(f"{self.exchange_name}_task_processor")
        self._5m_log_key = f"{self.exchange_name}_task_counts"
        self.log_throttler.set_interval(self._5m_log_key, 1 * 60)

        # 任务处理器
        self.processor = None
        self.is_running = False

    async def start(self):
        """启动任务处理器"""
        if self.is_running:
            logger.warning(f"{self.exchange_name} 任务处理器已在运行中")
            return

        self.is_running = True
        self.processor = asyncio.create_task(self._process_pending_tasks())
        logger.info(f"{self.exchange_name} 任务处理器已启动")

    async def stop(self):
        """停止任务处理器"""
        if not self.is_running:
            return

        self.is_running = False
        if self.processor:
            self.processor.cancel()
            try:
                await self.processor
            except asyncio.CancelledError:
                pass
            self.processor = None
        logger.info(f"{self.exchange_name} 任务处理器已停止")

    async def _process_pending_tasks(self):
        """处理待处理任务的事件循环"""
        try:
            while True:
                try:
                    # 获取基差率阈值
                    basis_threshold = self.strategy_config.get('BASIS_THRESHOLD', 0.001)  # 默认0.1%

                    # 获取当前交易所的待处理任务
                    task_counts = await self.pending_tasks_manager.get_task_counts(self.exchange_name)
                    close_tasks = await self.pending_tasks_manager.get_close_tasks(self.exchange_name)
                    open_tasks = await self.pending_tasks_manager.get_open_tasks(self.exchange_name)
                    
                    msg = (
                        f"{self.exchange_name} TaskProcessor 待处理任务更新 - "
                        f"待平仓: {task_counts['close_count']}, close_tasks:{close_tasks}, "
                        f"待建仓: {task_counts['open_count']}, open_tasks:{open_tasks}"
                    )
                    self.log_throttler.log_if_allowed(logger_obj=logger, key=self._5m_log_key, message=msg)

                    # 1. 处理待平仓任务
                    remaining_close_tasks = []
                    for symbol in await self.pending_tasks_manager.get_close_tasks(self.exchange_name):
                        try:
                            if not symbol:
                                logger.error(f"{self.exchange_name} 待平仓任务缺少symbol字段: {symbol}")
                                remaining_close_tasks.append(symbol)
                                continue

                            # 获取当前持仓信息
                            current_position = self.position_manager.get_position(self.exchange_name, symbol)
                            if not current_position:
                                logger.warning(f"{self.exchange_name} {symbol} 未找到持仓信息，跳过平仓任务")
                                remaining_close_tasks.append(symbol)
                                continue

                            direction = current_position.direction
                            if not direction:
                                logger.error(f"{self.exchange_name} {symbol} 持仓缺少direction字段")
                                remaining_close_tasks.append(symbol)
                                continue

                            # 获取当前基差率
                            basis_rate = self.market_data_manager.get_basis_rate(self.exchange_name, symbol)
                            if basis_rate is None:
                                logger.warning(f"{self.exchange_name} {symbol} 未找到当前基差率，跳过平仓任务")
                                remaining_close_tasks.append(symbol)
                                continue

                            # 使用开仓时的基差率
                            entry_basis_rate = current_position.entry_basis_rate
                            basis_change = abs(basis_rate - entry_basis_rate)

                            logger.debug(
                                f"{self.exchange_name} {symbol} 基差率变动 - "
                                f"方向: {direction}, 基差率: {format_basis_rate(basis_rate)}, "
                                f"开仓基差率: {format_basis_rate(entry_basis_rate)}, "
                                f"基差变动: {basis_change:.4%}"
                            )

                            # 检查是否满足平仓条件
                            if (
                                    (
                                            direction == DirectionType.LONG_SPOT_SHORT_FUTURES and basis_change > basis_threshold)
                                    or
                                    (
                                            direction == DirectionType.SHORT_SPOT_LONG_FUTURES and basis_change > basis_threshold)
                            ):
                                # 获取当前资金费率（可选）
                                funding_rate = 0
                                settlement_interval = 0
                                normal_funding_rate = 0
                                try:
                                    # 尝试从资金费率检测器获取资金费率
                                    funding_rate = self.funding_rate_cache.get_current_rate(self.exchange_name, symbol)
                                    # 获取当前结算周期
                                    settlement_interval = self.funding_rate_cache.get_settlement_interval(
                                        self.exchange_name, symbol)
                                    # 获取正常资金费率
                                    normal_funding_rate = self.funding_rate_cache.get_normal_funding_rate(
                                        self.exchange_name, symbol)
                                except:
                                    pass

                                await self.pending_tasks_manager.remove_close_task(self.exchange_name, symbol)
                                # 执行平仓
                                await self.trade_executor.close_position(symbol=symbol)
                                message = (
                                    f"{self.exchange_name} {symbol} 执行平仓 - "
                                    f"方向: {direction}\n"
                                    f"资金费率: {format_funding_rate(funding_rate)}\n"
                                    f"结算周期: {settlement_interval}\n"
                                    f"折算成8小时资金费率: {format_funding_rate(normal_funding_rate)}\n"
                                    f"基差率: {format_basis_rate(basis_rate)}\n"
                                    f"基差变动: {basis_change:.4%}"
                                )
                                logger.debug(message)
                                self.ding_talk_manager.send_message(
                                    message=message,
                                    prefix=self.exchange_name
                                )
                            else:
                                remaining_close_tasks.append(symbol)
                        except Exception as e:
                            logger.error(f"{self.exchange_name} {symbol} 处理平仓任务失败: {str(e)}")
                            remaining_close_tasks.append(symbol)

                    # 2. 处理待建仓任务
                    remaining_open_tasks = []
                    for opportunity in await self.pending_tasks_manager.get_open_tasks(self.exchange_name):
                        try:
                            symbol = opportunity.get('symbol')
                            direction = opportunity.get('trade_info', {}).get('direction')

                            if not symbol or not direction:
                                logger.error(
                                    f"{self.exchange_name} 待建仓任务缺少必要字段: "
                                    f"symbol={symbol}, direction={direction}"
                                )
                                remaining_open_tasks.append(opportunity)
                                continue

                            # 获取当前基差率
                            basis_rate = self.market_data_manager.get_basis_rate(self.exchange_name, symbol)
                            if basis_rate is None:
                                logger.warning(f"{self.exchange_name} {symbol} 未找到当前基差率，跳过建仓任务")
                                remaining_open_tasks.append(opportunity)
                                continue

                            # 获取当前资金费率（可选）
                            funding_rate = 0
                            settlement_interval = 0
                            normal_funding_rate = 0
                            try:
                                # 尝试从资金费率检测器获取资金费率
                                funding_rate = self.funding_rate_cache.get_current_rate(self.exchange_name, symbol)
                                # 获取当前结算周期
                                settlement_interval = self.funding_rate_cache.get_settlement_interval(
                                    self.exchange_name, symbol)
                                # 获取当前标准化资金费率
                                normal_funding_rate = self.funding_rate_cache.get_normal_funding_rate(
                                    self.exchange_name, symbol)
                            except:
                                pass

                            # 检查是否满足建仓条件
                            # 特殊处理：
                            # 1. 当方向为LONG_SPOT_SHORT_FUTURES时，如果basis_rate > 0且资金费率normal_funding_rate(折算成8小时) > 0.0002（0.02%），也允许开仓
                            # 2. 当方向为SHORT_SPOT_LONG_FUTURES时，如果basis_rate < 0且资金费率normal_funding_rate(折算成8小时) < -0.0002（-0.02%），也允许开仓

                            # 现货做多、合约做空的高资金费率条件
                            high_funding_long_spot_condition = (direction == DirectionType.LONG_SPOT_SHORT_FUTURES
                                                                and basis_rate >= 0
                                                                and normal_funding_rate >= 0.0002)

                            # 现货做空、合约做多的高资金费率条件
                            high_funding_short_spot_condition = (direction == DirectionType.SHORT_SPOT_LONG_FUTURES
                                                                 and basis_rate <= 0
                                                                 and normal_funding_rate <= -0.0002)

                            # 基于基差率阈值的正常条件
                            normal_condition = (
                                    (
                                            direction == DirectionType.LONG_SPOT_SHORT_FUTURES and basis_rate >= basis_threshold) or
                                    (
                                            direction == DirectionType.SHORT_SPOT_LONG_FUTURES and basis_rate <= -basis_threshold))

                            # 满足任一条件即可开仓
                            if normal_condition or high_funding_long_spot_condition or high_funding_short_spot_condition:
                                logger.info(
                                    f"{self.exchange_name} {symbol} 执行建仓 - "
                                    f"方向: {direction}, 资金费率: {format_funding_rate(funding_rate)}, 基差率: {format_basis_rate(basis_rate)}"
                                )
                                await self.pending_tasks_manager.remove_open_task(self.exchange_name, symbol)
                                # 执行建仓
                                await self.trade_executor.open_new_position(symbol=symbol, direction=direction)
                            else:
                                logger.debug(
                                    f"{self.exchange_name} {symbol} 基差率未满足建仓条件 - "
                                    f"方向: {direction}, 基差率: {format_basis_rate(basis_rate)}"
                                )
                                remaining_open_tasks.append(opportunity)
                        except Exception as e:
                            logger.error(
                                f"{self.exchange_name} {opportunity.get('symbol', '未知')} 处理建仓任务失败: {str(e)}")
                            remaining_open_tasks.append(opportunity)

                    # 5. 等待下一次检查
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.error(f"{self.exchange_name} 处理待处理任务失败: {str(e)}")
                    await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"{self.exchange_name} 任务处理器异常: {str(e)}")
        finally:
            logger.info(f"{self.exchange_name} 任务处理器已退出")
            self.is_running = False
