"""
待处理任务管理器 - 负责管理各个交易所的待处理任务列表
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

from crypt_carry.constant.trade_type import TradeType
from crypt_carry.core.exchange.order.order_state import OrderState

logger = logging.getLogger(__name__)


class PendingTasksManager:
    """待处理任务管理器，负责管理各个交易所的待处理任务列表

    实现为单例模式，按交易所进行隔离，便于在不同模块间共享任务状态。
    """

    _instance = None
    _tasks_by_exchange: Dict[str, Dict] = {}
    # 按交易所和交易对存储锁定资金
    # 结构: {exchange_name: {'total': float, symbols: {symbol: float}}}
    _locked_funds_by_exchange: Dict[str, Dict] = {}
    _tasks_lock = asyncio.Lock()  # 添加任务锁
    _funds_lock = asyncio.Lock()  # 添加资金锁

    @classmethod
    def get_instance(cls) -> 'PendingTasksManager':
        """获取PendingTasksManager的单例实例

        Returns:
            PendingTasksManager: 单例实例
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def get_tasks(self, exchange_name: str) -> Dict:
        """获取指定交易所的待处理任务列表

        Args:
            exchange_name: 交易所名称

        Returns:
            Dict: 待处理任务列表
        """
        # 如果exchange_name不存在，初始化
        if exchange_name not in self._tasks_by_exchange:
            self._tasks_by_exchange[exchange_name] = {
                'open_positions': [],  # 待建仓任务
                'close_positions': [],  # 待平仓任务
                'last_funding_time': None,  # 上次资金费率更新时间
                'is_force_rebalance': False  # 是否强制轮动标志
            }

        # 同时确保锁定资金记录存在
        if exchange_name not in self._locked_funds_by_exchange:
            self._locked_funds_by_exchange[exchange_name] = {
                'total': 0.0,  # 交易所总锁定资金
                'symbols': {}  # 按交易对存储锁定资金
            }
            
        return self._tasks_by_exchange[exchange_name]

    async def add_open_task(self, exchange_name: str, task: Dict, is_force_rebalance: Optional[bool] = None) -> None:
        """添加待建仓任务

        Args:
            exchange_name: 交易所名称
            task: 待建仓任务信息
            is_force_rebalance: 是否是强制轮动
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            if is_force_rebalance is None:
                is_force_rebalance = tasks.get('is_force_rebalance', False)
            # 检查是否已存在相同symbol的任务
            symbol = task.get('symbol')
            if symbol:
                # 移除相同symbol的任务
                tasks['open_positions'] = [t for t in tasks['open_positions'] if t.get('symbol') != symbol]
            tasks['open_positions'].append(task)
            tasks['is_force_rebalance'] = is_force_rebalance
            logger.debug(f"{exchange_name} 添加待建仓任务: {symbol}")

    async def add_close_task(self, exchange_name: str, symbol: str) -> None:
        """添加待平仓任务

        Args:
            exchange_name: 交易所名称
            symbol: 交易对
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            if symbol not in tasks['close_positions']:
                tasks['close_positions'].append(symbol)
                logger.info(f"{exchange_name} 添加待平仓任务: {symbol}")

    async def add_open_tasks(self, exchange_name: str, tasks: List[Dict], is_force_rebalance: bool = False) -> None:
        """批量添加待建仓任务

        Args:
            exchange_name: 交易所名称
            tasks: 待建仓任务列表
            is_force_rebalance: 是否是强制轮动
        """
        for task in tasks:
            await self.add_open_task(exchange_name, task, is_force_rebalance)

    async def add_close_tasks(self, exchange_name: str, symbols: List[str]) -> None:
        """批量添加待平仓任务

        Args:
            exchange_name: 交易所名称
            symbols: 交易对列表
        """
        for symbol in symbols:
            await self.add_close_task(exchange_name, symbol)

    async def remove_open_task(self, exchange_name: str, symbol: str) -> None:
        """移除待建仓任务

        Args:
            exchange_name: 交易所名称
            symbol: 交易对
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            tasks['open_positions'] = [t for t in tasks['open_positions'] if t.get('symbol') != symbol]
            logger.info(f"{exchange_name} 移除待建仓任务: {symbol}")

    async def remove_close_task(self, exchange_name: str, symbol: str) -> None:
        """移除待平仓任务

        Args:
            exchange_name: 交易所名称
            symbol: 交易对
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            if symbol in tasks['close_positions']:
                tasks['close_positions'].remove(symbol)
                logger.info(f"{exchange_name} 移除待平仓任务: {symbol}")

    async def update_funding_time(self, exchange_name: str, time: Optional[datetime] = None) -> None:
        """更新上次资金费率更新时间

        Args:
            exchange_name: 交易所名称
            time: 更新时间，默认为当前UTC时间
        """
        if time is None:
            time = datetime.now(timezone.utc)
        tasks = self.get_tasks(exchange_name)
        tasks['last_funding_time'] = time

    async def clear_tasks(self, exchange_name: str) -> None:
        """清空指定交易所的所有待处理任务

        Args:
            exchange_name: 交易所名称
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            if exchange_name in self._tasks_by_exchange:
                self._tasks_by_exchange[exchange_name] = {
                    'open_positions': [],
                    'close_positions': [],
                    'last_funding_time': None,
                    'is_force_rebalance': False
                }
                logger.info(f"{exchange_name} 清空所有待处理任务")

    async def clear_open_tasks(self, exchange_name: str, preserve_exempt: bool = False) -> None:
        """清空指定交易所的待建仓任务
        
        Args:
            exchange_name: 交易所名称
            preserve_exempt: 是否保留豁免清理的任务
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            # 如果非强制轮动，清空待建仓任务
            if not tasks.get('is_force_rebalance', False):
                tasks = self.get_tasks(exchange_name)
                if preserve_exempt:
                    # 保留标记为豁免清理的任务
                    exempt_tasks = [t for t in tasks['open_positions'] if t.get('is_exempt_from_clearing', False)]
                    removed_count = len(tasks['open_positions']) - len(exempt_tasks)
                    tasks['open_positions'] = exempt_tasks
                    logger.info(
                        f"{exchange_name} 清空待建仓任务，保留{len(exempt_tasks)}个豁免任务，移除{removed_count}个任务")
                else:
                    tasks['open_positions'] = []
                    logger.debug(f"{exchange_name} 清空待建仓任务")

    async def clear_close_tasks(self, exchange_name: str) -> None:
        """清空指定交易所的待平仓任务"""
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            count = len(tasks['close_positions'])
            tasks['close_positions'] = []
            logger.info(f"{exchange_name} 清空待平仓任务，移除{count}个任务")

    async def update_tasks(self, exchange_name: str, open_tasks: List[Dict], close_tasks: List[str]) -> None:
        """更新指定交易所的待处理任务

        Args:
            exchange_name: 交易所名称
            open_tasks: 待建仓任务列表
            close_tasks: 待平仓任务列表
        """
        async with self._tasks_lock:  # 使用锁保护任务操作
            tasks = self.get_tasks(exchange_name)
            tasks['open_positions'] = open_tasks
            tasks['close_positions'] = close_tasks
            logger.info(f"{exchange_name} 更新待处理任务 - 待建仓: {len(open_tasks)}, 待平仓: {len(close_tasks)}")

    async def get_open_tasks(self, exchange_name: str) -> List[Dict]:
        """获取待建仓任务列表

        Args:
            exchange_name: 交易所名称

        Returns:
            List[Dict]: 待建仓任务列表
        """
        async with self._tasks_lock:  # 使用锁保护任务读取
            return self.get_tasks(exchange_name)['open_positions']

    async def get_close_tasks(self, exchange_name: str) -> List[str]:
        """获取待平仓任务列表

        Args:
            exchange_name: 交易所名称

        Returns:
            List[str]: 待平仓任务列表
        """
        async with self._tasks_lock:  # 使用锁保护任务读取
            return self.get_tasks(exchange_name)['close_positions']

    async def get_funding_time(self, exchange_name: str) -> Optional[datetime]:
        """获取上次资金费率更新时间

        Args:
            exchange_name: 交易所名称

        Returns:
            Optional[datetime]: 上次资金费率更新时间
        """
        return self.get_tasks(exchange_name)['last_funding_time']

    async def get_task_counts(self, exchange_name: str) -> Dict[str, int]:
        """获取任务数量信息

        Args:
            exchange_name: 交易所名称

        Returns:
            Dict[str, int]: 包含待建仓和待平仓任务数量的字典
        """
        tasks = self.get_tasks(exchange_name)
        return {
            'open_count': len(tasks['open_positions']),
            'close_count': len(tasks['close_positions'])
        }

    async def recover_task(self, exchange_name: str, order_state: OrderState) -> None:
        """
        恢复任务
        """
        symbol = order_state.symbol
        logger.warning(f"{exchange_name} {symbol} 开始回滚任务, order_state :{order_state.__dict__}")

        new_task = {
            'symbol': symbol,  # 交易对符号，如 'BTC/USDT'
            'ewma_score': 0.0,  # EWMA得分
            'weight_score': 0.0,  # 权重得分
            'is_recovery': True,  # 标记为恢复创建的任务
            'is_exempt_from_clearing': order_state.trade_type == TradeType.CLOSE,  # 平仓任务豁免清理
            'trade_info': {
                'direction': order_state.direction,  # 交易方向，@DirectionType
                'spot_price': '',  # 现货价格
                'futures_price': '',  # 合约价格
                'basis_rate': '',  # 基差率
                'funding_rate': '',  # 资金费率
                'expected_profit': '',  # 预期收益
                'normalized_funding_rate': '',  # 标准化资金费率
                'normalized_expected_profit': ''  # 标准化预期收益
            }
        }

        # 延迟5秒
        await asyncio.sleep(5)
        # 根据交易类型使用不同的任务添加方法
        if order_state.trade_type == TradeType.CREATE:
            # 直接调用添加开仓任务的方法
            await self.add_open_task(exchange_name, new_task, None)
            logger.debug(f"{exchange_name} {symbol} 任务回滚完成，已重新添加到待建仓队列")
        else:
            # 调用添加平仓任务的方法
            await self.add_close_task(exchange_name, symbol)
            logger.debug(f"{exchange_name} {symbol} 任务回滚完成，已添加到待平仓队列")

        # 记录当前任务列表状态
        async with self._tasks_lock:
            tasks = self.get_tasks(exchange_name)
            open_tasks = [t.get('symbol') for t in tasks['open_positions']]
            logger.warning(f"{exchange_name} {symbol} 回滚后待建仓任务列表: {open_tasks}")

        logger.debug(f"{exchange_name} {symbol} 完成回滚")

    async def lock_funds(self, exchange_name: str, amount: float, symbol: str = '') -> float:
        """锁定指定交易所和交易对的资金

        Args:
            exchange_name: 交易所名称
            amount: 锁定金额
            symbol: 交易对，可选

        Returns:
            float: 锁定金额
        """
        async with self._funds_lock:  # 使用锁保护资金操作
            # 确保交易所记录存在
            if exchange_name not in self._locked_funds_by_exchange:
                self._locked_funds_by_exchange[exchange_name] = {
                    'total': 0.0,
                    'symbols': {}
                }

            # 按交易对锁定资金（如果提供了交易对）
            if symbol:
                if symbol not in self._locked_funds_by_exchange[exchange_name]['symbols']:
                    self._locked_funds_by_exchange[exchange_name]['symbols'][symbol] = 0.0
                self._locked_funds_by_exchange[exchange_name]['symbols'][symbol] += amount
                logger.info(f"{exchange_name}, {symbol}, 锁定金额: {amount}")

            # 累加锁定金额
            self._locked_funds_by_exchange[exchange_name]['total'] += amount

            logger.debug(f"{exchange_name} 锁定资金 {amount} USDT" +
                         (f" 用于 {symbol}" if symbol else ""))
            return amount

    async def release_funds(self, exchange_name: str, amount: float, symbol: str = '') -> float:
        """释放指定交易所和交易对的锁定资金

        Args:
            exchange_name: 交易所名称
            amount: 释放金额
            symbol: 交易对，可选

        Returns:
            float: 实际释放金额
        """
        async with self._funds_lock:  # 使用锁保护资金操作
            # 确保交易所记录存在
            if exchange_name not in self._locked_funds_by_exchange:
                self._locked_funds_by_exchange[exchange_name] = {
                    'total': 0.0,
                    'symbols': {}
                }
                return 0.0

            # 确保不会释放超过锁定的金额
            release_amount = min(amount, self._locked_funds_by_exchange[exchange_name]['total'])

            # 按交易对释放资金（如果提供了交易对）
            if symbol and symbol in self._locked_funds_by_exchange[exchange_name]['symbols']:
                symbol_release = min(release_amount, self._locked_funds_by_exchange[exchange_name]['symbols'][symbol])
                self._locked_funds_by_exchange[exchange_name]['symbols'][symbol] -= symbol_release

            # 减少锁定金额
            self._locked_funds_by_exchange[exchange_name]['total'] -= release_amount

            logger.debug(f"{exchange_name} 释放资金 {release_amount} USDT" +
                         (f" 来自 {symbol}" if symbol else ""))
            return release_amount

    async def get_locked_funds(self, exchange_name: str, symbol: str = '') -> float:
        """获取指定交易所和交易对当前锁定的资金

        Args:
            exchange_name: 交易所名称
            symbol: 交易对，可选

        Returns:
            float: 当前锁定的资金
        """
        async with self._funds_lock:  # 使用锁保护资金读取
            # 确保交易所记录存在
            if exchange_name not in self._locked_funds_by_exchange:
                self._locked_funds_by_exchange[exchange_name] = {
                    'total': 0.0,
                    'symbols': {}
                }

            # 如果指定了交易对，则返回该交易对的锁定金额
            if symbol:
                if symbol not in self._locked_funds_by_exchange[exchange_name]['symbols']:
                    return 0.0
                return self._locked_funds_by_exchange[exchange_name]['symbols'][symbol]
            else:
                # 返回交易所总锁定金额
                return self._locked_funds_by_exchange[exchange_name]['total']

    async def clear_locked_funds(self, exchange_name: str, symbol: str = '') -> None:
        """清空指定交易所和交易对的锁定资金

        Args:
            exchange_name: 交易所名称
            symbol: 交易对，可选
        """
        async with self._funds_lock:  # 使用锁保护资金操作
            if exchange_name not in self._locked_funds_by_exchange:
                return

            if symbol:
                # 清空指定交易对的锁定资金
                if symbol in self._locked_funds_by_exchange[exchange_name]['symbols']:
                    # 获取当前交易对锁定的金额
                    symbol_locked = self._locked_funds_by_exchange[exchange_name]['symbols'][symbol]
                    # 从总锁定金额中减去该交易对的锁定金额
                    self._locked_funds_by_exchange[exchange_name]['total'] -= symbol_locked
                    # 清空该交易对的锁定金额
                    self._locked_funds_by_exchange[exchange_name]['symbols'][symbol] = 0.0

                    logger.info(f"{exchange_name} {symbol} 清空锁定资金 {symbol_locked} USDT，"
                                f"交易所剩余总锁定: {self._locked_funds_by_exchange[exchange_name]['total']} USDT")
            else:
                # 清空整个交易所的锁定资金
                total_locked = self._locked_funds_by_exchange[exchange_name]['total']
                self._locked_funds_by_exchange[exchange_name] = {
                    'total': 0.0,
                    'symbols': {}
                }
                logger.info(f"{exchange_name} 清空所有锁定资金 {total_locked} USDT")
