"""
套保策略数据模型
定义了与套保相关的数据结构
"""
import datetime
from crypt_carry.web.app import db


class HedgingStrategy(db.Model):
    """多空套保策略模型"""
    __tablename__ = 'hedging_strategies'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='策略名称')
    description = db.Column(db.Text, nullable=True, comment='策略描述')
    is_active = db.Column(db.<PERSON>, default=True, comment='是否激活')
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow,
                           comment='更新时间')

    # 策略配置
    check_interval = db.Column(db.Integer, default=60, comment='检查间隔(秒)')
    auto_close = db.Column(db.<PERSON>, default=True, comment='是否自动平仓')
    max_open_positions = db.Column(db.Integer, default=5, comment='最大持仓数量')

    # 外键关系
    positions = db.relationship('HedgingPosition', backref='strategy', lazy=True, cascade='all, delete-orphan')
    conditions = db.relationship('HedgingCondition', backref='strategy', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<策略 {self.name}>'


class HedgingPosition(db.Model):
    """套保持仓模型"""
    __tablename__ = 'hedging_positions'

    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('hedging_strategies.id'), nullable=False)

    # 多头信息
    long_exchange = db.Column(db.String(50), nullable=False, comment='多头交易所')
    long_symbol = db.Column(db.String(20), nullable=False, comment='多头交易对')
    long_order_id = db.Column(db.String(100), nullable=True, comment='多头订单ID')
    long_amount = db.Column(db.Float, nullable=False, comment='多头数量')
    long_price = db.Column(db.Float, nullable=True, comment='多头价格')

    # 空头信息
    short_exchange = db.Column(db.String(50), nullable=False, comment='空头交易所')
    short_symbol = db.Column(db.String(20), nullable=False, comment='空头交易对')
    short_order_id = db.Column(db.String(100), nullable=True, comment='空头订单ID')
    short_amount = db.Column(db.Float, nullable=False, comment='空头数量')
    short_price = db.Column(db.Float, nullable=True, comment='空头价格')

    # 持仓状态
    status = db.Column(db.String(20), default='open', comment='持仓状态: open, closed, partially_closed')
    opened_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, comment='开仓时间')
    closed_at = db.Column(db.DateTime, nullable=True, comment='平仓时间')

    # 盈亏信息
    profit_loss = db.Column(db.Float, nullable=True, comment='盈亏金额(USDT)')
    profit_loss_percent = db.Column(db.Float, nullable=True, comment='盈亏百分比')

    def __repr__(self):
        return f'<持仓 {self.id}: {self.long_exchange}多{self.long_symbol} - {self.short_exchange}空{self.short_symbol}>'


class HedgingCondition(db.Model):
    """套保条件模型"""
    __tablename__ = 'hedging_conditions'

    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('hedging_strategies.id'), nullable=False)

    # 条件类型
    condition_type = db.Column(db.String(50), nullable=False,
                               comment='条件类型: funding_rate, price_difference, basis_rate')

    # 多头条件
    long_exchange = db.Column(db.String(50), nullable=False, comment='多头交易所')
    long_symbol = db.Column(db.String(20), nullable=False, comment='多头交易对')
    long_amount = db.Column(db.Float, nullable=False, comment='多头金额(USDT)')
    long_is_spot = db.Column(db.Boolean, default=True, comment='多头是否为现货')

    # 空头条件
    short_exchange = db.Column(db.String(50), nullable=False, comment='空头交易所')
    short_symbol = db.Column(db.String(20), nullable=False, comment='空头交易对')
    short_amount = db.Column(db.Float, nullable=False, comment='空头金额(USDT)')
    short_is_spot = db.Column(db.Boolean, default=False, comment='空头是否为现货')

    # 触发条件
    trigger_value = db.Column(db.Float, nullable=False, comment='触发值')
    comparison_operator = db.Column(db.String(10), default='>', comment='比较运算符: >, <, >=, <=, ==')

    # 条件状态
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    priority = db.Column(db.Integer, default=1, comment='优先级')

    def __repr__(self):
        return f'<条件 {self.id}: {self.condition_type} {self.long_exchange}/{self.short_exchange}>'
