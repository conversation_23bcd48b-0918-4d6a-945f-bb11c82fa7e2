"""
订单API测试脚本
用于测试新增的订单管理API功能
"""
import requests
import json
from datetime import datetime


class OrderAPITester:
    """订单API测试器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/hedging"
    
    def test_get_config(self):
        """测试获取配置信息"""
        print("=== 测试获取配置信息 ===")
        
        # 测试获取交易所列表
        response = requests.get(f"{self.api_base}/config/exchanges")
        print(f"获取交易所列表: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"交易所: {data.get('exchanges', [])}")
        
        # 测试获取交易对列表
        response = requests.get(f"{self.api_base}/config/symbols")
        print(f"获取交易对列表: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"交易对数量: {len(data.get('symbols', []))}")
        
        # 测试获取模板配置
        response = requests.get(f"{self.api_base}/config/templates")
        print(f"获取模板配置: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"模板类型: {list(data.get('templates', {}).keys())}")
        
        print()
    
    def test_create_hedging_order(self):
        """测试创建套保订单"""
        print("=== 测试创建套保订单 ===")
        
        # 测试数据
        hedging_data = {
            "conditions": [
                {
                    "type": "price_difference",
                    "enabled": True,
                    "trigger_value": 100.0,
                    "comparison_operator": ">"
                },
                {
                    "type": "funding_rate_diff",
                    "enabled": True,
                    "trigger_value": 0.001,
                    "comparison_operator": ">"
                },
                {
                    "type": "basis_rate",
                    "enabled": False,
                    "trigger_value": 0.005,
                    "comparison_operator": ">"
                }
            ],
            "long_exchange": "binance",
            "long_symbol": "BTC/USDT",
            "long_is_spot": True,
            "short_exchange": "okx",
            "short_symbol": "BTC/USDT",
            "short_is_spot": False,
            "amount_usdt": 1000.0,
            "priority": 1
        }
        
        response = requests.post(
            f"{self.api_base}/orders/hedging",
            headers={"Content-Type": "application/json"},
            data=json.dumps(hedging_data)
        )
        
        print(f"创建套保订单: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"订单ID: {data.get('order_id')}")
            print(f"创建的条件数: {len(data.get('conditions', []))}")
            return data.get('order_id')
        else:
            print(f"错误: {response.text}")
            return None
        
        print()
    
    def test_create_single_order(self):
        """测试创建单独订单"""
        print("=== 测试创建单独订单 ===")
        
        # 测试市价单
        market_order_data = {
            "exchange": "binance",
            "symbol": "BTC/USDT",
            "side": "buy",
            "order_type": "market",
            "amount": 0.001,
            "is_spot": True
        }
        
        response = requests.post(
            f"{self.api_base}/orders/single",
            headers={"Content-Type": "application/json"},
            data=json.dumps(market_order_data)
        )
        
        print(f"创建市价单: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"订单ID: {data.get('order', {}).get('order_id')}")
            market_order_id = data.get('order', {}).get('order_id')
        else:
            print(f"错误: {response.text}")
            market_order_id = None
        
        # 测试限价单
        limit_order_data = {
            "exchange": "okx",
            "symbol": "ETH/USDT",
            "side": "sell",
            "order_type": "limit",
            "amount": 0.1,
            "price": 2500.0,
            "is_spot": False
        }
        
        response = requests.post(
            f"{self.api_base}/orders/single",
            headers={"Content-Type": "application/json"},
            data=json.dumps(limit_order_data)
        )
        
        print(f"创建限价单: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"订单ID: {data.get('order', {}).get('order_id')}")
            limit_order_id = data.get('order', {}).get('order_id')
        else:
            print(f"错误: {response.text}")
            limit_order_id = None
        
        print()
        return market_order_id, limit_order_id
    
    def test_get_active_orders(self):
        """测试获取活跃订单"""
        print("=== 测试获取活跃订单 ===")
        
        response = requests.get(f"{self.api_base}/orders/active")
        print(f"获取活跃订单: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            orders = data.get('orders', [])
            print(f"订单总数: {len(orders)}")
            
            hedging_orders = [o for o in orders if o.get('type') == 'hedging']
            single_orders = [o for o in orders if o.get('type') == 'single']
            
            print(f"套保订单: {len(hedging_orders)}")
            print(f"单独订单: {len(single_orders)}")
            
            return orders
        else:
            print(f"错误: {response.text}")
            return []
        
        print()
    
    def test_cancel_order(self, order_id):
        """测试取消订单"""
        if not order_id:
            return
            
        print(f"=== 测试取消订单 {order_id} ===")
        
        response = requests.post(f"{self.api_base}/orders/{order_id}/cancel")
        print(f"取消订单: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"取消成功: {data.get('message')}")
        else:
            print(f"错误: {response.text}")
        
        print()
    
    def test_error_cases(self):
        """测试错误情况"""
        print("=== 测试错误情况 ===")
        
        # 测试无效的套保订单（缺少必要字段）
        invalid_hedging_data = {
            "conditions": [],  # 空条件列表
            "long_exchange": "binance"
            # 缺少其他必要字段
        }
        
        response = requests.post(
            f"{self.api_base}/orders/hedging",
            headers={"Content-Type": "application/json"},
            data=json.dumps(invalid_hedging_data)
        )
        
        print(f"无效套保订单: {response.status_code} (应该是400)")
        
        # 测试无效的单独订单（限价单没有价格）
        invalid_single_data = {
            "exchange": "binance",
            "symbol": "BTC/USDT",
            "side": "buy",
            "order_type": "limit",  # 限价单
            "amount": 0.001
            # 缺少价格
        }
        
        response = requests.post(
            f"{self.api_base}/orders/single",
            headers={"Content-Type": "application/json"},
            data=json.dumps(invalid_single_data)
        )
        
        print(f"无效单独订单: {response.status_code} (应该是400)")
        
        print()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始API测试...")
        print(f"测试时间: {datetime.now()}")
        print("=" * 50)
        
        try:
            # 1. 测试配置获取
            self.test_get_config()
            
            # 2. 测试创建订单
            hedging_order_id = self.test_create_hedging_order()
            market_order_id, limit_order_id = self.test_create_single_order()
            
            # 3. 测试获取订单列表
            orders = self.test_get_active_orders()
            
            # 4. 测试取消订单
            if hedging_order_id:
                self.test_cancel_order(hedging_order_id)
            
            # 5. 测试错误情况
            self.test_error_cases()
            
            print("=" * 50)
            print("所有测试完成!")
            
        except Exception as e:
            print(f"测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    # 创建测试器并运行测试
    tester = OrderAPITester()
    tester.run_all_tests()
    
    print("\n使用说明:")
    print("1. 确保Flask应用正在运行 (python -m flask run)")
    print("2. 确保API端点正确配置")
    print("3. 检查返回的状态码和数据格式")
    print("4. 可以修改测试数据来测试不同场景")
