"""
套保策略API路由模块（简化版）
只保留核心的下单功能，移除复杂的策略管理接口
"""
import json
import os
import asyncio
import functools
from datetime import datetime
from typing import Dict, List, Optional

from flask import jsonify, request

from . import hedging_blueprint

# 添加日志配置
try:
    from crypt_carry.utils.logger_config import get_web_service_logger
    logger = get_web_service_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


def async_to_sync(f):
    """异步转同步装饰器"""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# ==================== 配置管理 ====================

# 本地存储路径（使用JSON文件代替数据库）
try:
    from crypt_carry.utils.paths import get_web_data_dir
    STORAGE_PATH = str(get_web_data_dir())
    HEDGING_CONFIG_FILE = str(get_web_data_dir() / 'hedging_config.json')
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    STORAGE_PATH = os.path.join(os.path.dirname(__file__), '..', 'data')
    HEDGING_CONFIG_FILE = os.path.join(STORAGE_PATH, 'hedging_config.json')

# 确保存储目录存在
os.makedirs(STORAGE_PATH, exist_ok=True)

# 内存中的套保配置
_hedging_config = {
    'active_positions': [],
    'next_position_id': 1
}


def _save_config():
    """保存套保配置到本地文件"""
    try:
        with open(HEDGING_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(_hedging_config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存套保配置失败: {str(e)}")
        return False


def _load_config():
    """从本地文件加载套保配置"""
    global _hedging_config
    if os.path.exists(HEDGING_CONFIG_FILE):
        try:
            with open(HEDGING_CONFIG_FILE, 'r', encoding='utf-8') as f:
                _hedging_config = json.load(f)
        except Exception as e:
            print(f"加载套保配置失败: {str(e)}")


# 初始化加载配置
_load_config()

# 全局任务管理器实例
_global_task_manager = None


def _get_task_manager():
    """获取全局任务管理器实例"""
    global _global_task_manager
    if _global_task_manager is None:
        try:
            from crypt_carry.strategies.hedging.tasks.task_manager import get_task_manager
            _global_task_manager = get_task_manager()
            logger.info("任务管理器初始化成功")
        except ImportError as e:
            logger.error(f"无法导入任务管理器: {str(e)}")
            _global_task_manager = None
    return _global_task_manager


def _get_order_manager():
    """获取全局订单管理器实例"""
    from crypt_carry.core.exchange.account.order_manager import OrderManager
    return OrderManager.get_instance()


# ==================== 核心下单接口 ====================

@hedging_blueprint.route('/orders/hedging', methods=['POST'])
@async_to_sync
async def create_hedging_order():
    """
    创建套保订单

    这是核心的套保下单接口，根据前端提交的条件创建跨交易所套保订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"hedging_{int(datetime.now().timestamp())}"
        logger.info(f"收到套保下单请求，订单ID: {order_id}")
        logger.info(f"请求数据: {data}")

        # 验证必要字段 - 更新字段名以匹配前端
        required_fields = [
            'conditions', 'long_exchange', 'long_symbol',
            'short_exchange', 'short_symbol', 'amount', 'amount_currency'
        ]
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 解析条件
        conditions = data['conditions']
        if not isinstance(conditions, list):
            return jsonify({'success': False, 'error': '条件参数必须是数组格式'}), 400

        # 过滤启用的条件
        enabled_conditions = [c for c in conditions if c.get('enabled', False)]

        # 交易金额（根据amount_currency确定实际金额）
        amount = data['amount']
        amount_currency = data['amount_currency']

        # 统一交给策略管理器处理，不在API层判断执行时机
        created_conditions = []

        # 使用消息队列提交任务到套保策略服务
        try:
            logger.info("尝试获取任务管理器...")
            # 获取任务管理器实例
            task_manager = _get_task_manager()

            if task_manager:
                logger.info("使用消息队列模式提交套保任务")

                # 准备任务数据
                task_data = {
                    'order_id': order_id,
                    'conditions': enabled_conditions,
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_is_spot': data.get('short_is_spot', False),
                    'priority': data.get('priority', 1)
                }

                # 提交任务到消息队列
                task_result = task_manager.submit_hedging_order(task_data)

                if task_result['success']:
                    logger.info(f"套保任务提交成功: {task_result['task_id']}")
                    created_conditions = [{
                        'task_id': task_result['task_id'],
                        'order_id': order_id,
                        'execution_type': 'queued',
                        'status': 'submitted',
                        'submitted_at': task_result['submitted_at'],
                        'message': '任务已提交到消息队列，等待 Worker 处理'
                    }]
                else:
                    return jsonify({
                        'success': False,
                        'error': task_result['error']
                    }), 500
            else:
                raise Exception("任务管理器未初始化")

        except Exception as task_error:
            # 如果任务管理器失败，使用模拟模式
            logger.error(f"任务管理器失败，使用模拟模式: {str(task_error)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            print(f"任务管理器失败，使用模拟模式: {str(task_error)}")
            print(f"详细错误信息: {traceback.format_exc()}")

            # 模拟策略管理器的处理逻辑
            if len(enabled_conditions) == 0:
                # 模拟立即执行套保
                execution_data = {
                    'execution_id': f"{order_id}_immediate",
                    'execution_type': 'immediate',
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'status': 'executed',
                    'executed_at': datetime.now().isoformat(),
                    'result': {
                        'success': True,
                        'message': '立即套保执行成功 (模拟模式)',
                        'hedging_pair': {
                            'long_order': {'exchange': data['long_exchange'], 'symbol': data['long_symbol'], 'status': 'simulated'},
                            'short_order': {'exchange': data['short_exchange'], 'symbol': data['short_symbol'], 'status': 'simulated'}
                        },
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }
                created_conditions.append(execution_data)
            else:
                # 模拟添加组合条件（所有条件作为一个整体）
                condition_data = {
                    'condition_id': f"{order_id}_combined",
                    'condition_type': 'combined',
                    'conditions': enabled_conditions,  # 保存所有条件
                    'long_exchange': data['long_exchange'],
                    'long_symbol': data['long_symbol'],
                    'long_is_spot': data.get('long_is_spot', True),
                    'short_exchange': data['short_exchange'],
                    'short_symbol': data['short_symbol'],
                    'short_is_spot': data.get('short_is_spot', False),
                    'amount': amount,
                    'amount_currency': amount_currency,
                    'priority': data.get('priority', 1),
                    'created_at': datetime.now().isoformat(),
                    'status': 'monitoring',
                    'note': '模拟模式 - 未实际添加到策略管理器',
                    'logic': 'ALL_CONDITIONS_MUST_BE_MET'  # 标明逻辑：所有条件都必须满足
                }
                created_conditions.append(condition_data)

        # 构建返回结果
        # 根据创建的条件类型判断执行模式
        is_immediate = (created_conditions and
                       created_conditions[0].get('execution_type') == 'immediate')

        if is_immediate:
            # 立即执行的情况
            execution_status = "已立即执行" if created_conditions else "执行失败"
            result = {
                'success': True,
                'message': f'套保订单创建成功，{execution_status}',
                'order_id': order_id,
                'execution_type': 'immediate',
                'conditions': created_conditions,
                'execution_status': execution_status,
                'next_steps': [
                    '1. 套保已立即执行完成',
                    '2. 可通过API查询执行结果',
                    '3. 查看持仓管理器了解当前持仓状态'
                ],
                'created_at': datetime.now().isoformat()
            }
        else:
            # 条件监控的情况
            monitoring_status = "已启动监控" if created_conditions else "监控启动失败"
            result = {
                'success': True,
                'message': f'套保订单创建成功，{monitoring_status}',
                'order_id': order_id,
                'execution_type': 'conditional',
                'conditions': created_conditions,
                'monitoring_status': monitoring_status,
                'next_steps': [
                    '1. 系统将持续监控市场条件',
                    '2. 当满足所有触发条件时自动执行套保',
                    '3. 可通过API查询执行状态'
                ],
                'created_at': datetime.now().isoformat()
            }

        return jsonify(result), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/single', methods=['POST'])
@async_to_sync
async def create_single_order():
    """
    创建单独订单（非套保）

    这是核心的单独下单接口，在指定交易所创建单个订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 生成唯一的订单ID
        order_id = f"single_{int(datetime.now().timestamp())}"

        # 验证必要字段 - 更新以匹配前端数据格式
        required_fields = ['exchange', 'symbol', 'side', 'order_type', 'amount', 'amount_currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

        # 集成实际的交易执行器
        try:
            # 计算实际交易数量
            amount = data['amount']
            if 'amount_currency' in data:
                # 如果是按金额下单，需要转换为数量
                # 这里简化处理，实际应用中需要根据当前价格计算
                if data['amount_currency'] in ['USDT', 'USDC']:
                    # 假设当前价格，实际应该从市场数据获取
                    current_price = data.get('price', 50000.0)  # 默认价格
                    if data['order_type'] == 'market':
                        # 市价单需要获取实时价格
                        # TODO: 从市场数据管理器获取实时价格
                        pass
                    amount = data['amount'] / current_price

            # 尝试使用实际的订单管理器，如果失败则使用模拟模式
            try:
                # 获取订单管理器实例
                order_manager = _get_order_manager()

                # 执行订单
                order = await order_manager.create_order(
                    exchange=data['exchange'],
                    symbol=data['symbol'],
                    order_type=data['order_type'],
                    side=data['side'],
                    amount=amount,
                    price=data.get('price'),
                    is_spot=data.get('is_spot', True)
                )

                # 实际订单执行成功
                result = {
                    'success': True,
                    'message': '订单提交成功',
                    'order': {
                        'order_id': order.get('id', order_id),
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': order.get('status', 'submitted'),
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': order.get('id'),
                        'filled': order.get('filled', 0),
                        'remaining': order.get('remaining', amount),
                        'cost': order.get('cost', 0)
                    }
                }

            except Exception as order_error:
                # 如果实际订单执行失败，使用模拟模式
                print(f"实际订单执行失败，使用模拟模式: {str(order_error)}")

                # 模拟订单执行
                result = {
                    'success': True,
                    'message': '订单提交成功 (模拟模式)',
                    'order': {
                        'order_id': order_id,
                        'exchange': data['exchange'],
                        'symbol': data['symbol'],
                        'side': data['side'],
                        'order_type': data['order_type'],
                        'amount': amount,
                        'price': data.get('price'),
                        'is_spot': data.get('is_spot', True),
                        'status': 'submitted',
                        'created_at': datetime.now().isoformat(),
                        'exchange_order_id': f"mock_{order_id}",
                        'filled': 0,
                        'remaining': amount,
                        'cost': 0,
                        'note': '模拟模式 - 未实际执行交易'
                    }
                }

            return jsonify(result), 201

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'订单执行失败: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理请求失败: {str(e)}'
        }), 500


# ==================== 任务管理接口 ====================

@hedging_blueprint.route('/tasks/<task_id>/status', methods=['GET'])
def get_task_status(task_id):
    """
    查询任务状态

    Args:
        task_id: 任务ID

    Returns:
        JSON响应包含任务状态信息
    """
    try:
        task_manager = _get_task_manager()

        if task_manager:
            status_result = task_manager.get_task_status(task_id)
            return jsonify(status_result)
        else:
            return jsonify({
                'success': False,
                'error': '任务管理器未初始化'
            }), 400

    except Exception as e:
        logger.error(f"查询任务状态失败: {task_id}, 错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询任务状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/tasks/<task_id>/cancel', methods=['POST'])
def cancel_task(task_id):
    """
    取消任务

    Args:
        task_id: 任务ID

    Returns:
        JSON响应
    """
    try:
        task_manager = _get_task_manager()

        if task_manager:
            cancel_result = task_manager.cancel_task(task_id)
            return jsonify(cancel_result)
        else:
            return jsonify({
                'success': False,
                'error': '任务管理器未初始化'
            }), 400

    except Exception as e:
        logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'取消任务失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/worker/status', methods=['GET'])
def get_worker_status():
    """
    获取 Worker 状态

    Returns:
        JSON响应包含 Worker 状态信息
    """
    try:
        task_manager = _get_task_manager()

        if task_manager:
            worker_result = task_manager.get_worker_status()
            return jsonify(worker_result)
        else:
            return jsonify({
                'success': False,
                'error': '任务管理器未初始化',
                'worker_status': {
                    'active_workers': [],
                    'worker_count': 0,
                    'status': 'unavailable'
                }
            })

    except Exception as e:
        logger.error(f"获取 Worker 状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取 Worker 状态失败: {str(e)}'
        }), 500


# ==================== 辅助接口 ====================

@hedging_blueprint.route('/config/symbols', methods=['GET'])
def get_trading_symbols():
    """
    获取支持的交易对列表
    """
    # 这里应该从交易所API获取实际的交易对列表
    # 暂时返回常用的交易对
    symbols = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'DOT/USDT',
        'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT', 'LINK/USDT', 'UNI/USDT'
    ]

    return jsonify({
        'success': True,
        'symbols': symbols
    })


@hedging_blueprint.route('/orders/hedging/status', methods=['GET'])
def get_hedging_status():
    """
    查询套保条件的监控状态

    返回当前所有活跃的套保条件及其状态
    """
    try:
        # 使用任务管理器查询状态
        task_manager = _get_task_manager()

        if task_manager:
            logger.info("使用任务管理器查询套保状态")

            # 通过消息队列查询状态
            status_result = task_manager.get_hedging_status()

            if status_result['success']:
                return jsonify(status_result)
            else:
                return jsonify({
                    'success': False,
                    'error': status_result['error']
                }), 500
        else:
            # 如果任务管理器不可用，返回默认状态
            return jsonify({
                'success': True,
                'message': '任务管理器未初始化，套保策略服务可能未启动',
                'is_running': False,
                'conditions': [],
                'total_conditions': 0,
                'worker_status': 'unavailable',
                'note': '请确保套保策略 Worker 服务已启动'
            })

    except Exception as e:
        logger.error(f"查询套保状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询套保状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/hedging/<condition_id>', methods=['DELETE'])
def remove_hedging_condition(condition_id):
    """
    移除指定的套保条件

    Args:
        condition_id: 条件ID
    """
    try:
        # 使用任务管理器移除条件
        task_manager = _get_task_manager()

        if task_manager:
            logger.info(f"使用任务管理器移除套保条件: {condition_id}")

            # 通过消息队列提交停止监控任务
            stop_result = task_manager.stop_condition_monitoring(condition_id)

            if stop_result['success']:
                return jsonify({
                    'success': True,
                    'message': f'套保条件 {condition_id} 移除任务已提交',
                    'task_id': stop_result['task_id'],
                    'condition_id': condition_id
                })
            else:
                return jsonify({
                    'success': False,
                    'error': stop_result['error']
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': '任务管理器未初始化，套保策略服务可能未启动'
            }), 400

    except Exception as e:
        logger.error(f"移除套保条件失败: {condition_id}, 错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'移除套保条件失败: {str(e)}'
        }), 500


# ==================== 新架构API接口 ====================

@hedging_blueprint.route('/orders/<order_id>/status', methods=['GET'])
@async_to_sync
async def get_order_status(order_id: str):
    """获取订单状态

    Args:
        order_id: 订单ID

    Returns:
        订单状态信息
    """
    try:
        print(f"查询订单状态: {order_id}")

        # 导入订单缓存管理器
        from crypt_carry.strategies.hedging.orders.cache_manager import get_order_cache_manager

        order_cache = get_order_cache_manager()

        # 获取订单摘要
        order_summary = await order_cache.get_order_summary(order_id)

        if not order_summary:
            return jsonify({
                'success': False,
                'error': f'订单不存在: {order_id}'
            }), 404

        # 获取详细的订单信息
        order = await order_cache.get_order(order_id)

        if order:
            # 构建详细状态信息
            result = {
                'success': True,
                'order_id': order_id,
                'summary': order_summary,
                'details': {
                    'execution_type': order.execution_type.value,
                    'conditions': order.conditions,
                    'long_exchange': order.long_exchange,
                    'long_symbol': order.long_symbol,
                    'long_is_spot': order.long_is_spot,
                    'short_exchange': order.short_exchange,
                    'short_symbol': order.short_symbol,
                    'short_is_spot': order.short_is_spot,
                    'amount': order.amount,
                    'amount_currency': order.amount_currency,
                    'priority': order.priority,
                    'split_tasks': order.split_tasks,
                    'execution_results': order.execution_results
                }
            }
        else:
            result = {
                'success': True,
                'order_id': order_id,
                'summary': order_summary,
                'details': None
            }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询订单状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询订单状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/orders/list', methods=['GET'])
@async_to_sync
async def list_orders():
    """获取订单列表

    Returns:
        订单列表
    """
    try:
        print("查询订单列表")

        # 获取查询参数
        status = request.args.get('status')  # 可选的状态过滤
        limit = int(request.args.get('limit', 50))  # 限制数量

        # 导入订单缓存管理器
        from crypt_carry.strategies.hedging.orders.cache_manager import get_order_cache_manager, OrderStatus

        order_cache = get_order_cache_manager()

        if status:
            # 根据状态过滤
            try:
                status_enum = OrderStatus(status)
                orders = await order_cache.get_orders_by_status(status_enum)
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': f'无效的订单状态: {status}'
                }), 400
        else:
            # 获取所有订单
            orders = list(order_cache.orders.values())

        # 限制数量并按创建时间倒序排列
        orders = sorted(orders, key=lambda x: x.created_at or datetime.min, reverse=True)[:limit]

        # 构建订单摘要列表
        order_summaries = []
        for order in orders:
            summary = await order_cache.get_order_summary(order.order_id)
            if summary:
                order_summaries.append(summary)

        result = {
            'success': True,
            'orders': order_summaries,
            'total_count': len(order_summaries),
            'filter_status': status,
            'limit': limit
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询订单列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询订单列表失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/executor/status', methods=['GET'])
@async_to_sync
async def get_executor_status():
    """获取任务执行器状态

    Returns:
        执行器状态信息
    """
    try:
        print("查询任务执行器状态")

        # 导入任务执行器
        from crypt_carry.strategies.hedging.orders.executor import get_task_executor

        task_executor = get_task_executor()

        # 获取执行器状态
        status = await task_executor.get_execution_status()

        result = {
            'success': True,
            'executor_status': status
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"查询任务执行器状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询任务执行器状态失败: {str(e)}'
        }), 500


@hedging_blueprint.route('/tasks/<task_id>/execute', methods=['POST'])
@async_to_sync
async def force_execute_task(task_id: str):
    """强制执行指定任务

    Args:
        task_id: 任务ID

    Returns:
        执行结果
    """
    try:
        print(f"强制执行任务: {task_id}")

        # 导入任务执行器
        from crypt_carry.strategies.hedging.orders.executor import get_task_executor

        task_executor = get_task_executor()

        # 强制执行任务
        result = await task_executor.force_execute_task(task_id)

        return jsonify(result), 200

    except Exception as e:
        print(f"强制执行任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'强制执行任务失败: {str(e)}'
        }), 500