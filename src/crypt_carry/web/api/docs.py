"""
API文档配置模块
使用Flask-RESTX创建Swagger文档
"""
from flask_restx import Api, fields
from flask import Blueprint

# 创建API文档蓝图
docs_blueprint = Blueprint('docs', __name__)

# 创建API实例
api = Api(
    docs_blueprint,
    version='1.0',
    title='Crypt Carry API',
    description='加密货币套利交易系统API文档',
    doc='/docs/',
    prefix=''
)

# 创建命名空间
hedging_ns = api.namespace('hedging', description='套保交易相关API')
exchanges_ns = api.namespace('exchanges', description='交易所相关API')

# ==================== 数据模型定义 ====================

# 套保条件模型
hedging_condition_model = api.model('HedgingCondition', {
    'type': fields.String(required=True, description='条件类型',
                         enum=['price_difference', 'funding_rate_diff', 'basis_rate']),
    'enabled': fields.Boolean(required=True, description='是否启用'),
    'trigger_value': fields.Float(required=True, description='触发值'),
    'comparison_operator': fields.String(required=True, description='比较运算符',
                                       enum=['>', '<', '>=', '<=', '=='])
})

# 套保订单请求模型
hedging_order_request = api.model('HedgingOrderRequest', {
    'conditions': fields.List(fields.Nested(hedging_condition_model),
                             required=True, description='套保条件列表'),
    'long_exchange': fields.String(required=True, description='多头交易所'),
    'long_symbol': fields.String(required=True, description='多头交易对'),
    'long_is_spot': fields.Boolean(default=True, description='多头是否为现货'),
    'short_exchange': fields.String(required=True, description='空头交易所'),
    'short_symbol': fields.String(required=True, description='空头交易对'),
    'short_is_spot': fields.Boolean(default=False, description='空头是否为现货'),
    'amount': fields.Float(required=True, description='交易金额'),
    'amount_currency': fields.String(required=True, description='金额币种',
                                   enum=['USDT', 'USDC']),
    'priority': fields.Integer(default=1, description='优先级')
})

# 单独订单请求模型
single_order_request = api.model('SingleOrderRequest', {
    'exchange': fields.String(required=True, description='交易所'),
    'symbol': fields.String(required=True, description='交易对'),
    'side': fields.String(required=True, description='买卖方向', enum=['buy', 'sell']),
    'order_type': fields.String(required=True, description='订单类型',
                               enum=['market', 'limit']),
    'amount': fields.Float(required=True, description='交易金额'),
    'amount_currency': fields.String(required=True, description='金额币种',
                                   enum=['USDT', 'USDC']),
    'price': fields.Float(description='价格（限价单必填）'),
    'is_spot': fields.Boolean(default=True, description='是否为现货')
})

# 订单信息模型
order_info_model = api.model('OrderInfo', {
    'order_id': fields.String(description='订单ID'),
    'exchange': fields.String(description='交易所'),
    'symbol': fields.String(description='交易对'),
    'side': fields.String(description='买卖方向'),
    'order_type': fields.String(description='订单类型'),
    'amount': fields.Float(description='数量'),
    'price': fields.Float(description='价格'),
    'status': fields.String(description='订单状态'),
    'created_at': fields.String(description='创建时间'),
    'filled': fields.Float(description='已成交数量'),
    'remaining': fields.Float(description='剩余数量'),
    'cost': fields.Float(description='成交金额')
})

# 响应模型
api_response_model = api.model('ApiResponse', {
    'success': fields.Boolean(description='是否成功'),
    'message': fields.String(description='响应消息'),
    'error': fields.String(description='错误信息')
})

# 套保订单响应模型
hedging_order_response = api.inherit('HedgingOrderResponse', api_response_model, {
    'order_id': fields.String(description='订单ID'),
    'conditions': fields.List(fields.Raw, description='创建的条件列表'),
    'created_at': fields.String(description='创建时间')
})

# 单独订单响应模型
single_order_response = api.inherit('SingleOrderResponse', api_response_model, {
    'order': fields.Nested(order_info_model, description='订单信息')
})

# 交易所信息模型
exchange_info_model = api.model('ExchangeInfo', {
    'id': fields.String(description='交易所ID'),
    'name': fields.String(description='交易所名称'),
    'spot_enabled': fields.Boolean(description='是否支持现货'),
    'futures_enabled': fields.Boolean(description='是否支持合约')
})

# 交易对信息模型
symbol_info_model = api.model('SymbolInfo', {
    'symbol': fields.String(description='交易对'),
    'base': fields.String(description='基础货币'),
    'quote': fields.String(description='计价货币')
})

# 资金费率模型
funding_rate_model = api.model('FundingRate', {
    'symbol': fields.String(description='交易对'),
    'rate': fields.Float(description='当前资金费率'),
    'next_funding_time': fields.Integer(description='下次资金费率时间戳'),
    'estimated_rate': fields.Float(description='预估资金费率')
})

# 策略信息模型
strategy_info_model = api.model('StrategyInfo', {
    'id': fields.Integer(description='策略ID'),
    'name': fields.String(description='策略名称'),
    'description': fields.String(description='策略描述'),
    'is_active': fields.Boolean(description='是否激活'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

# ==================== API路由定义 ====================
# 注意：实际的路由实现在各自的routes.py文件中
# 这里只定义数据模型，路由装饰器在routes.py中使用

# 导入文档路由以注册到命名空间
from . import docs_routes  # noqa
