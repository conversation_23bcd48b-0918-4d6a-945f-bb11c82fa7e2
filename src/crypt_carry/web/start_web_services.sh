#!/bin/bash

# Crypt Carry Web Services 启动脚本 (兼容性脚本)
# 重定向到新的脚本位置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 新的脚本路径
NEW_WEB_SCRIPT="$PROJECT_ROOT/src/scripts/web/start_web_services.sh"

log_warning "此脚本已移动到新位置，请使用新的脚本路径："
log_info "新位置: $NEW_WEB_SCRIPT"
log_info ""

# 检查新脚本是否存在
if [ ! -f "$NEW_WEB_SCRIPT" ]; then
    log_error "新的Web服务脚本不存在: $NEW_WEB_SCRIPT"
    log_info "请运行项目安装脚本: ./src/scripts/utils/install_project.sh"
    exit 1
fi

log_info "重定向到新的脚本位置..."
exec "$NEW_WEB_SCRIPT" "$@"
