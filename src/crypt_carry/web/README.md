# Crypt Carry Web 模块

这是 Crypt Carry 项目的 Web 控制中心，提供完整的前后端交互界面，用于管理整个项目的所有交易策略，包括：
- 加密货币资金费率套利策略
- 跨交易所套保策略
- 其他量化交易策略
- 交易所账户管理
- 风险控制和监控

## 📁 项目结构

```
src/crypt_carry/web/
├── README.md                    # 本文件
├── app.py                       # Flask 应用入口
├── config.py                    # Web 应用配置
├── api/                         # 后端 API
│   ├── __init__.py
│   ├── hedging/                 # 套保策略 API
│   │   ├── __init__.py
│   │   └── routes.py           # 套保相关路由
│   ├── funding/                 # 资金费率 API
│   │   ├── __init__.py
│   │   └── routes.py           # 资金费率相关路由
│   ├── exchanges/               # 交易所管理 API
│   │   ├── __init__.py
│   │   └── routes.py           # 交易所相关路由
│   └── trading.py              # 通用交易 API
├── data/                        # 数据存储目录
│   └── hedging_config.json     # 套保配置数据
├── frontend/                    # 前端应用
│   ├── package.json            # Node.js 依赖配置
│   ├── yarn.lock               # 依赖锁定文件
│   ├── tsconfig.json           # TypeScript 配置
│   ├── tailwind.config.js      # Tailwind CSS 配置
│   ├── vite.config.ts          # Vite 构建配置
│   ├── public/                 # 静态资源
│   └── src/                    # 源代码
│       ├── App.tsx             # 主应用组件
│       ├── main.tsx            # 应用入口
│       ├── components/         # 可复用组件
│       │   ├── ui/             # UI 基础组件
│       │   ├── layout/         # 布局组件
│       │   ├── HedgingOrderForm.tsx    # 套保订单表单
│       │   └── SingleOrderForm.tsx     # 单独订单表单
│       └── pages/              # 页面组件
│           ├── HomePage.tsx            # 首页
│           ├── HedgingStrategyPage.tsx # 套保策略页面
│           ├── PositionsPage.tsx       # 持仓管理页面
│           └── OrderManagement.tsx     # 订单管理页面
└── schemas/                     # 数据模型定义
    ├── __init__.py
    ├── hedging.py              # 套保相关数据模型
    └── funding.py              # 资金费率相关数据模型
```

## 🚀 快速启动

### 环境要求

- **Python**: 3.8+
- **Node.js**: 16+
- **Yarn**: 1.22+

### 一键启动（推荐）

我们提供了便捷的启动脚本，可以一键启动前后端服务：

```bash
# 进入 web 目录
cd src/crypt_carry/web

# 启动所有服务
./start_web_services.sh

# 其他命令
./start_web_services.sh status   # 检查服务状态
./start_web_services.sh stop     # 停止所有服务
./start_web_services.sh restart  # 重启所有服务
./start_web_services.sh logs     # 查看服务日志
./start_web_services.sh help     # 查看帮助
```

**启动脚本功能：**
- ✅ 检查conda环境
- ✅ 自动检查和安装依赖
- ✅ 检查端口冲突并处理
- ✅ 同时启动前后端服务
- ✅ 实时状态监控
- ✅ 优雅的服务停止
- ✅ 日志查看和管理

### 环境要求

本项目使用conda进行Python环境管理：

```bash
# 1. 安装conda（如果尚未安装）
# 下载并安装Miniconda: https://docs.conda.io/en/latest/miniconda.html

# 2. 创建项目环境
conda create -n crypt_carry python=3.9
conda activate crypt_carry312

# 3. 启动服务
cd src/crypt_carry/web
./start_web_services.sh
```

**注意：**
- 必须在激活的conda环境中运行
- 使用项目根目录的requirements.txt安装Python依赖
- 前端服务使用Node.js/Yarn，不需要Python环境
- 启动脚本会检查conda环境并给出明确指导

### 手动启动（开发调试）

如果需要单独启动服务进行调试：

#### 后端服务

```bash
# 确保在conda环境中
conda activate crypt_carry312

# 安装依赖（使用项目根目录的requirements.txt）
cd /path/to/crypt_carry  # 项目根目录
pip install -r requirements.txt

# 设置环境变量
export FLASK_APP=app.py
export FLASK_ENV=development
export FLASK_DEBUG=1
export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"

# 启动Flask应用
python app.py
```

**后端服务将在 http://localhost:5000 启动**

#### 前端服务

```bash
# 进入前端目录
cd src/crypt_carry/web/frontend

# 安装依赖
yarn install

# 启动开发服务器
yarn dev

# 或者构建生产版本
yarn build
yarn preview
```

**前端服务将在 http://localhost:5173 启动**

## 📋 快速参考

### 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:3000 | React + TypeScript |
| 后端API | http://localhost:5001 | Flask + Python |
| API文档 | http://localhost:5001/api/docs | Swagger/OpenAPI 文档 |
| 健康检查 | http://localhost:5001/health | 服务状态检查 |

### 常用命令

| 操作 | 命令 | 说明 |
|------|------|------|
| 启动服务 | `./start_web_services.sh` | 启动前后端所有服务 |
| 检查状态 | `./start_web_services.sh status` | 检查服务运行状态 |
| 停止服务 | `./start_web_services.sh stop` | 停止所有服务 |
| 查看日志 | `./start_web_services.sh logs` | 查看服务日志 |
| 重启服务 | `./start_web_services.sh restart` | 重启所有服务 |
| 帮助信息 | `./start_web_services.sh help` | 显示帮助信息 |

### 目录结构

| 目录 | 说明 |
|------|------|
| `api/` | 后端API路由和逻辑 |
| `frontend/` | 前端React应用 |
| `schemas/` | 数据模型定义 |
| `data/` | 数据存储目录 |
| `logs/` | 服务日志文件 |

## 🔧 开发模式

### 同时启动前后端服务

```bash
# 方式1: 使用启动脚本（推荐）
conda activate crypt_carry312
cd src/crypt_carry/web
./start_web_services.sh

# 方式2: 手动启动（调试用）
# 终端 1: 启动后端
conda activate crypt_carry312
cd src/crypt_carry/web
python app.py

# 终端 2: 启动前端
cd src/crypt_carry/web/frontend
yarn dev
```

### 环境变量配置

在conda环境中设置环境变量：

```bash
# 激活环境
conda activate crypt_carry312

# 设置开发环境变量
export FLASK_ENV=development
export FLASK_DEBUG=1
export PYTHONPATH="$PROJECT_ROOT/src:$PYTHONPATH"

# 或者创建 .env 文件（在 src/crypt_carry/web/ 目录下）
cat > .env << EOF
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-here
API_BASE_URL=http://localhost:5000
EOF
```

## 📡 API 端点

### 套保策略 API (`/api/hedging/`)

#### 配置相关
- `GET /api/hedging/config/templates` - 获取套保条件模板
- `GET /api/hedging/config/exchanges` - 获取支持的交易所列表
- `GET /api/hedging/config/symbols` - 获取支持的交易对列表

#### 订单管理
- `POST /api/hedging/orders/hedging` - 创建套保订单
- `POST /api/hedging/orders/single` - 创建单独订单
- `GET /api/hedging/orders/active` - 获取活跃订单列表
- `POST /api/hedging/orders/<order_id>/cancel` - 取消订单

#### 策略管理
- `GET /api/hedging/strategies` - 获取所有策略
- `POST /api/hedging/strategies` - 创建新策略
- `GET /api/hedging/strategies/<id>` - 获取特定策略
- `PUT /api/hedging/strategies/<id>` - 更新策略
- `DELETE /api/hedging/strategies/<id>` - 删除策略

#### 持仓管理
- `GET /api/hedging/positions` - 获取所有持仓
- `POST /api/hedging/positions` - 创建新持仓
- `POST /api/hedging/positions/<id>/close` - 平仓

### 资金费率 API (`/api/funding/`)

- `GET /api/funding/rates` - 获取资金费率数据
- `GET /api/funding/arbitrage` - 获取套利机会

## 🎯 主要功能

### 1. 策略管理中心
- **资金费率套利**: 监控和执行资金费率套利策略
- **跨交易所套保**: 多条件设置的套保策略管理
- **策略配置**: 创建、编辑、启停各种交易策略
- **策略监控**: 实时监控策略运行状态和性能

### 2. 交易执行
- **套保订单**: 支持价差、资金费率差异、基差率等多种触发条件
- **单独订单**: 现货/合约、市价/限价单的灵活交易
- **批量操作**: 支持批量下单和订单管理
- **实时执行**: 策略触发时的自动订单执行

### 3. 交易所管理
- **多交易所支持**: 统一管理多个交易所账户
- **账户监控**: 实时查看账户余额和持仓
- **API管理**: 交易所API密钥的安全管理
- **连接状态**: 监控交易所连接状态和延迟

### 4. 风险控制
- **实时监控**: 持仓、盈亏、风险指标实时监控
- **风险限制**: 可配置的风险控制参数
- **止损止盈**: 自动化的风险管理
- **报警系统**: 异常情况的及时通知

### 5. 数据分析
- **交易历史**: 详细的交易记录和分析
- **策略回测**: 策略历史表现分析
- **市场数据**: 实时市场数据展示
- **报表生成**: 自动化的交易报表

## 🧪 测试

### API 测试

```bash
# 运行 API 测试脚本
cd src/crypt_carry/web/api/hedging
python test_orders_api.py
```

### 前端测试

```bash
cd src/crypt_carry/web/frontend

# 运行单元测试
yarn test

# 运行 E2E 测试
yarn test:e2e
```

## 📦 构建和部署

### 前端构建

```bash
cd src/crypt_carry/web/frontend

# 构建生产版本
yarn build

# 预览构建结果
yarn preview
```

### 后端部署

```bash
# 使用 Gunicorn 部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# 或使用 uWSGI
pip install uwsgi
uwsgi --http :5000 --module app:app
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :5000  # 后端端口
   lsof -i :5173  # 前端端口

   # 杀死占用进程
   kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存
   pip cache purge
   yarn cache clean

   # 重新安装
   pip install -r requirements.txt --force-reinstall
   yarn install --force
   ```

3. **API 连接失败**
   - 检查后端服务是否正常启动
   - 确认 API 基础 URL 配置正确
   - 检查防火墙和网络设置

### 日志查看

```bash
# 后端日志
tail -f logs/app.log

# 前端开发服务器日志
# 直接在终端查看 yarn dev 输出
```

## 🤝 开发指南

### 添加新的 API 端点

1. 在 `api/` 目录下创建或修改路由文件
2. 定义数据模型（在 `schemas/` 目录）
3. 实现业务逻辑
4. 添加测试用例

### 添加新的前端页面

1. 在 `frontend/src/pages/` 创建新组件
2. 在 `App.tsx` 中添加路由
3. 在导航菜单中添加链接
4. 实现页面逻辑和样式

### 代码规范

- **Python**: 遵循 PEP 8 规范
- **TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 使用约定式提交格式

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🆘 获取帮助

如有问题或建议，请：

1. 查看本 README 文档
2. 检查 [Issues](../../issues) 页面
3. 创建新的 Issue 描述问题
4. 联系项目维护者

## 🐍 Conda环境管理

本项目统一使用conda进行Python环境管理，简单高效：

### 安装conda

如果尚未安装conda，请访问：https://docs.conda.io/en/latest/miniconda.html

### 创建项目环境

```bash
# 创建专用环境
conda create -n crypt_carry python=3.9

# 激活环境
conda activate crypt_carry312

# 安装项目依赖
cd src/crypt_carry/web
pip install -r requirements.txt
```

### 日常使用

```bash
# 每次开发前激活环境
conda activate crypt_carry312

# 启动服务
cd src/crypt_carry/web
./start_web_services.sh
```

### 为什么选择conda？

- ✅ **更好的包管理**：自动解决依赖冲突
- ✅ **Python版本管理**：支持多个Python版本
- ✅ **科学计算优化**：预编译的数值计算库
- ✅ **跨平台一致性**：Linux/macOS/Windows统一体验
- ✅ **项目隔离**：完全独立的环境

## 🔧 配置说明

### 后端配置

#### Flask 应用配置 (`config.py`)

```python
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    DEBUG = True

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # CORS 配置
    CORS_ORIGINS = ['http://localhost:5173', 'http://127.0.0.1:5173']

    # API 配置
    API_TITLE = 'Crypt Carry API'
    API_VERSION = 'v1'
```

#### 套保策略配置 (`src/crypt_carry/config/hedging_config.yaml`)

```yaml
# 支持的交易所
EXCHANGES:
  - binance
  - okx

# 策略基础配置
STRATEGY:
  MAX_POSITIONS: 10
  MIN_AMOUNT: 100
  MAX_AMOUNT: 10000
  CHECK_INTERVAL: 5

# 风险控制
RISK:
  MAX_TOTAL_EXPOSURE: 100000
  STOP_LOSS_THRESHOLD: -0.1
  TAKE_PROFIT_THRESHOLD: 0.2

# 套保条件模板
TEMPLATES:
  PRICE_DIFFERENCE:
    BTC_USDT:
      TRIGGER_VALUE: 100.0
      AMOUNT_USDT: 2000.0
    DEFAULT:
      TRIGGER_VALUE: 50.0
      AMOUNT_USDT: 1000.0
```

### 前端配置

#### Vite 配置 (`frontend/vite.config.ts`)

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
```

## 📊 监控和日志

### 应用监控

```bash
# 查看应用状态
curl http://localhost:5000/health

# 查看 API 文档
curl http://localhost:5000/api/docs
```

### 日志配置

在 `app.py` 中配置日志：

```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

## 🔐 安全配置

### API 安全

1. **CORS 配置**: 限制跨域请求来源
2. **输入验证**: 使用 Marshmallow 进行数据验证
3. **错误处理**: 统一的错误响应格式
4. **日志记录**: 记录所有 API 请求和错误

### 前端安全

1. **XSS 防护**: React 自动转义用户输入
2. **CSRF 防护**: 使用 CSRF Token
3. **输入验证**: 使用 Zod 进行客户端验证
4. **安全头部**: 配置安全相关的 HTTP 头部

## 🚀 性能优化

### 后端优化

```python
# 使用缓存
from flask_caching import Cache
cache = Cache(app)

@cache.cached(timeout=300)
def get_market_data():
    # 缓存市场数据
    pass

# 数据库连接池
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 10,
    'pool_recycle': 120,
    'pool_pre_ping': True
}
```

### 前端优化

```typescript
// 代码分割
const OrderManagement = lazy(() => import('./pages/OrderManagement'));

// 组件缓存
const MemoizedComponent = React.memo(Component);

// 虚拟滚动（大列表）
import { FixedSizeList as List } from 'react-window';
```

## 📱 移动端适配

### 响应式设计

```css
/* Tailwind CSS 响应式类 */
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  <!-- 移动端1列，平板2列，桌面3列 -->
</div>
```

### PWA 支持

```typescript
// 添加 Service Worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js');
}
```


