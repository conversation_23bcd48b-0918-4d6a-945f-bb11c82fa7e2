import React, { useState } from 'react';
import {
  Card,
  Form,
  Button,
  Select,
  InputNumber,
  Space,
  Row,
  Col,
  message
} from 'antd';
import {
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';

const { Option } = Select;

interface SingleOrderFormData {
  exchange: string;
  symbol: string;
  side: 'buy' | 'sell';
  order_type: 'market' | 'limit';
  amount: number;
  amount_currency: 'USDT' | 'USDC';
  price?: number;
  is_spot: boolean;
}

interface SingleOrderFormProps {
  onSubmit: (data: SingleOrderFormData) => Promise<void>;
  loading?: boolean;
}

export const SingleOrderForm: React.FC<SingleOrderFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [orderType, setOrderType] = useState<'market' | 'limit'>('market');
  const [side, setSide] = useState<'buy' | 'sell'>('buy');

  const handleSubmit = async (values: any) => {
    try {
      setSubmitStatus('idle');

      const formData: SingleOrderFormData = {
        exchange: values.exchange,
        symbol: values.symbol,
        side: values.side,
        order_type: values.order_type,
        amount: values.amount,
        amount_currency: values.amount_currency,
        price: values.price,
        is_spot: values.is_spot !== false
      };

      await onSubmit(formData);
      setSubmitStatus('success');
      message.success('订单提交成功！');

      // 3秒后重置状态
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } catch (error) {
      setSubmitStatus('error');
      message.error(error instanceof Error ? error.message : '提交失败');
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          side: 'buy',
          order_type: 'market',
          amount_currency: 'USDT'
        }}
        onValuesChange={(changedValues) => {
          if (changedValues.order_type) {
            setOrderType(changedValues.order_type);
          }
          if (changedValues.side) {
            setSide(changedValues.side);
          }
        }}
      >
        <Row gutter={24}>
          {/* 左侧：基础设置 */}
          <Col span={12}>
            <Card title="基础设置" size="small" style={{ height: '100%' }}>
              {/* 交易所和交易对 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="交易所"
                    name="exchange"
                    rules={[{ required: true, message: '请选择交易所' }]}
                    style={{ marginBottom: 12 }}
                  >
                    <Select placeholder="选择交易所" size="small">
                      <Option value="binance">BINANCE</Option>
                      <Option value="okx">OKX</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="交易对"
                    name="symbol"
                    rules={[{ required: true, message: '请选择交易对' }]}
                    style={{ marginBottom: 12 }}
                  >
                    <Select placeholder="选择交易对" size="small">
                      <Option value="BTC/USDT">BTC/USDT</Option>
                      <Option value="ETH/USDT">ETH/USDT</Option>
                      <Option value="BNB/USDT">BNB/USDT</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* 交易类型 */}
              <Form.Item
                label="交易类型"
                name="is_spot"
                rules={[{ required: true, message: '请选择交易类型' }]}
                style={{ marginBottom: 12 }}
              >
                <Select placeholder="选择交易类型" size="small">
                  <Option value={true}>现货交易</Option>
                  <Option value={false}>合约交易</Option>
                </Select>
              </Form.Item>

              {/* 买卖方向和订单类型 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="买卖方向"
                    name="side"
                    rules={[{ required: true, message: '请选择买卖方向' }]}
                    style={{ marginBottom: 12 }}
                  >
                    <Select size="small">
                      <Option value="buy">
                        <Space>
                          <RiseOutlined style={{ color: '#52c41a' }} />
                          买入
                        </Space>
                      </Option>
                      <Option value="sell">
                        <Space>
                          <FallOutlined style={{ color: '#ff4d4f' }} />
                          卖出
                        </Space>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="订单类型"
                    name="order_type"
                    rules={[{ required: true, message: '请选择订单类型' }]}
                    style={{ marginBottom: 12 }}
                  >
                    <Select size="small">
                      <Option value="market">市价单</Option>
                      <Option value="limit">限价单</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* 金额和价格 */}
              <Row gutter={16}>
                <Col span={orderType === 'limit' ? 12 : 24}>
                  <Form.Item
                    label="交易金额"
                    name="amount"
                    rules={[
                      { required: true, message: '请输入交易金额' },
                      { type: 'number', min: 1, message: '交易金额必须大于1' }
                    ]}
                    style={{ marginBottom: 12 }}
                  >
                    <InputNumber
                      addonAfter={
                        <Form.Item name="amount_currency" noStyle>
                          <Select style={{ width: 80 }} size="small">
                            <Option value="USDT">USDT</Option>
                            <Option value="USDC">USDC</Option>
                          </Select>
                        </Form.Item>
                      }
                      style={{ width: '100%' }}
                      size="small"
                      min={1}
                      step={1}
                      placeholder="输入交易金额"
                      precision={2}
                    />
                  </Form.Item>
                </Col>
                {orderType === 'limit' && (
                  <Col span={12}>
                    <Form.Item
                      label="价格"
                      name="price"
                      rules={[
                        { required: true, message: '限价单必须设置价格' },
                        { type: 'number', min: 0.01, message: '价格必须大于0' }
                      ]}
                      style={{ marginBottom: 12 }}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        size="small"
                        min={0.01}
                        step={0.01}
                        placeholder="输入限价"
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>

              {/* 提交按钮 */}
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Space>
                  <Button size="small" onClick={() => form.resetFields()}>
                    重置
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    htmlType="submit"
                    loading={loading}
                    disabled={submitStatus === 'success'}
                    style={{
                      backgroundColor: side === 'buy' ? '#52c41a' : '#ff4d4f',
                      borderColor: side === 'buy' ? '#52c41a' : '#ff4d4f'
                    }}
                  >
                    {side === 'buy' ? <RiseOutlined /> : <FallOutlined />}
                    {submitStatus === 'success' ? '已提交' : `${side === 'buy' ? '买入' : '卖出'}订单`}
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>

          {/* 右侧：订单预览 */}
          <Col span={12}>
            <Card title="订单预览" size="small" style={{ height: '100%' }}>
              <Form.Item dependencies={['exchange', 'symbol', 'is_spot', 'side', 'order_type', 'amount', 'amount_currency', 'price']} noStyle>
                {({ getFieldValue }) => {
                  const currentExchange = getFieldValue('exchange');
                  const currentSymbol = getFieldValue('symbol');
                  const currentIsSpot = getFieldValue('is_spot');
                  const currentSide = getFieldValue('side') || side;
                  const currentOrderType = getFieldValue('order_type') || orderType;
                  const currentAmount = getFieldValue('amount');
                  const currentAmountCurrency = getFieldValue('amount_currency');
                  const currentPrice = getFieldValue('price');

                  return (
                    <div style={{ backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '6px' }}>
                      <Row gutter={[16, 12]}>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>交易所</div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                            {currentExchange?.toUpperCase() || '-'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>交易对</div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                            {currentSymbol || '-'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>类型</div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                            {currentIsSpot === true ? '现货' : currentIsSpot === false ? '合约' : '-'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>方向</div>
                          <div style={{
                            fontWeight: 'bold',
                            fontSize: '14px',
                            color: currentSide === 'buy' ? '#52c41a' : '#ff4d4f',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}>
                            {currentSide === 'buy' ? <RiseOutlined /> : <FallOutlined />}
                            {currentSide === 'buy' ? '买入' : currentSide === 'sell' ? '卖出' : '-'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>订单类型</div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                            {currentOrderType === 'market' ? '市价单' : currentOrderType === 'limit' ? '限价单' : '-'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ fontSize: '12px', color: '#666' }}>交易金额</div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                            {(currentAmount && currentAmountCurrency) ? `${currentAmount} ${currentAmountCurrency}` : '-'}
                          </div>
                        </Col>
                        {currentOrderType === 'limit' && (
                          <>
                            <Col span={12}>
                              <div style={{ fontSize: '12px', color: '#666' }}>限价</div>
                              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                                {currentPrice || '-'}
                              </div>
                            </Col>
                            <Col span={12}>
                              <div style={{ fontSize: '12px', color: '#666' }}>预计数量</div>
                              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                                {(currentAmount && currentPrice)
                                  ? (currentAmount / currentPrice).toFixed(6)
                                  : '-'
                                }
                              </div>
                            </Col>
                          </>
                        )}
                      </Row>
                    </div>
                  );
                }}
              </Form.Item>

              {/* 风险提示 */}
              <Form.Item dependencies={['order_type', 'side']} noStyle>
                {({ getFieldValue }) => {
                  const currentOrderType = getFieldValue('order_type') || orderType;
                  const currentSide = getFieldValue('side') || side;

                  return (
                    <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#fff7e6', borderRadius: '6px', border: '1px solid #ffd591' }}>
                      <div style={{ fontSize: '12px', color: '#d46b08', fontWeight: 'bold', marginBottom: '4px' }}>
                        ⚠️ 风险提示
                      </div>
                      <div style={{ fontSize: '11px', color: '#d46b08' }}>
                        • {currentOrderType === 'market' ? '市价单将立即执行，请确认数量无误' : '限价单将在价格达到设定值时执行'}
                        <br />
                        • 请确保账户有足够余额进行交易
                        <br />
                        • {currentSide === 'buy' ? '买入' : currentSide === 'sell' ? '卖出' : '交易'}操作不可撤销，请谨慎操作
                      </div>
                    </div>
                  );
                }}
              </Form.Item>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
