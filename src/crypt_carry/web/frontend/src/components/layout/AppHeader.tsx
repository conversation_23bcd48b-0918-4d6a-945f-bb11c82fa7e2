import React from 'react';
import {Layout, Menu} from 'antd';
import {Link, useLocation} from 'react-router-dom';
import {HomeOutlined, LineChartOutlined, WalletOutlined, ShoppingCartOutlined} from '@ant-design/icons';

const {Header} = Layout;

/**
 * 应用页头组件
 * 包含导航菜单和Logo
 */
const AppHeader: React.FC = () => {
    // 获取当前路径，用于高亮当前菜单项
    const location = useLocation();

    // 菜单项定义
    const menuItems = [
        {
            key: '/',
            icon: <HomeOutlined/>,
            label: <Link to="/">首页</Link>,
        },
        {
            key: '/hedging',
            icon: <LineChartOutlined/>,
            label: <Link to="/hedging">套保策略</Link>,
        },
        {
            key: '/positions',
            icon: <WalletOutlined/>,
            label: <Link to="/positions">持仓管理</Link>,
        },
        {
            key: '/orders',
            icon: <ShoppingCartOutlined/>,
            label: <Link to="/orders">订单管理</Link>,
        },
    ];

    return (
        <Header className="site-header">
            <div className="logo">
                <h1>Crypt Carry</h1>
            </div>
            <Menu
                theme="dark"
                mode="horizontal"
                selectedKeys={[location.pathname]}
                items={menuItems}
            />
        </Header>
    );
};

export default AppHeader;
