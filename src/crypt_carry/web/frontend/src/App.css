.app-layout {
    min-height: 100vh;
}

.site-header {
    position: sticky;
    top: 0;
    z-index: 1;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 24px;
}

.logo {
    height: 32px;
    margin-right: 16px;
    display: flex;
    align-items: center;
}

.logo h1 {
    margin: 0;
    color: white;
    font-size: 18px;
    margin-left: 8px;
}

.site-footer {
    text-align: center;
    padding: 24px 50px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    background: #f0f2f5;
}

.site-content {
    padding: 24px;
    background: #f0f2f5;
    min-height: calc(100vh - 64px - 69px);
}

.ant-pro-card {
    margin-bottom: 24px;
}

/* 表格响应式优化 */
.ant-table-wrapper {
    overflow-x: auto;
}

.ant-table-thead > tr > th {
    white-space: nowrap;
}

.ant-table-tbody > tr > td {
    white-space: nowrap;
}

/* 按钮组优化 */
.ant-space-small {
    gap: 4px !important;
}

.ant-btn-sm {
    padding: 0 8px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
}

/* 操作列优化 */
.ant-table-column-title {
    white-space: nowrap;
}

/* 适配移动设备 */
@media (max-width: 768px) {
    .site-content {
        padding: 12px;
    }

    .ant-table-wrapper {
        font-size: 12px;
    }

    .ant-btn-sm {
        font-size: 11px;
        padding: 0 6px;
        height: 22px;
        line-height: 20px;
    }

    .ant-space-small {
        gap: 2px !important;
    }
}

@media (max-width: 576px) {
    .site-header {
        padding: 0 12px;
    }

    .site-content {
        padding: 8px;
    }

    .ant-card {
        margin-bottom: 16px;
    }
}
