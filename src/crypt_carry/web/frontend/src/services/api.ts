import axios, {AxiosResponse} from 'axios';
import {message} from 'antd';
import {
    Exchange, Symbol, MarketInfo, FundingRate,
    Balance, Position, HedgingStrategy, HedgingCondition
} from '../types';
import { API_CONFIG, API_ENDPOINTS } from '../config/api';

/**
 * 创建axios实例，设置基础配置
 */
const api = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: API_CONFIG.HEADERS,
});

/**
 * 请求拦截器
 * 可以在这里处理认证信息等
 */
api.interceptors.request.use(
    (config) => {
        // 这里可以添加认证令牌等逻辑
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

/**
 * 响应拦截器
 * 统一处理响应错误和格式化
 */
api.interceptors.response.use(
    (response: AxiosResponse) => {
        return response.data;
    },
    (error) => {
        // 错误处理逻辑
        const errorMessage = error.response?.data?.message || error.message || '请求失败';
        message.error(errorMessage);
        return Promise.reject(error);
    }
);

/**
 * API服务类
 * 封装所有与后端的通信方法
 */
export class ApiService {
    /**
     * 获取所有支持的交易所
     */
    static async getExchanges(): Promise<Exchange[]> {
        try {
            return await api.get('/exchanges');
        } catch (error) {
            console.error('获取交易所列表失败:', error);
            return [];
        }
    }

    /**
     * 获取交易所支持的交易对
     * @param exchangeId 交易所ID
     */
    static async getSymbols(exchangeId: string): Promise<Symbol[]> {
        try {
            return await api.get(`/exchanges/${exchangeId}/symbols`);
        } catch (error) {
            console.error(`获取${exchangeId}交易对失败:`, error);
            return [];
        }
    }

    /**
     * 获取市场信息
     * @param exchangeId 交易所ID
     * @param symbol 交易对
     */
    static async getMarketInfo(exchangeId: string, symbol: string): Promise<MarketInfo | null> {
        try {
            return await api.get(`/exchanges/${exchangeId}/markets/${symbol}`);
        } catch (error) {
            console.error(`获取市场信息失败:`, error);
            return null;
        }
    }

    /**
     * 获取合约资金费率
     * @param exchangeId 交易所ID
     * @param symbol 交易对
     */
    static async getFundingRate(exchangeId: string, symbol: string): Promise<FundingRate | null> {
        try {
            return await api.get(`/exchanges/${exchangeId}/funding_rate/${symbol}`);
        } catch (error) {
            console.error(`获取资金费率失败:`, error);
            return null;
        }
    }

    /**
     * 获取所有合约资金费率
     * @param exchangeId 交易所ID
     */
    static async getAllFundingRates(exchangeId: string): Promise<FundingRate[]> {
        try {
            return await api.get(`/exchanges/${exchangeId}/funding_rates`);
        } catch (error) {
            console.error(`获取所有资金费率失败:`, error);
            return [];
        }
    }

    /**
     * 获取账户余额
     * @param exchangeId 交易所ID
     * @param type 账户类型(spot/futures)
     */
    static async getBalance(exchangeId: string, type: 'spot' | 'futures'): Promise<Balance | null> {
        try {
            return await api.get(`/exchanges/${exchangeId}/balance/${type}`);
        } catch (error) {
            console.error(`获取账户余额失败:`, error);
            return null;
        }
    }

    /**
     * 获取所有套保策略
     */
    static async getHedgingStrategies(): Promise<HedgingStrategy[]> {
        try {
            return await api.get('/hedging/strategies');
        } catch (error) {
            console.error('获取套保策略失败:', error);
            return [];
        }
    }

    /**
     * 创建套保策略
     * @param strategy 策略数据
     */
    static async createHedgingStrategy(strategy: Omit<HedgingStrategy, 'id'>): Promise<HedgingStrategy | null> {
        try {
            return await api.post('/hedging/strategies', strategy);
        } catch (error) {
            console.error('创建套保策略失败:', error);
            return null;
        }
    }

    /**
     * 更新套保策略
     * @param id 策略ID
     * @param strategy 策略数据
     */
    static async updateHedgingStrategy(id: number, strategy: Partial<HedgingStrategy>): Promise<HedgingStrategy | null> {
        try {
            return await api.put(`/hedging/strategies/${id}`, strategy);
        } catch (error) {
            console.error('更新套保策略失败:', error);
            return null;
        }
    }

    /**
     * 删除套保策略
     * @param id 策略ID
     */
    static async deleteHedgingStrategy(id: number): Promise<boolean> {
        try {
            await api.delete(`/hedging/strategies/${id}`);
            return true;
        } catch (error) {
            console.error('删除套保策略失败:', error);
            return false;
        }
    }

    /**
     * 创建套保条件
     * @param strategyId 策略ID
     * @param condition 条件数据
     */
    static async createHedgingCondition(
        strategyId: number,
        condition: Omit<HedgingCondition, 'id'>
    ): Promise<HedgingCondition | null> {
        try {
            return await api.post(`/hedging/strategies/${strategyId}/conditions`, condition);
        } catch (error) {
            console.error('创建套保条件失败:', error);
            return null;
        }
    }

    /**
     * 更新套保条件
     * @param strategyId 策略ID
     * @param conditionId 条件ID
     * @param condition 条件数据
     */
    static async updateHedgingCondition(
        strategyId: number,
        conditionId: number,
        condition: Partial<HedgingCondition>
    ): Promise<HedgingCondition | null> {
        try {
            return await api.put(
                `/hedging/strategies/${strategyId}/conditions/${conditionId}`,
                condition
            );
        } catch (error) {
            console.error('更新套保条件失败:', error);
            return null;
        }
    }

    /**
     * 删除套保条件
     * @param strategyId 策略ID
     * @param conditionId 条件ID
     */
    static async deleteHedgingCondition(strategyId: number, conditionId: number): Promise<boolean> {
        try {
            await api.delete(`/hedging/strategies/${strategyId}/conditions/${conditionId}`);
            return true;
        } catch (error) {
            console.error('删除套保条件失败:', error);
            return false;
        }
    }

    /**
     * 获取所有持仓
     */
    static async getPositions(): Promise<Position[]> {
        try {
            return await api.get('/hedging/positions');
        } catch (error) {
            console.error('获取持仓数据失败:', error);
            return [];
        }
    }

    /**
     * 平仓操作
     * @param positionId 持仓ID
     */
    static async closePosition(positionId: number): Promise<boolean> {
        try {
            await api.post(`/hedging/positions/${positionId}/close`);
            return true;
        } catch (error) {
            console.error('平仓操作失败:', error);
            return false;
        }
    }

    /**
     * 手动创建新持仓
     * @param positionData 持仓数据
     */
    static async createPosition(
        positionData: Omit<Position, 'id' | 'status' | 'opened_at' | 'closed_at' | 'profit_loss' | 'profit_loss_percent'>
    ): Promise<Position | null> {
        try {
            return await api.post('/hedging/positions', positionData);
        } catch (error) {
            console.error('创建持仓失败:', error);
            return null;
        }
    }

    // ==================== 订单管理相关API ====================

    /**
     * 创建套保订单
     * @param orderData 套保订单数据
     */
    static async createHedgingOrder(orderData: any): Promise<any> {
        try {
            return await api.post(API_ENDPOINTS.HEDGING_ORDERS, orderData);
        } catch (error) {
            console.error('创建套保订单失败:', error);
            throw error;
        }
    }

    /**
     * 创建单独订单
     * @param orderData 单独订单数据
     */
    static async createSingleOrder(orderData: any): Promise<any> {
        try {
            return await api.post(API_ENDPOINTS.SINGLE_ORDERS, orderData);
        } catch (error) {
            console.error('创建单独订单失败:', error);
            throw error;
        }
    }

    /**
     * 获取活跃订单列表
     */
    static async getActiveOrders(): Promise<any[]> {
        try {
            const response: any = await api.get(API_ENDPOINTS.ACTIVE_ORDERS);
            return response.orders || [];
        } catch (error) {
            console.error('获取活跃订单失败:', error);
            return [];
        }
    }

    /**
     * 获取订单列表
     * @param status 可选的状态过滤
     * @param limit 限制数量
     */
    static async getOrders(status?: string, limit: number = 50): Promise<any[]> {
        try {
            const params = new URLSearchParams();
            if (status) params.append('status', status);
            params.append('limit', limit.toString());

            const response: any = await api.get(`${API_ENDPOINTS.ORDER_LIST}?${params.toString()}`);
            return response.orders || [];
        } catch (error) {
            console.error('获取订单列表失败:', error);
            return [];
        }
    }

    /**
     * 获取订单状态
     * @param orderId 订单ID
     */
    static async getOrderStatus(orderId: string): Promise<any> {
        try {
            return await api.get(API_ENDPOINTS.ORDER_STATUS(orderId));
        } catch (error) {
            console.error('获取订单状态失败:', error);
            throw error;
        }
    }

    /**
     * 取消订单
     * @param orderId 订单ID
     */
    static async cancelOrder(orderId: string): Promise<any> {
        try {
            return await api.post(API_ENDPOINTS.CANCEL_ORDER(orderId));
        } catch (error) {
            console.error('取消订单失败:', error);
            throw error;
        }
    }

    /**
     * 获取任务执行器状态
     */
    static async getExecutorStatus(): Promise<any> {
        try {
            return await api.get(API_ENDPOINTS.EXECUTOR_STATUS);
        } catch (error) {
            console.error('获取执行器状态失败:', error);
            throw error;
        }
    }

    /**
     * 强制执行任务
     * @param taskId 任务ID
     */
    static async forceExecuteTask(taskId: string): Promise<any> {
        try {
            return await api.post(API_ENDPOINTS.FORCE_EXECUTE_TASK(taskId));
        } catch (error) {
            console.error('强制执行任务失败:', error);
            throw error;
        }
    }
}

export default ApiService;
