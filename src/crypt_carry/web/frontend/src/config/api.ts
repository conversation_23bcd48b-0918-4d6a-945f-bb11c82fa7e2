/**
 * API配置文件
 * 统一管理API相关的配置信息
 */

/**
 * 获取API基础URL
 * 根据环境变量或当前环境自动确定
 */
export const getApiBaseUrl = (): string => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_BASE_URL) {
    return process.env.REACT_APP_API_BASE_URL;
  }

  // 根据当前环境自动判断
  if (process.env.NODE_ENV === 'development') {
    // 开发环境：使用本地后端服务
    return 'http://localhost:5001/api';
  } else {
    // 生产环境：使用相对路径
    return '/api';
  }
};

/**
 * API配置常量
 */
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: 30000,
  HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 健康检查
  HEALTH: '/health',
  
  // 交易所相关
  EXCHANGES: '/exchanges',
  EXCHANGE_SYMBOLS: (exchangeId: string) => `/exchanges/${exchangeId}/symbols`,
  EXCHANGE_MARKETS: (exchangeId: string, symbol: string) => `/exchanges/${exchangeId}/markets/${symbol}`,
  FUNDING_RATE: (exchangeId: string, symbol: string) => `/exchanges/${exchangeId}/funding_rate/${symbol}`,
  ALL_FUNDING_RATES: (exchangeId: string) => `/exchanges/${exchangeId}/funding_rates`,
  BALANCE: (exchangeId: string, type: string) => `/exchanges/${exchangeId}/balance/${type}`,
  
  // 套保策略相关
  HEDGING_STRATEGIES: '/hedging/strategies',
  HEDGING_STRATEGY: (id: number) => `/hedging/strategies/${id}`,
  HEDGING_CONDITIONS: (strategyId: number) => `/hedging/strategies/${strategyId}/conditions`,
  HEDGING_CONDITION: (strategyId: number, conditionId: number) => 
    `/hedging/strategies/${strategyId}/conditions/${conditionId}`,
  
  // 持仓相关
  POSITIONS: '/hedging/positions',
  POSITION: (id: number) => `/hedging/positions/${id}`,
  CLOSE_POSITION: (id: number) => `/hedging/positions/${id}/close`,
  
  // 订单管理相关
  HEDGING_ORDERS: '/hedging/orders/hedging',
  SINGLE_ORDERS: '/hedging/orders/single',
  ACTIVE_ORDERS: '/hedging/orders/active',
  ORDER_LIST: '/hedging/orders/list',
  ORDER_STATUS: (orderId: string) => `/hedging/orders/${orderId}/status`,
  CANCEL_ORDER: (orderId: string) => `/hedging/orders/${orderId}/cancel`,
  
  // 任务执行器相关
  EXECUTOR_STATUS: '/hedging/executor/status',
  FORCE_EXECUTE_TASK: (taskId: string) => `/hedging/tasks/${taskId}/execute`,
} as const;

/**
 * 环境配置
 */
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  apiBaseUrl: getApiBaseUrl(),
} as const;

export default API_CONFIG;
