/**
 * 类型定义文件
 * 定义系统中使用的主要数据类型
 */

// 交易所类型
export interface Exchange {
    id: string;
    name: string;
    spot_enabled: boolean;
    futures_enabled: boolean;
}

// 交易对类型
export interface Symbol {
    symbol: string;
    base: string;
    quote: string;
    settle?: string;
}

// 市场信息类型
export interface MarketInfo {
    symbol: string;
    price_precision: number;
    amount_precision: number;
    min_amount: number;
    min_cost: number;
    maker_fee: number;
    taker_fee: number;
    last_price: number;
}

// 资金费率类型
export interface FundingRate {
    symbol: string;
    rate: number;
    next_funding_time?: number;
    estimated_rate?: number;
}

// 账户余额类型
export interface Balance {
    [currency: string]: {
        free: number;
        used: number;
        total: number;
    }
}

// 合约账户类型
export interface FuturesBalance {
    USDT: {
        free: number;
        used: number;
        total: number;
    };
    unrealizedPnL: number;
    marginBalance: number;
    maintenanceMargin: number;
    initialMargin: number;
    availableBalance: number;
}

// 持仓类型
export interface Position {
    id: number;
    strategy_id: number;

    // 多头信息
    long_exchange: string;
    long_symbol: string;
    long_order_id: string;
    long_amount: number;
    long_price: number;

    // 空头信息
    short_exchange: string;
    short_symbol: string;
    short_order_id: string;
    short_amount: number;
    short_price: number;

    // 持仓状态
    status: 'open' | 'closed' | 'partially_closed';
    opened_at: string;
    closed_at?: string;

    // 盈亏信息
    profit_loss?: number;
    profit_loss_percent?: number;
}

// 套保条件类型
export interface HedgingCondition {
    id?: number;
    condition_type: 'funding_rate' | 'price_difference' | 'basis_rate';

    // 多头条件
    long_exchange: string;
    long_symbol: string;
    long_amount: number;
    long_is_spot: boolean;

    // 空头条件
    short_exchange: string;
    short_symbol: string;
    short_amount: number;
    short_is_spot: boolean;

    // 触发条件
    trigger_value: number;
    comparison_operator: '>' | '<' | '>=' | '<=' | '==';
    is_active: boolean;
    priority: number;
}

// 套保策略类型
export interface HedgingStrategy {
    id?: number;
    name: string;
    description?: string;
    is_active: boolean;
    created_at?: string;
    updated_at?: string;

    // 策略配置
    check_interval?: number;
    auto_close?: boolean;
    max_open_positions?: number;

    // 关联数据
    positions?: Position[];
    conditions: HedgingCondition[];
}
