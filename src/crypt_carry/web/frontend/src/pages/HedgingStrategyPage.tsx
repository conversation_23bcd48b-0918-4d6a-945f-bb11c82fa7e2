import React, {useState, useEffect} from 'react';
import {
    Card, Table, Button, Modal, Form, Input, InputNumber,
    Select, Switch, Space, Typography, message, Tabs
} from 'antd';
import {PlusOutlined, EditOutlined, DeleteOutlined} from '@ant-design/icons';
import {HedgingStrategy, HedgingCondition, Exchange, Symbol} from '../types';

const {Title} = Typography;
const {TabPane} = Tabs;
const {Option} = Select;

/**
 * 套保策略页面
 * 用于管理跨交易所的套保策略和条件
 */
const HedgingStrategyPage: React.FC = () => {
    // 状态定义
    const [strategies, setStrategies] = useState<HedgingStrategy[]>([]);
    const [exchanges, setExchanges] = useState<Exchange[]>([]);
    const [symbols, setSymbols] = useState<Symbol[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    const [conditionModalVisible, setConditionModalVisible] = useState<boolean>(false);
    const [currentStrategy, setCurrentStrategy] = useState<HedgingStrategy | null>(null);
    const [currentCondition, setCurrentCondition] = useState<HedgingCondition | null>(null);
    const [strategyForm] = Form.useForm();
    const [conditionForm] = Form.useForm();

    // 初始化加载数据
    useEffect(() => {
        fetchExchanges();
        fetchSymbols();
        fetchStrategies();
    }, []);

    // 模拟获取交易所数据
    const fetchExchanges = async () => {
        // 实际项目中应该调用API获取数据
        setExchanges([
            {id: 'binance', name: '币安', spot_enabled: true, futures_enabled: true},
            {id: 'okx', name: 'OKX', spot_enabled: true, futures_enabled: true},
        ]);
    };

    // 模拟获取交易对数据
    const fetchSymbols = async () => {
        // 实际项目中应该调用API获取数据
        setSymbols([
            {symbol: 'BTC/USDT', base: 'BTC', quote: 'USDT'},
            {symbol: 'ETH/USDT', base: 'ETH', quote: 'USDT'},
            {symbol: 'SOL/USDT', base: 'SOL', quote: 'USDT'},
        ]);
    };

    // 模拟获取策略数据
    const fetchStrategies = async () => {
        setLoading(true);
        try {
            // 实际项目中应该调用API获取数据
            setStrategies([
                {
                    id: 1,
                    name: 'BTC资金费率套保',
                    description: '基于比特币资金费率的多空套保策略',
                    is_active: true,
                    created_at: '2023-10-01T00:00:00Z',
                    check_interval: 300,
                    auto_close: true,
                    max_open_positions: 2,
                    conditions: [
                        {
                            id: 1,
                            condition_type: 'funding_rate',
                            long_exchange: 'binance',
                            long_symbol: 'BTC/USDT',
                            long_amount: 0.01,
                            long_is_spot: true,
                            short_exchange: 'okx',
                            short_symbol: 'BTC/USDT',
                            short_amount: 0.01,
                            short_is_spot: false,
                            trigger_value: 0.001,
                            comparison_operator: '>',
                            is_active: true,
                            priority: 1
                        }
                    ]
                }
            ]);
        } catch (error) {
            message.error('获取套保策略失败');
            console.error('获取套保策略失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 打开创建策略弹窗
    const showCreateStrategyModal = () => {
        setCurrentStrategy(null);
        strategyForm.resetFields();
        setModalVisible(true);
    };

    // 打开编辑策略弹窗
    const showEditStrategyModal = (strategy: HedgingStrategy) => {
        setCurrentStrategy(strategy);
        strategyForm.setFieldsValue({
            name: strategy.name,
            description: strategy.description,
            is_active: strategy.is_active,
            check_interval: strategy.check_interval,
            auto_close: strategy.auto_close,
            max_open_positions: strategy.max_open_positions,
        });
        setModalVisible(true);
    };

    // 打开创建条件弹窗
    const showCreateConditionModal = (strategyId: number) => {
        const strategy = strategies.find(s => s.id === strategyId);
        if (strategy) {
            setCurrentStrategy(strategy);
            setCurrentCondition(null);
            conditionForm.resetFields();
            setConditionModalVisible(true);
        }
    };

    // 打开编辑条件弹窗
    const showEditConditionModal = (condition: HedgingCondition, strategyId: number) => {
        const strategy = strategies.find(s => s.id === strategyId);
        if (strategy) {
            setCurrentStrategy(strategy);
            setCurrentCondition(condition);
            conditionForm.setFieldsValue({
                condition_type: condition.condition_type,
                long_exchange: condition.long_exchange,
                long_symbol: condition.long_symbol,
                long_amount: condition.long_amount,
                long_is_spot: condition.long_is_spot,
                short_exchange: condition.short_exchange,
                short_symbol: condition.short_symbol,
                short_amount: condition.short_amount,
                short_is_spot: condition.short_is_spot,
                trigger_value: condition.trigger_value,
                comparison_operator: condition.comparison_operator,
                is_active: condition.is_active,
                priority: condition.priority,
            });
            setConditionModalVisible(true);
        }
    };

    // 处理保存策略
    const handleSaveStrategy = async () => {
        try {
            const values = await strategyForm.validateFields();

            if (currentStrategy) {
                // 更新现有策略
                const updatedStrategies = strategies.map(s =>
                    s.id === currentStrategy.id
                        ? {...s, ...values, updated_at: new Date().toISOString()}
                        : s
                );
                setStrategies(updatedStrategies);
                message.success('策略更新成功');
            } else {
                // 创建新策略
                const newStrategy: HedgingStrategy = {
                    id: Date.now(), // 临时ID，实际应由后端生成
                    ...values,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    conditions: [],
                };
                setStrategies([...strategies, newStrategy]);
                message.success('策略创建成功');
            }

            setModalVisible(false);
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    // 处理保存条件
    const handleSaveCondition = async () => {
        if (!currentStrategy) return;

        try {
            const values = await conditionForm.validateFields();

            if (currentCondition) {
                // 更新现有条件
                const updatedStrategies = strategies.map(strategy => {
                    if (strategy.id === currentStrategy.id) {
                        const updatedConditions = strategy.conditions.map(condition =>
                            condition.id === currentCondition.id
                                ? {...condition, ...values}
                                : condition
                        );
                        return {...strategy, conditions: updatedConditions};
                    }
                    return strategy;
                });
                setStrategies(updatedStrategies);
                message.success('条件更新成功');
            } else {
                // 创建新条件
                const newCondition: HedgingCondition = {
                    id: Date.now(), // 临时ID，实际应由后端生成
                    ...values,
                };

                const updatedStrategies = strategies.map(strategy => {
                    if (strategy.id === currentStrategy.id) {
                        return {
                            ...strategy,
                            conditions: [...strategy.conditions, newCondition]
                        };
                    }
                    return strategy;
                });

                setStrategies(updatedStrategies);
                message.success('条件创建成功');
            }

            setConditionModalVisible(false);
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    // 删除策略
    const handleDeleteStrategy = (strategyId: number) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这个套保策略吗？删除后无法恢复。',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                const updatedStrategies = strategies.filter(s => s.id !== strategyId);
                setStrategies(updatedStrategies);
                message.success('策略已删除');
            }
        });
    };

    // 删除条件
    const handleDeleteCondition = (conditionId: number, strategyId: number) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除这个套保条件吗？删除后无法恢复。',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                const updatedStrategies = strategies.map(strategy => {
                    if (strategy.id === strategyId) {
                        return {
                            ...strategy,
                            conditions: strategy.conditions.filter(c => c.id !== conditionId)
                        };
                    }
                    return strategy;
                });
                setStrategies(updatedStrategies);
                message.success('条件已删除');
            }
        });
    };

    // 切换策略激活状态
    const toggleStrategyActive = (strategy: HedgingStrategy) => {
        const updatedStrategies = strategies.map(s =>
            s.id === strategy.id
                ? {...s, is_active: !s.is_active}
                : s
        );
        setStrategies(updatedStrategies);
        message.success(`策略已${!strategy.is_active ? '激活' : '停用'}`);
    };

    // 策略表格列定义
    const strategyColumns = [
        {
            title: '策略名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
        },
        {
            title: '条件数量',
            key: 'conditionCount',
            render: (text: string, record: HedgingStrategy) => record.conditions.length,
        },
        {
            title: '检查间隔(秒)',
            dataIndex: 'check_interval',
            key: 'check_interval',
        },
        {
            title: '最大持仓数',
            dataIndex: 'max_open_positions',
            key: 'max_open_positions',
        },
        {
            title: '自动平仓',
            key: 'auto_close',
            render: (text: string, record: HedgingStrategy) => (
                record.auto_close ? '是' : '否'
            ),
        },
        {
            title: '状态',
            key: 'status',
            render: (text: string, record: HedgingStrategy) => (
                <Switch
                    checked={record.is_active}
                    onChange={() => toggleStrategyActive(record)}
                    checkedChildren="激活"
                    unCheckedChildren="停用"
                />
            ),
        },
        {
            title: '操作',
            key: 'action',
            width: 280,
            render: (text: string, record: HedgingStrategy) => (
                <Space size="small">
                    <Button
                        type="primary"
                        size="small"
                        icon={<PlusOutlined/>}
                        onClick={() => showCreateConditionModal(record.id!)}
                    >
                        添加条件
                    </Button>
                    <Button
                        size="small"
                        icon={<EditOutlined/>}
                        onClick={() => showEditStrategyModal(record)}
                    >
                        编辑
                    </Button>
                    <Button
                        danger
                        size="small"
                        icon={<DeleteOutlined/>}
                        onClick={() => handleDeleteStrategy(record.id!)}
                    >
                        删除
                    </Button>
                </Space>
            ),
        },
    ];

    // 条件表格列定义
    const conditionColumns = [
        {
            title: '条件类型',
            dataIndex: 'condition_type',
            key: 'condition_type',
            render: (text: string) => {
                const typeMap: Record<string, string> = {
                    'funding_rate': '资金费率',
                    'price_difference': '价格差异',
                    'basis_rate': '基差率'
                };
                return typeMap[text] || text;
            }
        },
        {
            title: '多头交易所',
            dataIndex: 'long_exchange',
            key: 'long_exchange',
        },
        {
            title: '多头交易对',
            dataIndex: 'long_symbol',
            key: 'long_symbol',
        },
        {
            title: '多头类型',
            key: 'long_type',
            render: (text: string, record: HedgingCondition) => (
                record.long_is_spot ? '现货' : '合约'
            ),
        },
        {
            title: '空头交易所',
            dataIndex: 'short_exchange',
            key: 'short_exchange',
        },
        {
            title: '空头交易对',
            dataIndex: 'short_symbol',
            key: 'short_symbol',
        },
        {
            title: '空头类型',
            key: 'short_type',
            render: (text: string, record: HedgingCondition) => (
                record.short_is_spot ? '现货' : '合约'
            ),
        },
        {
            title: '触发条件',
            key: 'trigger_condition',
            render: (text: string, record: HedgingCondition) => (
                `${record.comparison_operator} ${record.trigger_value}`
            ),
        },
        {
            title: '状态',
            key: 'status',
            render: (text: string, record: HedgingCondition) => (
                record.is_active ? '激活' : '停用'
            ),
        },

    ];

    // 策略详情展开渲染
    const expandedRowRender = (strategy: HedgingStrategy) => {
        // 为当前策略创建专用的条件列定义
        const strategyConditionColumns = [
            ...conditionColumns.slice(0, -1), // 除了最后一个操作列
            {
                title: '操作',
                key: 'action',
                width: 150,
                render: (text: string, record: HedgingCondition) => (
                    <Space size="small">
                        <Button
                            size="small"
                            icon={<EditOutlined/>}
                            onClick={() => showEditConditionModal(record, strategy.id!)}
                        >
                            编辑
                        </Button>
                        <Button
                            danger
                            size="small"
                            icon={<DeleteOutlined/>}
                            onClick={() => handleDeleteCondition(record.id!, strategy.id!)}
                        >
                            删除
                        </Button>
                    </Space>
                ),
            },
        ];

        return (
            <Table
                columns={strategyConditionColumns}
                dataSource={strategy.conditions}
                pagination={false}
                rowKey="id"
                scroll={{x: 1000}}
            />
        );
    };

    return (
        <div>
            <Title level={2}>套保策略管理</Title>

            <Card>
                <div style={{marginBottom: 16}}>
                    <Button
                        type="primary"
                        icon={<PlusOutlined/>}
                        onClick={showCreateStrategyModal}
                    >
                        新建策略
                    </Button>
                </div>

                <Table
                    columns={strategyColumns}
                    expandable={{
                        expandedRowRender,
                        expandRowByClick: true,
                    }}
                    dataSource={strategies}
                    rowKey="id"
                    loading={loading}
                    scroll={{x: 1200}}
                />
            </Card>

            {/* 策略表单弹窗 */}
            <Modal
                title={currentStrategy ? '编辑套保策略' : '新建套保策略'}
                open={modalVisible}
                onOk={handleSaveStrategy}
                onCancel={() => setModalVisible(false)}
                width={600}
                okText="保存"
                cancelText="取消"
            >
                <Form
                    form={strategyForm}
                    layout="vertical"
                >
                    <Form.Item
                        name="name"
                        label="策略名称"
                        rules={[{required: true, message: '请输入策略名称'}]}
                    >
                        <Input placeholder="输入策略名称"/>
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label="策略描述"
                    >
                        <Input.TextArea rows={3} placeholder="输入策略描述（可选）"/>
                    </Form.Item>

                    <Form.Item
                        name="check_interval"
                        label="检查间隔（秒）"
                        rules={[{required: true, message: '请输入检查间隔'}]}
                        initialValue={300}
                    >
                        <InputNumber min={10} max={3600} style={{width: '100%'}}/>
                    </Form.Item>

                    <Form.Item
                        name="max_open_positions"
                        label="最大持仓数"
                        rules={[{required: true, message: '请输入最大持仓数'}]}
                        initialValue={5}
                    >
                        <InputNumber min={1} max={50} style={{width: '100%'}}/>
                    </Form.Item>

                    <Form.Item
                        name="auto_close"
                        label="自动平仓"
                        valuePropName="checked"
                        initialValue={true}
                    >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭"/>
                    </Form.Item>

                    <Form.Item
                        name="is_active"
                        label="策略状态"
                        valuePropName="checked"
                        initialValue={true}
                    >
                        <Switch checkedChildren="激活" unCheckedChildren="停用"/>
                    </Form.Item>
                </Form>
            </Modal>

            {/* 条件表单弹窗 */}
            <Modal
                title={currentCondition ? '编辑套保条件' : '新建套保条件'}
                open={conditionModalVisible}
                onOk={handleSaveCondition}
                onCancel={() => setConditionModalVisible(false)}
                width={700}
                okText="保存"
                cancelText="取消"
            >
                <Form
                    form={conditionForm}
                    layout="vertical"
                >
                    <Form.Item
                        name="condition_type"
                        label="条件类型"
                        rules={[{required: true, message: '请选择条件类型'}]}
                        initialValue="funding_rate"
                    >
                        <Select>
                            <Option value="funding_rate">资金费率</Option>
                            <Option value="price_difference">价格差异</Option>
                            <Option value="basis_rate">基差率</Option>
                        </Select>
                    </Form.Item>

                    <Tabs defaultActiveKey="1">
                        <TabPane tab="多头设置" key="1">
                            <Form.Item
                                name="long_exchange"
                                label="多头交易所"
                                rules={[{required: true, message: '请选择多头交易所'}]}
                            >
                                <Select placeholder="选择交易所">
                                    {exchanges.map(exchange => (
                                        <Option key={exchange.id} value={exchange.id}>{exchange.name}</Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="long_symbol"
                                label="多头交易对"
                                rules={[{required: true, message: '请选择多头交易对'}]}
                            >
                                <Select placeholder="选择交易对">
                                    {symbols.map(symbol => (
                                        <Option key={symbol.symbol} value={symbol.symbol}>{symbol.symbol}</Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="long_amount"
                                label="多头数量"
                                rules={[{required: true, message: '请输入多头数量'}]}
                            >
                                <InputNumber min={0.001} step={0.001} style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item
                                name="long_is_spot"
                                label="多头类型"
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Switch
                                    checkedChildren="现货"
                                    unCheckedChildren="合约"
                                />
                            </Form.Item>
                        </TabPane>

                        <TabPane tab="空头设置" key="2">
                            <Form.Item
                                name="short_exchange"
                                label="空头交易所"
                                rules={[{required: true, message: '请选择空头交易所'}]}
                            >
                                <Select placeholder="选择交易所">
                                    {exchanges.map(exchange => (
                                        <Option key={exchange.id} value={exchange.id}>{exchange.name}</Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="short_symbol"
                                label="空头交易对"
                                rules={[{required: true, message: '请选择空头交易对'}]}
                            >
                                <Select placeholder="选择交易对">
                                    {symbols.map(symbol => (
                                        <Option key={symbol.symbol} value={symbol.symbol}>{symbol.symbol}</Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="short_amount"
                                label="空头数量"
                                rules={[{required: true, message: '请输入空头数量'}]}
                            >
                                <InputNumber min={0.001} step={0.001} style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item
                                name="short_is_spot"
                                label="空头类型"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch
                                    checkedChildren="现货"
                                    unCheckedChildren="合约"
                                />
                            </Form.Item>
                        </TabPane>

                        <TabPane tab="触发条件" key="3">
                            <Form.Item
                                name="comparison_operator"
                                label="比较运算符"
                                rules={[{required: true, message: '请选择比较运算符'}]}
                                initialValue=">"
                            >
                                <Select>
                                    <Option value=">">大于 (&gt;)</Option>
                                    <Option value="<">小于 (&lt;)</Option>
                                    <Option value=">=">大于等于 (&gt;=)</Option>
                                    <Option value="<=">小于等于 (&lt;=)</Option>
                                    <Option value="==">等于 (==)</Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="trigger_value"
                                label="触发值"
                                rules={[{required: true, message: '请输入触发值'}]}
                            >
                                <InputNumber min={-1} max={1} step={0.0001} style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item
                                name="priority"
                                label="优先级"
                                rules={[{required: true, message: '请输入优先级'}]}
                                initialValue={1}
                                tooltip="数字越小优先级越高"
                            >
                                <InputNumber min={1} max={100} style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item
                                name="is_active"
                                label="条件状态"
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Switch checkedChildren="激活" unCheckedChildren="停用"/>
                            </Form.Item>
                        </TabPane>
                    </Tabs>
                </Form>
            </Modal>
        </div>
    );
};

export default HedgingStrategyPage;
