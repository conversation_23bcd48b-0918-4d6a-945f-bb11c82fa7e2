import React, {useState, useEffect} from 'react';
import {
    Card, Table, Button, Modal, Badge, Space,
    Typography, Descriptions, Statistic, message, Divider
} from 'antd';
import {
    EyeOutlined, CloseOutlined, SyncOutlined,
    ArrowUpOutlined, ArrowDownOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import {Position, Exchange} from '../types';

const {Title} = Typography;
const {confirm} = Modal;

/**
 * 持仓管理页面
 * 展示和管理跨交易所套保持仓
 */
const PositionsPage: React.FC = () => {
    // 状态定义
    const [positions, setPositions] = useState<Position[]>([]);
    const [exchanges, setExchanges] = useState<Exchange[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
    const [currentPosition, setCurrentPosition] = useState<Position | null>(null);
    const [refreshing, setRefreshing] = useState<boolean>(false);

    // 初始化加载数据
    useEffect(() => {
        fetchExchanges();
        fetchPositions();
    }, []);

    // 模拟获取交易所数据
    const fetchExchanges = async () => {
        // 实际项目中应该调用API获取数据
        setExchanges([
            {id: 'binance', name: '币安', spot_enabled: true, futures_enabled: true},
            {id: 'okx', name: 'OKX', spot_enabled: true, futures_enabled: true},
        ]);
    };

    // 模拟获取持仓数据
    const fetchPositions = async () => {
        setLoading(true);
        try {
            // 实际项目中应该调用API获取数据
            setPositions([
                {
                    id: 1,
                    strategy_id: 1,
                    long_exchange: 'binance',
                    long_symbol: 'BTC/USDT',
                    long_order_id: 'bin123456',
                    long_amount: 0.01,
                    long_price: 48000,
                    short_exchange: 'okx',
                    short_symbol: 'BTC/USDT',
                    short_order_id: 'okx123456',
                    short_amount: 0.01,
                    short_price: 48100,
                    status: 'open',
                    opened_at: '2023-10-02T12:34:56Z',
                    profit_loss: 85.5,
                    profit_loss_percent: 1.75
                },
                {
                    id: 2,
                    strategy_id: 1,
                    long_exchange: 'okx',
                    long_symbol: 'ETH/USDT',
                    long_order_id: 'okx234567',
                    long_amount: 0.1,
                    long_price: 3500,
                    short_exchange: 'binance',
                    short_symbol: 'ETH/USDT',
                    short_order_id: 'bin234567',
                    short_amount: 0.1,
                    short_price: 3520,
                    status: 'open',
                    opened_at: '2023-10-03T09:12:34Z',
                    profit_loss: -12.3,
                    profit_loss_percent: -0.35
                }
            ]);
        } catch (error) {
            message.error('获取持仓数据失败');
            console.error('获取持仓数据失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 显示持仓详情
    const showPositionDetail = (position: Position) => {
        setCurrentPosition(position);
        setDetailModalVisible(true);
    };

    // 刷新持仓数据
    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await fetchPositions();
            message.success('持仓数据已刷新');
        } catch (error) {
            message.error('刷新数据失败');
        } finally {
            setRefreshing(false);
        }
    };

    // 平仓操作
    const handleClosePosition = (positionId: number) => {
        confirm({
            title: '确认平仓',
            icon: <ExclamationCircleOutlined/>,
            content: '确定要平仓该持仓吗？这将在两个交易所同时提交市价平仓订单。',
            okText: '确认平仓',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    // 实际项目中应该调用API执行平仓
                    // 模拟平仓成功
                    const updatedPositions = positions.map(p =>
                        p.id === positionId
                            ? {
                                ...p,
                                status: 'closed' as const,
                                closed_at: new Date().toISOString()
                            }
                            : p
                    );
                    setPositions(updatedPositions);
                    message.success('平仓操作已提交');
                } catch (error) {
                    message.error('平仓操作失败');
                    console.error('平仓操作失败:', error);
                }
            }
        });
    };

    // 获取交易所名称
    const getExchangeName = (exchangeId: string) => {
        const exchange = exchanges.find(e => e.id === exchangeId);
        return exchange ? exchange.name : exchangeId;
    };

    // 持仓表格列定义
    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: 60,
        },
        {
            title: '状态',
            key: 'status',
            width: 100,
            render: (text: string, record: Position) => {
                if (record.status === 'open') {
                    return <Badge status="processing" text="持仓中"/>;
                } else if (record.status === 'closed') {
                    return <Badge status="default" text="已平仓"/>;
                } else {
                    return <Badge status="warning" text="部分平仓"/>;
                }
            },
        },
        {
            title: '多头信息',
            key: 'long_info',
            render: (text: string, record: Position) => (
                <div>
                    <div><strong>交易所:</strong> {getExchangeName(record.long_exchange)}</div>
                    <div><strong>交易对:</strong> {record.long_symbol}</div>
                    <div><strong>数量:</strong> {record.long_amount}</div>
                    <div><strong>价格:</strong> {record.long_price.toFixed(2)}</div>
                </div>
            ),
        },
        {
            title: '空头信息',
            key: 'short_info',
            render: (text: string, record: Position) => (
                <div>
                    <div><strong>交易所:</strong> {getExchangeName(record.short_exchange)}</div>
                    <div><strong>交易对:</strong> {record.short_symbol}</div>
                    <div><strong>数量:</strong> {record.short_amount}</div>
                    <div><strong>价格:</strong> {record.short_price.toFixed(2)}</div>
                </div>
            ),
        },
        {
            title: '开仓时间',
            dataIndex: 'opened_at',
            key: 'opened_at',
            render: (text: string) => new Date(text).toLocaleString(),
        },
        {
            title: '盈亏',
            key: 'profit_loss',
            render: (text: string, record: Position) => {
                const isProfit = (record.profit_loss || 0) >= 0;
                return (
                    <Statistic
                        value={record.profit_loss || 0}
                        precision={2}
                        valueStyle={{color: isProfit ? '#3f8600' : '#cf1322'}}
                        prefix={isProfit ? <ArrowUpOutlined/> : <ArrowDownOutlined/>}
                        suffix="USDT"
                    />
                );
            },
        },
        {
            title: '盈亏率',
            key: 'profit_loss_percent',
            render: (text: string, record: Position) => {
                const isProfit = (record.profit_loss_percent || 0) >= 0;
                return (
                    <Statistic
                        value={record.profit_loss_percent || 0}
                        precision={2}
                        valueStyle={{color: isProfit ? '#3f8600' : '#cf1322'}}
                        prefix={isProfit ? <ArrowUpOutlined/> : <ArrowDownOutlined/>}
                        suffix="%"
                    />
                );
            },
        },
        {
            title: '操作',
            key: 'action',
            width: 160,
            render: (text: string, record: Position) => (
                <Space>
                    <Button
                        icon={<EyeOutlined/>}
                        onClick={() => showPositionDetail(record)}
                        title="查看详情"
                    >
                        详情
                    </Button>
                    {record.status === 'open' && (
                        <Button
                            danger
                            icon={<CloseOutlined/>}
                            onClick={() => handleClosePosition(record.id)}
                            title="平仓"
                        >
                            平仓
                        </Button>
                    )}
                </Space>
            ),
        },
    ];

    // 计算持仓统计数据
    const getPositionStats = () => {
        const openPositions = positions.filter(p => p.status === 'open');
        const totalPnL = openPositions.reduce((sum, p) => sum + (p.profit_loss || 0), 0);
        const avgPnLPercent = openPositions.length
            ? openPositions.reduce((sum, p) => sum + (p.profit_loss_percent || 0), 0) / openPositions.length
            : 0;

        return {openCount: openPositions.length, totalPnL, avgPnLPercent};
    };

    const stats = getPositionStats();

    return (
        <div>
            <Title level={2}>持仓管理</Title>

            <Card bordered={false} style={{marginBottom: 24}}>
                <div style={{display: 'flex', justifyContent: 'space-between'}}>
                    <Space size="large">
                        <Statistic title="当前持仓数" value={stats.openCount}/>
                        <Statistic
                            title="总盈亏"
                            value={stats.totalPnL}
                            precision={2}
                            valueStyle={{color: stats.totalPnL >= 0 ? '#3f8600' : '#cf1322'}}
                            prefix={stats.totalPnL >= 0 ? <ArrowUpOutlined/> : <ArrowDownOutlined/>}
                            suffix="USDT"
                        />
                        <Statistic
                            title="平均盈亏率"
                            value={stats.avgPnLPercent}
                            precision={2}
                            valueStyle={{color: stats.avgPnLPercent >= 0 ? '#3f8600' : '#cf1322'}}
                            prefix={stats.avgPnLPercent >= 0 ? <ArrowUpOutlined/> : <ArrowDownOutlined/>}
                            suffix="%"
                        />
                    </Space>
                    <Button
                        type="primary"
                        icon={<SyncOutlined spin={refreshing}/>}
                        onClick={handleRefresh}
                        loading={refreshing}
                    >
                        刷新数据
                    </Button>
                </div>
            </Card>

            <Card bordered={false}>
                <Table
                    columns={columns}
                    dataSource={positions}
                    rowKey="id"
                    loading={loading}
                    pagination={{pageSize: 10}}
                />
            </Card>

            {/* 持仓详情弹窗 */}
            <Modal
                title="持仓详情"
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={[
                    <Button key="close" onClick={() => setDetailModalVisible(false)}>
                        关闭
                    </Button>,
                    currentPosition?.status === 'open' && (
                        <Button
                            key="closePosition"
                            type="primary"
                            danger
                            onClick={() => {
                                setDetailModalVisible(false);
                                if (currentPosition) handleClosePosition(currentPosition.id);
                            }}
                        >
                            平仓
                        </Button>
                    ),
                ]}
                width={800}
            >
                {currentPosition && (
                    <>
                        <Descriptions title="基本信息" bordered>
                            <Descriptions.Item label="持仓ID">{currentPosition.id}</Descriptions.Item>
                            <Descriptions.Item label="策略ID">{currentPosition.strategy_id}</Descriptions.Item>
                            <Descriptions.Item label="状态">
                                {currentPosition.status === 'open'
                                    ? <Badge status="processing" text="持仓中"/>
                                    : <Badge status="default" text="已平仓"/>}
                            </Descriptions.Item>
                            <Descriptions.Item label="开仓时间" span={2}>
                                {new Date(currentPosition.opened_at).toLocaleString()}
                            </Descriptions.Item>
                            {currentPosition.closed_at && (
                                <Descriptions.Item label="平仓时间">
                                    {new Date(currentPosition.closed_at).toLocaleString()}
                                </Descriptions.Item>
                            )}
                        </Descriptions>

                        <Divider/>

                        <div style={{display: 'flex', justifyContent: 'space-between'}}>
                            <Card title="多头信息" style={{width: '48%'}}>
                                <p><strong>交易所:</strong> {getExchangeName(currentPosition.long_exchange)}</p>
                                <p><strong>交易对:</strong> {currentPosition.long_symbol}</p>
                                <p><strong>订单ID:</strong> {currentPosition.long_order_id}</p>
                                <p><strong>数量:</strong> {currentPosition.long_amount}</p>
                                <p><strong>价格:</strong> {currentPosition.long_price.toFixed(2)}</p>
                                <p>
                                    <strong>价值:</strong> {(currentPosition.long_amount * currentPosition.long_price).toFixed(2)} USDT
                                </p>
                            </Card>

                            <Card title="空头信息" style={{width: '48%'}}>
                                <p><strong>交易所:</strong> {getExchangeName(currentPosition.short_exchange)}</p>
                                <p><strong>交易对:</strong> {currentPosition.short_symbol}</p>
                                <p><strong>订单ID:</strong> {currentPosition.short_order_id}</p>
                                <p><strong>数量:</strong> {currentPosition.short_amount}</p>
                                <p><strong>价格:</strong> {currentPosition.short_price.toFixed(2)}</p>
                                <p>
                                    <strong>价值:</strong> {(currentPosition.short_amount * currentPosition.short_price).toFixed(2)} USDT
                                </p>
                            </Card>
                        </div>

                        <Divider/>

                        <Card title="盈亏信息">
                            <div style={{display: 'flex', justifyContent: 'space-around'}}>
                                <Statistic
                                    title="盈亏金额"
                                    value={currentPosition.profit_loss || 0}
                                    precision={2}
                                    valueStyle={{
                                        color: (currentPosition.profit_loss || 0) >= 0 ? '#3f8600' : '#cf1322',
                                        fontSize: 24
                                    }}
                                    prefix={(currentPosition.profit_loss || 0) >= 0 ? <ArrowUpOutlined/> :
                                        <ArrowDownOutlined/>}
                                    suffix="USDT"
                                />
                                <Statistic
                                    title="盈亏百分比"
                                    value={currentPosition.profit_loss_percent || 0}
                                    precision={2}
                                    valueStyle={{
                                        color: (currentPosition.profit_loss_percent || 0) >= 0 ? '#3f8600' : '#cf1322',
                                        fontSize: 24
                                    }}
                                    prefix={(currentPosition.profit_loss_percent || 0) >= 0 ? <ArrowUpOutlined/> :
                                        <ArrowDownOutlined/>}
                                    suffix="%"
                                />
                            </div>
                        </Card>
                    </>
                )}
            </Modal>
        </div>
    );
};

export default PositionsPage;
