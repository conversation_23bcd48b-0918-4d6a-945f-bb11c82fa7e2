import React from 'react';
import {Card, Row, Col, Button, Statistic, Typography} from 'antd';
import {useNavigate} from 'react-router-dom';
import {PlusOutlined, WalletOutlined, LineChartOutlined, SettingOutlined} from '@ant-design/icons';

const {Title, Paragraph} = Typography;

/**
 * 首页组件
 * 展示系统概览和快捷操作入口
 */
const HomePage: React.FC = () => {
    const navigate = useNavigate();

    return (
        <div>
            <Typography style={{marginBottom: 24}}>
                <Title level={2}>加密货币跨交易所套保系统</Title>
                <Paragraph>
                    欢迎使用 Crypt Carry 系统。本系统提供跨交易所自动化多空套保功能，帮助您捕捉市场机会，降低风险。
                </Paragraph>
            </Typography>

            <Row gutter={[24, 24]}>
                {/* 快捷操作卡片 */}
                <Col xs={24} md={12}>
                    <Card title="快捷操作" bordered={false}>
                        <Row gutter={[16, 16]}>
                            <Col span={12}>
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined/>}
                                    size="large"
                                    block
                                    onClick={() => navigate('/hedging')}
                                >
                                    新建套保策略
                                </Button>
                            </Col>
                            <Col span={12}>
                                <Button
                                    icon={<WalletOutlined/>}
                                    size="large"
                                    block
                                    onClick={() => navigate('/positions')}
                                >
                                    管理持仓
                                </Button>
                            </Col>
                        </Row>
                    </Card>
                </Col>

                {/* 系统概览卡片 */}
                <Col xs={24} md={12}>
                    <Card title="系统概览" bordered={false}>
                        <Row gutter={16}>
                            <Col span={8}>
                                <Statistic
                                    title="活跃策略"
                                    value={0}
                                    prefix={<SettingOutlined/>}
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title="当前持仓"
                                    value={0}
                                    prefix={<WalletOutlined/>}
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title="套保机会"
                                    value={0}
                                    prefix={<LineChartOutlined/>}
                                />
                            </Col>
                        </Row>
                    </Card>
                </Col>

                {/* 交易所支持卡片 */}
                <Col xs={24}>
                    <Card title="支持的交易所" bordered={false}>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={12}>
                                <Card type="inner" title="币安 (Binance)">
                                    <p>支持现货和合约交易</p>
                                    <p>提供资金费率和市场深度数据</p>
                                    <p>完整的API接口支持</p>
                                </Card>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Card type="inner" title="OKX">
                                    <p>支持现货和合约交易</p>
                                    <p>提供资金费率和市场深度数据</p>
                                    <p>完整的API接口支持</p>
                                </Card>
                            </Col>
                        </Row>
                    </Card>
                </Col>

                {/* 系统功能介绍卡片 */}
                <Col xs={24}>
                    <Card title="系统功能" bordered={false}>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                                <Card type="inner" title="跨交易所套保">
                                    <p>支持币安和OKX之间的跨交易所套保</p>
                                    <p>多空对冲，降低市场风险</p>
                                </Card>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Card type="inner" title="自动化交易">
                                    <p>基于设定条件自动执行交易</p>
                                    <p>支持资金费率、价格差异和基差率触发</p>
                                </Card>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Card type="inner" title="风险管理">
                                    <p>持仓监控和风险预警</p>
                                    <p>自动平仓功能，保护资金安全</p>
                                </Card>
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default HomePage;
