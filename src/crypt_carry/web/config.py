"""
Web应用配置文件
定义不同环境的配置参数
"""
import datetime
import os

# 基础目录
try:
    from crypt_carry.utils.paths import get_project_root, WEB_DIR

    BASE_DIR = str(WEB_DIR)
    PROJECT_ROOT = str(get_project_root())
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    PROJECT_ROOT = os.path.abspath(os.path.join(BASE_DIR, '../..'))


class BaseConfig:
    """基础配置类，包含所有环境共享的配置"""
    # 安全配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'my_precious_secret_key')
    DEBUG = False

    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt_super_secret')
    JWT_ACCESS_TOKEN_EXPIRES = datetime.timedelta(days=1)

    # 数据库配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 日志配置
    LOG_LEVEL = 'INFO'

    # 自动套保任务配置
    HEDGING_TASK_INTERVAL = 60  # 自动套保任务运行间隔（秒）
    MAX_CONCURRENT_TASKS = 5  # 最大并发任务数

    # 交易所配置
    SUPPORTED_EXCHANGES = ['binance', 'okx']


class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    SQLALCHEMY_DATABASE_URI = os.getenv(
        'DATABASE_URL',
        f'sqlite:///{os.path.join(BASE_DIR, "dev.db")}'
    )

    # 开发环境特定配置
    HEDGING_TASK_INTERVAL = 30  # 开发环境下更频繁地运行套保任务


class TestingConfig(BaseConfig):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    PRESERVE_CONTEXT_ON_EXCEPTION = False


class ProductionConfig(BaseConfig):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', f'sqlite:///{os.path.join(BASE_DIR, "prod.db")}')

    # 生产环境安全配置
    SECRET_KEY = os.getenv('SECRET_KEY')  # 必须在环境变量中设置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')  # 必须在环境变量中设置

    # 生产环境交易相关配置
    HEDGING_TASK_INTERVAL = 60  # 生产环境下标准间隔


# 配置映射，用于动态选择配置
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
