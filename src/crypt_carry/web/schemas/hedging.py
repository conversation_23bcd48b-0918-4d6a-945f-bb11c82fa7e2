"""
套保策略的序列化模式
用于API请求和响应的数据验证与转换
"""
from marshmallow import Schema, fields, validate, validates, ValidationError


class HedgingConditionSchema(Schema):
    """套保条件序列化模式"""
    id = fields.Int(dump_only=True)
    strategy_id = fields.Int(required=True, load_only=True)
    condition_type = fields.Str(required=True, validate=validate.OneOf(
        ['funding_rate', 'price_difference', 'basis_rate']
    ))

    # 多头条件
    long_exchange = fields.Str(required=True)
    long_symbol = fields.Str(required=True)
    long_amount = fields.Float(required=True, validate=validate.Range(min=0))
    long_is_spot = fields.Bool(default=True)

    # 空头条件
    short_exchange = fields.Str(required=True)
    short_symbol = fields.Str(required=True)
    short_amount = fields.Float(required=True, validate=validate.Range(min=0))
    short_is_spot = fields.Bool(default=False)

    # 触发条件
    trigger_value = fields.Float(required=True)
    comparison_operator = fields.Str(default='>', validate=validate.OneOf(
        ['>', '<', '>=', '<=', '==']
    ))

    # 条件状态
    is_active = fields.Bool(default=True)
    priority = fields.Int(default=1, validate=validate.Range(min=1, max=10))

    @validates('long_exchange')
    def validate_long_exchange(self, value):
        """验证多头交易所是否受支持"""
        if value.lower() not in ['binance', 'okx']:
            raise ValidationError(f"不支持的交易所: {value}，目前仅支持 binance 和 okx")

    @validates('short_exchange')
    def validate_short_exchange(self, value):
        """验证空头交易所是否受支持"""
        if value.lower() not in ['binance', 'okx']:
            raise ValidationError(f"不支持的交易所: {value}，目前仅支持 binance 和 okx")


class HedgingPositionSchema(Schema):
    """套保持仓序列化模式"""
    id = fields.Int(dump_only=True)
    strategy_id = fields.Int(dump_only=True)

    # 多头信息
    long_exchange = fields.Str(dump_only=True)
    long_symbol = fields.Str(dump_only=True)
    long_order_id = fields.Str(dump_only=True)
    long_amount = fields.Float(dump_only=True)
    long_price = fields.Float(dump_only=True)

    # 空头信息
    short_exchange = fields.Str(dump_only=True)
    short_symbol = fields.Str(dump_only=True)
    short_order_id = fields.Str(dump_only=True)
    short_amount = fields.Float(dump_only=True)
    short_price = fields.Float(dump_only=True)

    # 持仓状态
    status = fields.Str(dump_only=True)
    opened_at = fields.DateTime(dump_only=True)
    closed_at = fields.DateTime(dump_only=True)

    # 盈亏信息
    profit_loss = fields.Float(dump_only=True)
    profit_loss_percent = fields.Float(dump_only=True)


class HedgingStrategySchema(Schema):
    """套保策略序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True, validate=validate.Length(min=3, max=100))
    description = fields.Str(allow_none=True)
    is_active = fields.Bool(default=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 策略配置
    check_interval = fields.Int(default=60, validate=validate.Range(min=10, max=3600))
    auto_close = fields.Bool(default=True)
    max_open_positions = fields.Int(default=5, validate=validate.Range(min=1, max=50))

    # 关联数据
    positions = fields.List(fields.Nested(HedgingPositionSchema), dump_only=True)
    conditions = fields.List(fields.Nested(HedgingConditionSchema), dump_only=True)


class HedgingActionSchema(Schema):
    """套保操作序列化模式"""
    action_type = fields.Str(required=True, validate=validate.OneOf(
        ['start', 'stop', 'close_position']
    ))
    strategy_id = fields.Int(required=True)
    position_id = fields.Int(required=False)  # 仅close_position操作需要
