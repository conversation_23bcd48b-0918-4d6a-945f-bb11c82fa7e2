# 钉钉机器人配置
DINGDING_MSG_ROBOT:
  4H_VEGAS:
    WEBHOOK: "https://oapi.dingtalk.com/robot/send?access_token=b005db5edea7d52fcaaef7371e061c8287a0db613f5f4781ec6aadba6f3ee151"
    SECRET: "SEC31a3e1a7d684af6d052e33de1b8f6e6bac6fcd7449be7c4774252bcb558ada63"

# API密钥配置
API_KEYS:
  BINANCE:
    KEY: "RtYmj95Cq5kpBrZlHvW10wA2roip4utVLEYwUgY0L6Q2Q1Gf6oxqdoVlcovG2Ou9"
    SECRET: "uuvrDDTI14ijim4aCXQjOcZAVtsLO4ZottaxuKDaLrxJMDE0FUd0DmxoOAxy3wUV"
  OKX:
    # KEY: "a1653194-744d-4334-af92-80a9fcd4bbce"
    # SECRET: "A383DC9F3C37E9CFAB633A083735D4D2"
    # PASSWORD: "Ny12345678@"  # OKX API 密码
    # test account
     KEY: "f66bbc83-b820-4a2e-a577-2a585324604e"
     SECRET: "62F0E2068163995B85700CF0AB709FF4"
     PASSWORD: "Zhaolei521+"  # OKX API 密码
    # firm account
    #    KEY: "02095f47-893e-4d2d-9449-95ae4124144d"
    #    SECRET: "DBF9FC935FBC1668AE0471F03ACB75FB"
    #    PASSWORD: "Zhaolei521+"  # OKX API 密码
    # attach low fee account

# API URL配置
API_URLS:
  BINANCE:
    SPOT: "https://api.binance.com"
    FUTURES: "https://fapi.binance.com"
    SPOT_WS: "wss://stream.binance.com:9443/ws"
    FUTURES_WS: "wss://fstream.binance.com/ws"
  OKX:
    REST: "https://www.okx.com/api/v5"
    WS: "wss://ws.okx.com:8443/ws/v5/public"

# WebSocket配置
WEBSOCKET_CONFIG:
  PING_INTERVAL: 180
  CONNECTION_TIMEOUT: 600
  
HTTP_CONFIG:
  # 超时配置（毫秒）
  TIMEOUT: 30000


# 路径配置
PATHS:
  # 缓存目录
  CACHE_PAIRS: "data/cache"
  # 市场数据存储目录
  MARKET_DATA: "data/market_data"
  # 市场价格数据目录
  MARKET_PRICE: "data/market_data/market_price"
  # 套利机会数据目录
  ARBITRAGE: "data/market_data/arbitrage"
  # 资金费率数据目录
  FUNDING_RATES: "data/market_data/funding_rates"
  # 历史得分数据目录
  HISTORICAL_SCORES: "data/market_data/historical_scores"


# 重连延迟（秒）
RECONNECT_DELAY: 5

# 代理配置
PROXY:
  enabled: true
  http: "http://127.0.0.1:7890"
  https: "http://127.0.0.1:7890"
  ws: "ws://127.0.0.1:7890"
