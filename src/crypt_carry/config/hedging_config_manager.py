"""
套保策略配置管理器 - 简化版
"""
import logging
from typing import Dict, Any, List, Optional

from crypt_carry.config.config_loader import ConfigLoader

logger = logging.getLogger(__name__)


class HedgingConfigManager:
    """套保策略配置管理器"""

    def __init__(self):
        """初始化配置管理器"""
        self._config_loader = ConfigLoader
        self._config_cache: Optional[Dict[str, Any]] = None

    def reload_config(self) -> None:
        """重新加载配置"""
        self._config_cache = None
        self._config_loader.reload_config(force=True)
        logger.info("套保配置已重新加载")

    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        if self._config_cache is None:
            self._config_cache = self._config_loader.get_hedging_config()
        return self._config_cache

    @property
    def auto_start_monitoring(self) -> bool:
        """是否自动启动监控"""
        return self.config.get("auto_start_monitoring", True)

    # 基础配置
    @property
    def exchanges(self) -> List[str]:
        """支持的交易所列表"""
        return self.config.get("EXCHANGES", ["OKX"])

    @property
    def max_positions(self) -> int:
        """最大持仓数量"""
        return self.config.get("STRATEGY", {}).get("MAX_POSITIONS", 10)

    @property
    def min_amount(self) -> float:
        """最小交易金额"""
        return self.config.get("STRATEGY", {}).get("MIN_AMOUNT", 100.0)

    @property
    def max_amount(self) -> float:
        """最大交易金额"""
        return self.config.get("STRATEGY", {}).get("MAX_AMOUNT", 10000.0)

    @property
    def check_interval(self) -> int:
        """检查间隔(秒)"""
        return self.config.get("STRATEGY", {}).get("CHECK_INTERVAL", 5)

    # 风险控制
    @property
    def max_total_exposure(self) -> float:
        """最大总敞口"""
        return self.config.get("RISK", {}).get("MAX_TOTAL_EXPOSURE", 100000.0)

    @property
    def max_single_position(self) -> float:
        """单笔最大持仓"""
        return self.config.get("RISK", {}).get("MAX_SINGLE_POSITION", 10000.0)

    @property
    def stop_loss_threshold(self) -> float:
        """止损阈值"""
        return self.config.get("RISK", {}).get("STOP_LOSS_THRESHOLD", -0.1)

    @property
    def take_profit_threshold(self) -> float:
        """止盈阈值"""
        return self.config.get("RISK", {}).get("TAKE_PROFIT_THRESHOLD", 0.2)

    # 模板配置
    def get_template(self, condition_type: str, symbol: str = "DEFAULT") -> Dict[str, Any]:
        """获取条件模板

        Args:
            condition_type: 条件类型 (PRICE_DIFFERENCE, FUNDING_RATE_DIFF, BASIS_RATE)
            symbol: 交易对符号或DEFAULT

        Returns:
            Dict[str, Any]: 模板配置
        """
        templates = self.config.get("TEMPLATES", {}).get(condition_type, {})
        return templates.get(symbol, templates.get("DEFAULT", {}))

    # 通知配置
    @property
    def dingtalk_enabled(self) -> bool:
        """是否启用钉钉通知"""
        return self.config.get("NOTIFICATIONS", {}).get("DINGTALK", {}).get("ENABLED", True)

    @property
    def dingtalk_prefix(self) -> str:
        """钉钉消息前缀"""
        return self.config.get("NOTIFICATIONS", {}).get("DINGTALK", {}).get("PREFIX", "套保策略")

    # 配置更新方法
    def update_strategy_config(self, **kwargs) -> None:
        """更新策略配置"""
        updates = {"STRATEGY": kwargs}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None

    def update_risk_config(self, **kwargs) -> None:
        """更新风险配置"""
        updates = {"RISK": kwargs}
        self._config_loader.update_hedging_config(updates)
        self._config_cache = None

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "exchanges": self.exchanges,
            "max_positions": self.max_positions,
            "amount_range": f"{self.min_amount}-{self.max_amount} USDT",
            "check_interval": f"{self.check_interval}s",
            "risk_limits": {
                "max_total_exposure": self.max_total_exposure,
                "max_single_position": self.max_single_position,
                "stop_loss_threshold": f"{self.stop_loss_threshold:.1%}",
                "take_profit_threshold": f"{self.take_profit_threshold:.1%}"
            },
            "notifications": {
                "dingtalk_enabled": self.dingtalk_enabled,
                "dingtalk_prefix": self.dingtalk_prefix
            }
        }
