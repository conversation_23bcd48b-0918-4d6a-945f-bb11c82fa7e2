# 交易所配置
EXCHANGES:
  - BINANCE
  - OKX

# 订单执行配置
ORDER:
  MAX_RETRIES: 5
  # 重试间隔（秒）
  RETRY_INTERVAL_MS: 1000
  # maker订单检查间隔（毫秒）
  MAKER_CHECK_INTERVAL_MS: 300
  # taker订单检查间隔（毫秒）
  TAKER_CHECK_INTERVAL_MS: 300
  # 订单超时时间（毫秒）
  ORDER_TIMEOUT_MS: 8000
  # 新增全局超时时间配置
  GLOBAL_EXECUTION_TIMEOUT_MS: 30000
  # 订单执行冷却期（秒），下单后多长时间内不检查该交易对的持仓平衡
  ORDER_EXECUTION_COOLDOWN_SEC: 60
  # 全局冷却时间（秒）
  GLOBAL_COOLDOWN_SEC: 90


# 交易所特有配置
EXCHANGE_CONFIG:
  BINANCE:
    ENABLED: false
    PAIR_FROM: CONFIG
    STRATEGY:
      MAX_POSITIONS: 5
      LEVERAGE: 3
      USE_BALANCE: 500 # USDT, 单交易对可用资金
      EWMA_ALPHA: 0.7  # EWMA平滑系数，值越大表示越重视最新数据
      MAX_SCORE_HISTORY: 21  # 最大历史得分数量
      POSITION_HOLD_TIME: 3600  # 持仓最小持有时间（秒），避免频繁交易
      NEGATIVE_TRADING: false  # 是否允许做负资金费率套利交易
      TRADING_SPOT_FEE: 0.001  # 交易手续费
      TRADING_FUTURE_FEE: 0.0002  # 交易手续费
      FORCE_REBALANCE: true # 是否强制换仓
      FORCE_POSITION_CHECK: true # 是否强制触发持仓监控
      POSITION_MONITOR_INTERVAL_MINUTES: 0.2  # 持仓监控间隔（分钟）
      REBALANCE_AHEAD_MINUTES: 30  # 换仓提前时间（分钟）
      BASIS_THRESHOLD: 0.002  # 基差率阈值（0.1%）
      MARGIN_RISK_THRESHOLD: 0.1 # 保证金风险阈值（10%）
      AUTO_CLOSE_ON_HIGH_RISK: true # 是否在极高风险时自动平仓
    SCORING:
      # 动态周期参数：计算未来几个周期的资金费率累积收益
      FUTURE_CYCLES: 6
      # 得分权重：资金费率和基差率的权重1
      FUNDING_WEIGHT: 0.8
      BASIS_WEIGHT: 0.2
    MARKET:
      # 交易量阈值设置
      SPOT_VOLUME_THRESHOLD: 3000000  # 现货交易量阈值 (300万USDT)
      FUTURE_VOLUME_THRESHOLD: 6000000  # 合约交易量阈值 (600万USDT)

  OKX:
    ENABLED: true
    PAIR_FROM: API # CONFIG/ API
    STRATEGY:
      MAX_POSITIONS: 5
      LEVERAGE: 3
      USE_BALANCE: 100 # USDT, 单交易对可用资金(账户资金)
      EWMA_ALPHA: 0.7  # EWMA平滑系数，值越大表示越重视最新数据
      MAX_SCORE_HISTORY: 21  # 最大历史得分数量
      POSITION_HOLD_TIME: 3600  # 持仓最小持有时间（秒），避免频繁交易
      NEGATIVE_TRADING: false  # 是否允许做负资金费率套利交易
      TRADING_SPOT_FEE: 0.001  # 交易手续费
      TRADING_FUTURE_FEE: 0.0002  # 交易手续费
      FORCE_REBALANCE: false # 是否强制换仓
      FORCE_POSITION_CHECK: false # 是否强制触发持仓监控
      POSITION_MONITOR_INTERVAL_MINUTES: 0.2  # 持仓监控间隔（分钟）
      REBALANCE_AHEAD_MINUTES: 30  # 换仓提前时间（分钟）
      BASIS_THRESHOLD: 0.001  # 基差率阈值（0.1%）
      MARGIN_RISK_THRESHOLD: 0.1 # 保证金风险阈值（10%）
      AUTO_CLOSE_ON_HIGH_RISK: true # 是否在极高风险时自动平仓
    SCORING:
      # 动态周期参数：计算未来几个周期的资金费率累积收益
      FUTURE_CYCLES: 6
      # 得分权重：资金费率和基差率的权重
      FUNDING_WEIGHT: 0.8
      BASIS_WEIGHT: 0.2
    MARKET:
      # 交易量阈值设置
      SPOT_VOLUME_THRESHOLD: 90000000  # 现货交易量阈值 (350万USDT)
      FUTURE_VOLUME_THRESHOLD: 16000000  # 合约交易量阈值 (800万USDT)

EXCHANGE_PAIRS:
  BINANCE:
    symbol:
      # - BTC
      # - ETH
      # - SOL
      # - XRP
      # - BNB
      - DOGE
      - ADA
      - SHIB
      - AVAX
      - TRX
    # - BCH
    # - SUI
    # - PEPE
    # - NEAR
    # - UNI
    # - LTC
    # - APT
    # - HBAR
    # - ICP
    # - DAI
    # - ETC
    # - TAO
    # - RENDER
    # - FET
    # - FIL
    # - ARB
    # - VET
    # - ALGO
    # - TIA
    # - IMX
    # - STX
    # - BONK
    # - ATOM
    # - AAVE
    # - OP
    quote:
      - USDT
    exclude: [ ]
  OKX:
    symbol:
      - BTC
      - ETH
      - SOL
      - DOGE
      - KISHU
      - BUZZ
      - PIPPIN
      - ANIME
      - SWEAT
      - ICX
      - CFX
      - FARTCOIN
      - XCH
      - UXLINK
      - GMX
      - ALCH
      - ZK
      - BIO
      - BADGER
      - HYPE
      - GAS
      - AIDOGE
      - LAYER
      - FOXY
      - MOVR
      - DGB
      - SWARMS
      - MEMEFI
      - QTUM
      - AVAAI
      - GRIFFAIN
      - VELO
      - OM
      - LOOKS
      - BUZZ
      - ZENT
      - JELLYJELLY
      - FOXY
      - ARC
      - SHELL
      - NEIROETH


    quote:
      - USDT
    exclude: [ ]
    detect_exclude:
      - PI
      - OM

# 保存最近5个资金费率
MAX_HISTORICAL_RATES: 5

# 费率阈值配置 最小资金费率阈值
MIN_FUNDING_RATE: 0.01

# 费率阈值配置 最大基差率阈值
MAX_BASIS_RATE: 0.25

# 最小换仓金额
MIN_REBALANCE_AMOUNT: 100

# 风控配置
STOP_LOSS_RATE: 0.02
MAX_DRAWDOWN: 0.1

# 行情价格数据有效时间(秒)
MARKET_DATA_AGE_SEC: 8

# 交易执行配置
# 订单超时时间
ORDER_TIMEOUT: 10

# 最小交易金额
MIN_TRADE_AMOUNT: 10

# 滑点容忍度
SLIPPAGE_TOLERANCE: 0.001

# 数据存储配置
DATA_SAVE_INTERVAL: 300

# 最大历史数据保存天数
MAX_HISTORY_DAYS: 30

# 是否始终更新资金费率，不考虑时间点限制
ALWAYS_UPDATE_FUNDING_RATES: true

# 资金费率更新间隔（分钟），仅在always_update_funding_rates=True时使用
FUNDING_RATE_UPDATE_INTERVAL: 1

# 资金费率收取时间点
FUNDING_RATE_TIMES:
  - 0
  - 8
  - 16
# 换仓提前时间（分钟）
REBALANCE_AHEAD_MINUTES: 60
# 是否允许自动换仓
AUTO_REBALANCE_ENABLED: true

# 资金费率配置
FUNDING_RATE_HALF_LIFE: 28800000  # 资金费率半衰期（毫秒），默认8小时
