from pathlib import Path
from typing import Dict

from crypt_carry.config.config_loader import ConfigLoader
from crypt_carry.utils.paths import get_project_root


class DataDirectory:
    _instance = None

    def __new__(cls, config: Dict = None):
        if cls._instance is None:
            cls._instance = super(DataDirectory, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config: Dict = None):
        if self._initialized:
            return

        # 加载配置
        self.config = config or ConfigLoader.get_base_config()
        self.base_dir = get_project_root()  # 项目根目录
        self._initialized = True
        self._init_directories()

    def _init_directories(self):
        """初始化所有数据目录"""
        paths_config = self.config.get("PATHS", {})

        # 创建所有配置的目录
        for path_name, relative_path in paths_config.items():
            full_path = self.base_dir / relative_path
            full_path.mkdir(parents=True, exist_ok=True)

    def get_path(self, path_name: str) -> Path:
        """获取指定的数据目录路径"""
        relative_path = self.config.get("PATHS", {}).get(path_name)
        if not relative_path:
            raise ValueError(f"未找到路径配置: {path_name}")
        return self.base_dir / relative_path
