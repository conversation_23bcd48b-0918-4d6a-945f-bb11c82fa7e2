"""
异步重试工具模块
提供异步函数的重试机制，用于处理网络请求等可能失败的操作
"""
import asyncio
import logging
import random
from functools import wraps
from typing import TypeVar, Callable, Any, Optional, Tuple, Type

logger = logging.getLogger(__name__)


# 自定义可重试异常
class RetryableError(Exception):
    """可重试异常基类
    
    这个异常类及其子类可以被async_retry装饰器捕获，并触发重试机制。
    在需要主动触发重试的场景中，可以抛出这个异常。
    
    Example:
        if some_condition:
            raise RetryableError('需要重试的特定原因')
    """
    pass


# 定义返回类型
T = TypeVar('T')


def async_retry(
        max_retries: int = 3,
        initial_delay: float = 1.0,
        max_delay: float = 30.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        retryable_exceptions: Tuple[Type[Exception], ...] = None,
        non_retryable_exceptions: Tuple[Type[Exception], ...] = None,
        timeout: Optional[float] = None,
        error_type: str = ""
):
    """异步函数重试装饰器
    
    该装饰器可以应用于任何异步函数，在指定的异常发生时自动重试。
    重试策略包括指数退避和可选的随机抖动，以减少多个请求同时重试的影响。
    
    Args:
        max_retries: 最大重试次数，指定在放弃前尝试的最大次数
        initial_delay: 初始延迟（秒），首次重试前等待的时间
        max_delay: 最大延迟（秒），任何重试的最大等待时间
        backoff_factor: 退避因子，每次重试增加的倍数（指数退避）
        jitter: 是否添加随机抖动，防止多个请求同时重试
        retryable_exceptions: 可重试的异常类型元组，默认为网络相关异常
        non_retryable_exceptions: 不可重试的异常类型元组，出现时直接抛出不进行重试
        timeout: 函数执行超时时间（秒），超时后会抛出asyncio.TimeoutError
        error_type: 错误类型描述，用于日志记录
    
    Returns:
        装饰器函数
    
    Example:
        @async_retry(max_retries=3, initial_delay=2.0)
        async def fetch_data(url):
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return await response.json()
        
        # 带超时的使用方式
        @async_retry(max_retries=3, timeout=5.0, error_type="获取数据")
        async def fetch_data_with_timeout(url):
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return await response.json()
    """
    if retryable_exceptions is None:
        # 默认处理网络相关的异常和自定义可重试异常
        import aiohttp
        retryable_exceptions = (aiohttp.ClientError, asyncio.TimeoutError, ConnectionError, RetryableError)

    if non_retryable_exceptions is None:
        # 默认的不可重试异常
        non_retryable_exceptions = (ValueError, TypeError, KeyError, AttributeError)

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            delay = initial_delay

            # 获取第一个参数作为symbol，用于日志记录
            symbol = args[0] if args else "unknown"
            log_prefix = f"{error_type} {symbol}" if error_type else f"{func.__name__} {symbol}"

            # 尝试执行函数，包括初始尝试和重试
            for retry_count in range(max_retries + 1):
                try:
                    if retry_count > 0:
                        logger.debug(f"重试 {log_prefix} ({retry_count}/{max_retries})...")

                    # 使用超时控制（如果提供了timeout参数）
                    if timeout:
                        return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)
                    else:
                        return await func(*args, **kwargs)

                except non_retryable_exceptions as e:
                    # 不可重试的错误直接抛出
                    logger.error(f"{log_prefix} 遇到不可重试错误: {str(e)}")
                    raise

                except asyncio.TimeoutError as e:
                    last_exception = e
                    if retry_count == max_retries:
                        logger.error(f"{log_prefix} 在 {max_retries} 次重试后仍然超时")
                        raise
                    logger.warning(f"{log_prefix} 执行超时 (重试 {retry_count + 1}/{max_retries})")

                except retryable_exceptions as e:
                    last_exception = e
                    if retry_count == max_retries:
                        logger.error(f"{log_prefix} 在 {max_retries} 次重试后仍然失败: {str(e)}")
                        raise

                    logger.warning(f"{log_prefix} 失败 (重试 {retry_count + 1}/{max_retries}): {str(e)}")

                except Exception as e:
                    # 处理其他未指定的异常
                    last_exception = e
                    if retry_count == max_retries:
                        logger.error(f"{log_prefix} 在 {max_retries} 次重试后仍然失败(未知错误): {str(e)}")
                        raise

                    logger.warning(f"{log_prefix} 未知错误 (重试 {retry_count + 1}/{max_retries}): {str(e)}")

                # 计算下一次重试的延迟时间
                delay = min(delay * backoff_factor, max_delay)
                if jitter:  # 添加随机抖动，避免多个请求同时重试
                    delay = delay * (0.5 + random.random())

                logger.info(f"{log_prefix} 等待 {delay:.2f} 秒后重试...")
                await asyncio.sleep(delay)

            # 如果所有尝试都失败
            raise last_exception

        return wrapper

    return decorator
