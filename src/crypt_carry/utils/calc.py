"""
计算工具函数
"""
from typing import Tuple


def calculate_value_delta(spot_value: float, futures_value: float) -> Tuple[float, float]:
    """计算现货和期货持仓价值的差值和百分比
    
    Args:
        spot_value: 现货持仓价值
        futures_value: 期货持仓价值
        
    Returns:
        Tuple[float, float]: (差值, 差值百分比)
            - 差值 = spot_value - futures_value
            - 差值百分比 = 差值 / max(spot_value, futures_value)
    """
    if spot_value == 0 and futures_value == 0:
        return 0, 0

    delta_value = spot_value - futures_value
    max_value = max(abs(spot_value), abs(futures_value))

    # 计算差值百分比，避免除以零
    delta_percent = delta_value / max_value if max_value > 0 else 0

    return delta_value, delta_percent


def calculate_position_delta(delta_value: float, spot_price: float, futures_price: float) -> float:
    """计算需要调整的持仓数量
    
    Args:
        delta_value: 需要调整的价值差额
        spot_price: 现货价格
        futures_price: 期货价格
        
    Returns:
        float: 需要调整的数量
            - 如果 delta_value 为正，表示现货价值大于期货价值，需要增加期货持仓或减少现货持仓
            - 如果 delta_value 为负，表示期货价值大于现货价值，需要增加现货持仓或减少期货持仓
    """
    # 使用相应资产的价格计算需要调整的数量
    if delta_value > 0:
        # 现货价值大于期货价值，使用期货价格计算需要增加的期货持仓数量
        return delta_value / futures_price if futures_price > 0 else 0
    else:
        # 期货价值大于现货价值，使用现货价格计算需要增加的现货持仓数量
        return abs(delta_value) / spot_price if spot_price > 0 else 0


def calculate_expected_profit(funding_rate: float, basis_rate: float) -> float:
    """计算预期收益率
    
    Args:
        funding_rate: 资金费率
        basis_rate: 基差率
        
    Returns:
        float: 预期收益率 = |基差率| + |资金费率|
    """
    return abs(funding_rate) + abs(basis_rate)


def calculate_leverage(position_value: float, collateral_value: float) -> float:
    """计算杠杆率
    
    Args:
        position_value: 持仓价值
        collateral_value: 抵押品价值
        
    Returns:
        float: 杠杆率 = 持仓价值 / 抵押品价值
    """
    if collateral_value <= 0:
        return 0
    return position_value / collateral_value


def calculate_rebalance_amount(spot_amount: float, futures_amount: float,
                               spot_price: float, futures_price: float,
                               threshold: float = 0.02) -> Tuple[float, str, str]:
    """计算需要重新平衡的金额
    
    Args:
        spot_amount: 现货持仓数量
        futures_amount: 期货持仓数量
        spot_price: 现货价格
        futures_price: 期货价格
        threshold: 平衡阈值，默认2%
        
    Returns:
        Tuple[float, str, str]: (需要调整的数量, 调整方向, 买卖操作)
            - 调整方向: 'spot' 或 'futures'
            - 买卖操作: 'buy' 或 'sell'
    """
    # 计算价值
    spot_value = spot_amount * spot_price
    futures_value = futures_amount * futures_price

    # 计算价值差异
    delta_value, delta_percent = calculate_value_delta(spot_value, futures_value)

    # 如果差异在阈值内，不需要调整
    if abs(delta_percent) <= threshold:
        return 0, '', ''

    # 确定调整方向和操作
    if delta_value > 0:  # 现货价值大于期货价值
        adjustment_side = 'futures'
        action = 'buy'  # 买入期货
        delta_amount = delta_value / futures_price
    else:  # 期货价值大于现货价值
        adjustment_side = 'spot'
        action = 'buy'  # 买入现货
        delta_amount = abs(delta_value) / spot_price

    return delta_amount, adjustment_side, action


def calculate_basis_rate(spot_price: float, futures_price: float) -> float:
    """计算基差率
    
    Args:
        spot_price: 现货价格
        futures_price: 合约价格
        
    Returns:
        float: 基差率
    """
    if spot_price <= 0:
        return 0

    basis = (futures_price - spot_price) / spot_price
    return basis
