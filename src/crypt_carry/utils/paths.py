"""
路径配置模块
统一管理项目中使用的各种路径
"""

import os
from pathlib import Path
from typing import Union

# 项目根目录 - 从当前文件位置向上三级到达项目根目录
# 当前文件: src/crypt_carry/utils/paths.py
# 项目根目录: /Users/<USER>/My/python/crypt_carry
PROJECT_ROOT = Path(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# src目录
SRC_DIR = PROJECT_ROOT / 'src'

# crypt_carry包目录
PACKAGE_DIR = SRC_DIR / 'crypt_carry'

# 各个子模块目录
CONFIG_DIR = PACKAGE_DIR / 'config'
UTILS_DIR = PACKAGE_DIR / 'utils'
CORE_DIR = PACKAGE_DIR / 'core'
WEB_DIR = PACKAGE_DIR / 'web'
STRATEGIES_DIR = PACKAGE_DIR / 'strategies'

# 数据目录
DATA_DIR = PROJECT_ROOT / 'data'
LOGS_DIR = PROJECT_ROOT / 'logs'

# Web相关目录
WEB_DATA_DIR = WEB_DIR / 'data'
WEB_FRONTEND_DIR = WEB_DIR / 'frontend'


def get_project_root() -> Path:
    """获取项目根目录
    
    Returns:
        Path: 项目根目录路径
    """
    return PROJECT_ROOT


def get_config_dir() -> Path:
    """获取配置目录
    
    Returns:
        Path: 配置目录路径
    """
    return CONFIG_DIR


def get_data_dir() -> Path:
    """获取数据目录
    
    Returns:
        Path: 数据目录路径
    """
    return DATA_DIR


def get_logs_dir() -> Path:
    """获取日志目录
    
    Returns:
        Path: 日志目录路径
    """
    return LOGS_DIR


def get_web_data_dir() -> Path:
    """获取Web数据目录
    
    Returns:
        Path: Web数据目录路径
    """
    return WEB_DATA_DIR


def ensure_dir_exists(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建
    
    Args:
        path: 目录路径
        
    Returns:
        Path: 目录路径对象
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj


def get_relative_path_from_project_root(target_path: Union[str, Path]) -> str:
    """获取相对于项目根目录的相对路径
    
    Args:
        target_path: 目标路径
        
    Returns:
        str: 相对路径字符串
    """
    target_path_obj = Path(target_path)
    try:
        return str(target_path_obj.relative_to(PROJECT_ROOT))
    except ValueError:
        # 如果路径不在项目根目录下，返回绝对路径
        return str(target_path_obj.absolute())
