"""
测试钉钉消息发送
"""
import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(current_dir)

from crypt_carry.utils.ding_talk import DingTalk
from crypt_carry.constant.notice_type import NoticeType

def test_send():
    """测试发送消息"""
    # 获取钉钉实例
    ding_talk = DingTalk.get_instance()
    
    # 发送测试消息
    ding_talk.send_message(
        message="钉钉机器人测试消息",
        msg_type=NoticeType.MESSAGE_TEXT,
        is_at_all=False
    )

if __name__ == "__main__":
    test_send()
