"""
钉钉消息发送工具
"""
import base64
import hashlib
import hmac
import json
import logging
import time
import urllib.parse
from typing import Dict, List, Optional, Tuple

import requests

from crypt_carry.config.config_loader import ConfigLoader
from crypt_carry.constant.notice_type import NoticeType

logger = logging.getLogger(__name__)


class DingTalk:
    """钉钉消息发送器单例类"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DingTalk, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self.config = ConfigLoader.get_ding_talk_config()
            self.enabled = self.config.get('ENABLED', False)

            if self.enabled:
                self.webhook = self.config.get('WEBHOOK')
                self.secret = self.config.get('SECRET')

                if not self.webhook:
                    logger.warning("钉钉webhook未配置，禁用钉钉推送")
                    self.enabled = False
                    return

                logger.info("钉钉机器人初始化成功")

    def _get_timestamp_sign(self) -> Tuple[str, str]:
        """获取时间戳和签名

        Returns:
            Tuple[str, str]: (timestamp, sign)
        """
        timestamp = str(round(time.time() * 1000))
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode("utf-8"),
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256,
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return timestamp, sign

    def _send_request(self, msg: Dict) -> Dict:
        """发送消息到钉钉

        Args:
            msg: 消息内容

        Returns:
            Dict: 响应结果
        """
        try:
            if not self.enabled:
                logger.warning("钉钉推送未启用")
                return {"errcode": -1, "errmsg": "钉钉推送未启用"}

            headers = {'Content-Type': 'application/json'}

            if self.secret:
                timestamp, sign = self._get_timestamp_sign()
                webhook = f"{self.webhook}&timestamp={timestamp}&sign={sign}"
            else:
                webhook = self.webhook

            resp = requests.post(
                webhook,
                data=json.dumps(msg),
                headers=headers,
                timeout=5
            )
            resp_json = resp.json()

            if resp_json['errcode'] != 0:
                logger.error(f"发送钉钉消息失败: {resp_json}")
            return resp_json

        except Exception as e:
            logger.error(f"发送钉钉消息异常: {e}")
            return {"errcode": -1, "errmsg": str(e)}

    def send_message(
        self,
        message: str,
        msg_type: str = NoticeType.MESSAGE_TEXT,
        is_at_all: bool = False
    ) -> bool:
        """发送消息
        
        Args:
            message: 消息内容
            msg_type: 消息类型，用于过滤
            is_at_all: 是否@所有人

        Returns:
            bool: 是否发送成功
        """
        try:
            if not self.enabled:
                return False

            msg = {
                "msgtype": "text",
                "text": {"content": message},
                "at": {"isAtAll": is_at_all}
            }

            resp = self._send_request(msg)
            return resp.get('errcode') == 0

        except Exception as e:
            logger.error(f"发送钉钉消息失败: {e}")
            return False

    @classmethod
    def get_instance(cls) -> 'DingTalk':
        """获取单例实例"""
        return cls()
