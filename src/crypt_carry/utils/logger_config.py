"""
统一的日志配置模块
为不同策略类型提供统一的日志配置
"""
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径，以便导入util模块
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from util.logger import setup_logger, get_hedging_logger, get_funding_rate_logger, get_web_logger
except ImportError as e:
    print(f"无法导入util.logger: {e}")
    print("使用基础日志配置")
    
    def setup_logger(strategy_type: str = None):
        """基础日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(filename)s:%(lineno)d] - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'logs/{strategy_type or "app"}/app.log')
            ]
        )
        return logging.getLogger()
    
    def get_hedging_logger():
        return setup_logger('hedging')
    
    def get_funding_rate_logger():
        return setup_logger('funding_rate')
    
    def get_web_logger():
        return setup_logger('web')


class LoggerManager:
    """日志管理器"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, strategy_type: str = None, name: str = None):
        """获取日志记录器
        
        Args:
            strategy_type: 策略类型 (hedging, funding_rate, web)
            name: 日志记录器名称，通常使用 __name__
        
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        # 创建唯一的键
        key = f"{strategy_type}_{name}" if strategy_type and name else (strategy_type or name or "default")
        
        if key not in cls._loggers:
            # 设置根日志记录器
            if strategy_type == 'hedging':
                root_logger = get_hedging_logger()
            elif strategy_type == 'funding_rate':
                root_logger = get_funding_rate_logger()
            elif strategy_type == 'web':
                root_logger = get_web_logger()
            else:
                root_logger = setup_logger(strategy_type)
            
            # 如果指定了名称，创建子日志记录器
            if name:
                cls._loggers[key] = logging.getLogger(name)
            else:
                cls._loggers[key] = root_logger
        
        return cls._loggers[key]
    
    @classmethod
    def get_hedging_logger(cls, name: str = None):
        """获取套保策略日志记录器"""
        return cls.get_logger('hedging', name)
    
    @classmethod
    def get_funding_rate_logger(cls, name: str = None):
        """获取资金费率策略日志记录器"""
        return cls.get_logger('funding_rate', name)
    
    @classmethod
    def get_web_logger(cls, name: str = None):
        """获取Web服务日志记录器"""
        return cls.get_logger('web', name)


# 便捷函数
def get_strategy_logger(strategy_type: str, module_name: str = None):
    """获取策略日志记录器的便捷函数
    
    Args:
        strategy_type: 策略类型
        module_name: 模块名称，通常传入 __name__
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    return LoggerManager.get_logger(strategy_type, module_name)


# 为了向后兼容，提供直接的函数
def get_hedging_strategy_logger(module_name: str = None):
    """获取套保策略日志记录器"""
    return LoggerManager.get_hedging_logger(module_name)


def get_funding_rate_strategy_logger(module_name: str = None):
    """获取资金费率策略日志记录器"""
    return LoggerManager.get_funding_rate_logger(module_name)


def get_web_service_logger(module_name: str = None):
    """获取Web服务日志记录器"""
    return LoggerManager.get_web_logger(module_name)


# 初始化日志目录
def init_log_directories():
    """初始化日志目录"""
    try:
        from src.crypt_carry.utils.paths import get_project_root
        project_root = get_project_root()
    except ImportError:
        project_root = Path(__file__).parent.parent.parent.parent
    
    log_dirs = [
        project_root / 'logs' / 'hedging',
        project_root / 'logs' / 'funding_rate',
        project_root / 'logs' / 'web'
    ]
    
    for log_dir in log_dirs:
        log_dir.mkdir(parents=True, exist_ok=True)


# 自动初始化日志目录
init_log_directories()
