"""
套利系统启动入口
"""
import sys
from util.logger import setup_logger

# 初始化日志配置
logger = setup_logger()

import asyncio
import signal
import logging
from typing import Set

from crypt_carry.strategies.strategy_manager import StrategyManager

class ArbitrageSystem:
    """套利系统"""

    def __init__(self):
        """初始化套利系统"""
        # 初始化策略
        self.strategy_manager = StrategyManager()

        # 初始化关闭标志
        self.is_running = True

        # 初始化事件循环和任务集合
        self.loop = asyncio.get_event_loop()
        self.tasks: Set[asyncio.Task] = set()

    async def start(self):
        """启动套利系统"""
        try:
            # 注册信号处理
            for sig in (signal.SIGTERM, signal.SIGINT):
                self.loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self.signal_handler(s)))

            logger.info("正在启动套利系统...")

            # 创建策略运行任务
            strategy_task = self.loop.create_task(self.strategy_manager.run())
            self.tasks.add(strategy_task)
            strategy_task.add_done_callback(self.tasks.discard)

            # 等待任务完成或系统关闭
            while self.is_running:
                await asyncio.sleep(1)

            # 等待所有任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)

            return True

        except Exception as e:
            logger.error(f"套利系统启动失败: {str(e)}")
            return False

    async def signal_handler(self, signum):
        """处理进程信号"""
        logger.info(f"收到信号 {signum}, 准备关闭系统")
        self.is_running = False
        await self.stop()

    async def stop(self):
        """关闭系统"""
        try:
            logger.info("正在关闭系统...")

            # 停止策略
            await self.strategy_manager.stop()
            logger.info("系统已关闭")

            # 取消所有未完成的任务
            for task in self.tasks:
                if not task.done():
                    task.cancel()

            # 等待所有任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)

            # 停止事件循环
            self.loop.stop()

        except Exception as e:
            logger.error(f"关闭系统时发生错误: {str(e)}")


def run():
    """运行套利系统"""
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 创建并运行系统
        system = ArbitrageSystem()
        loop.run_until_complete(system.start())

    except KeyboardInterrupt:
        logger.info("收到退出信号，正在关闭系统...")
    except Exception as e:
        # 打印详细的异常信息和堆栈跟踪
        import traceback
        error_msg = f"系统运行异常: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
    finally:
        # 确保事件循环被关闭
        try:
            loop.run_until_complete(loop.shutdown_asyncgens())
            loop.close()
        except Exception:
            pass
        logger.info("系统已退出")


if __name__ == "__main__":
    run()
