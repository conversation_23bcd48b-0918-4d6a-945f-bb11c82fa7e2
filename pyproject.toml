[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "crypt_carry"
version = "0.1.0"
description = "加密货币跨交易所套利系统"
authors = [
    { name = "z<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.12"
readme = "README.md"
license = { text = "私有" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dependencies = [
    "numpy==2.1.3",
    "pandas==2.2.3",
    "ccxt==4.4.85",
    "aiohttp==3.10.11",
    "requests==2.32.3",
    "websockets==15.0.1",
    "openpyxl==3.1.5",
    "PyYAML==6.0.2",
    "ruamel.yaml==0.18.6",
    # Web模块依赖
    "flask==3.0.3",
    "flask-restful==0.3.10",
    "flask-cors==4.0.1",
    "flask-jwt-extended==4.6.0",
    "flask-sqlalchemy==3.1.1",
    "marshmallow==3.21.3",
]

[project.optional-dependencies]
dev = [
    "pytest==8.3.4",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/crypt_carry"
"Bug Tracker" = "https://github.com/yourusername/crypt_carry/issues"

[project.scripts]
crypt_carry = "scripts.run_carry:main"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
include = ["crypt_carry*"]
