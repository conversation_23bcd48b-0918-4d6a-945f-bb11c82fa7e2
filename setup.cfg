[metadata]
name = crypt_carry
description = 加密货币跨交易所套利系统
author = lei.zhao
author_email = <EMAIL>
license = 私有
license_files = LICENSE

[options]
package_dir =
    = src
packages = find:
python_requires = >=3.12
zip_safe = False
include_package_data = True
install_requires =
    numpy==2.1.3
    pandas==2.2.3
    ccxt==4.4.85
    aiohttp==3.10.11
    requests==2.32.3
    websockets==15.0.1
    openpyxl==3.1.5
    PyYAML==6.0.2
    ruamel.yaml==0.18.6

[options.packages.find]
where = src

[options.extras_require]
testing =
    pytest==8.3.4

[options.entry_points]
console_scripts =
    crypt_carry = scripts.run_carry:main

[flake8]
max-line-length = 100
exclude = .git,__pycache__,build,dist,venv
ignore = E203, W503

[mypy]
python_version = 3.12
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = True
disallow_untyped_decorators = False
no_implicit_optional = True
strict_optional = True

[mypy.plugins.numpy.*]
follow_imports = silent
disallow_untyped_defs = False

[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
