#!/usr/bin/env python3
"""
测试Web API的简单脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:5001/api"

def test_api_root():
    """测试API根路由"""
    print("🔍 测试API根路由...")
    try:
        response = requests.get(f"{BASE_URL}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_exchanges_api():
    """测试交易所API"""
    print("\n🔍 测试交易所API...")
    try:
        response = requests.get(f"{BASE_URL}/exchanges")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_hedging_strategies():
    """测试套保策略API"""
    print("\n🔍 测试套保策略API...")
    try:
        response = requests.get(f"{BASE_URL}/hedging/strategies")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_single_order_api():
    """测试单独下单API"""
    print("\n🔍 测试单独下单API...")
    
    # 测试数据
    order_data = {
        "exchange": "binance",
        "symbol": "BTC/USDT",
        "side": "buy",
        "order_type": "market",
        "amount": 100,  # 100 USDT
        "amount_currency": "USDT",
        "is_spot": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/hedging/orders/single",
            json=order_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code in [200, 201]
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_hedging_order_api():
    """测试套保下单API"""
    print("\n🔍 测试套保下单API...")
    
    # 测试数据
    hedging_data = {
        "conditions": [
            {
                "type": "price_difference",
                "enabled": True,
                "trigger_value": 100.0,
                "comparison_operator": ">"
            },
            {
                "type": "funding_rate_diff",
                "enabled": True,
                "trigger_value": 0.001,
                "comparison_operator": ">"
            }
        ],
        "long_exchange": "binance",
        "long_symbol": "BTC/USDT",
        "long_is_spot": True,
        "short_exchange": "okx",
        "short_symbol": "BTC/USDT",
        "short_is_spot": False,
        "amount": 1000,
        "amount_currency": "USDT",
        "priority": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/hedging/orders/hedging",
            json=hedging_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code in [200, 201]
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Web API...")
    
    tests = [
        ("API根路由", test_api_root),
        ("交易所API", test_exchanges_api),
        ("套保策略API", test_hedging_strategies),
        ("单独下单API", test_single_order_api),
        ("套保下单API", test_hedging_order_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        success = test_func()
        results.append((test_name, success))
        if success:
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n📊 测试结果汇总:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查API实现")

if __name__ == "__main__":
    main()
