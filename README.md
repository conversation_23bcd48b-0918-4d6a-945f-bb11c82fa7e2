# Crypt Carry

## 项目概述

`Crypt Carry` 是一个用于加密货币跨交易所套利的 Python 项目。它旨在自动化识别和执行不同交易所之间的价格差异，从而实现套利收益。该项目利用
WebSocket 连接实时获取市场数据，并结合配置化的交易策略来管理交易。

## 项目结构

```
carry/
├── exchange/                # 交易所相关
│   ├── base/               # 基础接口定义
│   │   ├── exchange_client.py    # 交易所客户端基类
│   │   └── exchange_config.py    # 交易所配置
│   ├── account/            # 账户管理
│   │   ├── account_manager.py    # 账户管理器（统一入口）
│   │   ├── balance_manager.py    # 余额管理
│   │   └── order_manager.py      # 订单管理
│   ├── funding/            # 资金费率管理
│   │   ├── funding_rate_manager.py    # 资金费率管理器
│   │   └── funding_rate_scheduler.py  # 资金费率定时更新调度器
│   └── implementations/    # 具体交易所实现
│       ├── binance/       # 币安
│       └── okx/          # OKX
├── trading/               # 交易策略
│   ├── strategy/         # 策略实现
│   ├── monitor/         # 监控模块
│   ├── position/        # 持仓管理
│   ├── opportunity/     # 机会检测
│   ├── trade/           # 交易执行
│   └── task/            # 任务管理
└── utils/                # 工具函数
```

## 模块详细描述

### `carry/` 核心交易逻辑

目录结构: [carry_dic.md](./docs/carry_dic.md)

#### `carry/account/` 账户管理

* **`account_manager.py`**: 负责管理和跟踪用户的账户信息，例如账户余额和交易历史。
* **`position_manager.py`**: 负责管理用户在不同交易所和交易对上的持仓信息，包括持仓数量、平均价格等。

#### `carry/base/`  基础模块

* **`exchange_config.py`**: 定义交易所配置相关的类和方法，例如 API 密钥和交易参数。
* **`exchange_connection.py`**:  定义了与交易所建立连接的通用接口和方法，负责管理网络连接和 API 请求。
* **`exchange_interface.py`**:  定义交易所交互的通用接口，例如获取市场数据、下单、取消订单等。

#### `carry/config/` 配置管理

* **`base_config.yaml`**:  基础配置，例如日志级别、数据存储路径等。
* **`config_loader.py`**:  负责加载和解析配置文件（`config.yaml` 和 `trading_config.yaml`）的模块。
* **`trading_config.yaml`**:  交易策略配置，例如套利策略、交易参数等。
* **`__init__.py`**:  使得 `config` 文件夹成为一个 Python 包。

#### `carry/exchange/` 交易所相关模块

* **`carry/exchange/base/`** 基础接口定义
    * **`exchange_client.py`**:  定义交易所客户端的基类，提供统一的接口。
    * **`exchange_config.py`**:  定义交易所配置相关的类和方法。

* **`carry/exchange/account/`** 账户管理
    * **`account_manager.py`**:  账户管理器，作为统一的账户管理入口。
    * **`balance_manager.py`**:  余额管理器，负责管理各个交易所的账户余额。
    * **`order_manager.py`**:  订单管理器，负责管理各个交易所的订单操作。

* **`carry/exchange/funding/`** 资金费率管理
    * **`funding_rate_cache.py`**:  缓存资金费率数据，减少重复查询。
    * **`funding_rate_manager.py`**:  使用单例模式统一管理多个交易所的资金费率数据，为套利策略提供统一的资金费率访问接口。
  * **`funding_rate_scheduler.py`**:  定时更新资金费率数据的调度器，支持按交易对批量更新，优化资源使用。
    * **`funding_rate_storage.py`**:  将资金费率数据存储到持久化存储中。

* **`carry/exchange/implementations/`** 具体交易所实现
    * **`binance/`**:  币安交易所实现
    * **`okx/`**:  OKX交易所实现
        * **`okx_client.py`**: OKX交易所客户端，实现了交易所客户端接口。
        * **`okx_ws.py`**: OKX WebSocket客户端，支持安全的重复订阅和并发处理。
        * **`okx_funding.py`**: OKX资金费率管理，实现了资金费率管理接口。
        * **`okx_position_rest.py`**: 通过REST API获取OKX持仓信息。
        * **`okx_position_ws.py`**: 通过WebSocket获取OKX持仓信息。

#### `carry/trading/` 交易策略

* **`carry/trading/strategy/`** 策略实现
    * **`base_strategy.py`**:  基础策略类，定义了策略的通用接口和方法。
    * **`exchange_funding_strategy.py`**:  资金费率套利策略，基于资金费率差异进行套利。
    * **`strategy_manager.py`**:  策略管理器，负责管理和协调多个交易所的策略。

* **`carry/trading/monitor/`** 监控模块
    * **`position_monitor.py`**:  持仓监控器，监控持仓变化并触发相应的操作。
    * **`position_force_checker.py`**:  强制持仓检查器，定期检查持仓是否符合预期。

* **`carry/trading/position/`** 持仓管理
    * **`position_adjuster.py`**:  持仓调整器，根据策略调整持仓。
    * **`position_manager.py`**:  持仓管理器，管理持仓信息。

* **`carry/trading/opportunity/`** 机会检测
    * **`opportunity_detector.py`**:  套利机会检测器，检测市场中的套利机会。

* **`carry/trading/trade/`** 交易执行
    * **`trade_executor.py`**:  交易执行器，执行交易操作。

* **`carry/trading/task/`** 任务管理
    * **`pending_tasks_manager.py`**:  待处理任务管理器，采用单例模式，按交易所隔离管理待建仓和待平仓任务。

#### `carry/utils/` 工具函数

* **`data_directory.py`**:  提供数据目录的管理，例如读取和写入数据文件。
* **`ding_talk_manager.py`**:  钉钉消息管理器，采用单例模式，为系统提供统一的钉钉消息发送功能。

## 安装和使用

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 配置交易所 API
    - 在 `config.yaml` 中配置 API 密钥 - 设置交易参数

3. 启动程序

```bash
./carry.sh
```

## 开发计划

1. [ ] 支持更多交易所
2. [ ] 添加回测功能
3. [ ] 优化订单执行策略
4. [ ] 添加图形化界面
5. [ ] 实现更多套利策略

## TODO

1. ~~ws 高频获取持仓信息,判断持仓保证金率, 及时执行平仓操作~~
2. 交易对通过market_data 的方式来提取, 不再通过配置方式.提高池子更新频率
   TODO: 未最终完成, 目前暂时使用配置.[已完成]
    - 对高频率场景,单独使用ws,获取持仓信息(支持该分组单独停止/单独启动, 启动时传入新的交易对)
3. ExchangeFundingStrategy 中
   ``` python
   self.pending_tasks = {
        'open_positions': [],  # 待建仓任务
        'close_positions': [],  # 待平仓任务
        'last_funding_time': None  # 上次资金费率更新时间
    }
    ```
   通过全局文件的形式来管理. 同时,交易时发生订单超时取消的场景,需要继续放入到self.pending_tasks,等待合适的机会执行

    + 订单超时
    + 订单取消
    + 订单失败
4. ~~轮动调仓时, 暂时拒绝持仓监控,不要清空持仓~~
    ```shell
   2025-03-14 23:53:04,065 [position_manager.py:233] - INFO - 清空 OKX 的所有持仓
   ```