#!/usr/bin/env python3
"""
项目重构后的功能测试脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试核心模块导入"""
    print("🧪 测试模块导入...")

    tests = [
        ("常量模块", "from crypt_carry.constant.direction_type import DirectionType"),
        ("配置模块", "from crypt_carry.config.config_loader import ConfigLoader"),
        ("工具模块", "from crypt_carry.utils.ding_talk_manager import DingTalkManager"),
        ("策略模块", "from crypt_carry.strategies.strategy_manager import StrategyManager"),
        ("交易所基础", "from crypt_carry.core.exchange.base.exchange_client import ExchangeClient"),
        ("交易所工厂", "from crypt_carry.core.exchange.factory.exchange_factory import ExchangeFactory"),
        ("Binance客户端", "from crypt_carry.core.providers.binance.binance_client import BinanceClient"),
        ("OKX客户端", "from crypt_carry.core.providers.okx.okx_client import OkxClient"),
        ("Web应用", "from crypt_carry.web.app import create_app"),
    ]

    success_count = 0
    for name, import_stmt in tests:
        try:
            exec(import_stmt)
            print(f"  ✅ {name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name}: 导入失败 - {e}")

    print(f"\n📊 导入测试结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")

    try:
        # 测试配置加载
        from crypt_carry.config.config_loader import ConfigLoader
        config = ConfigLoader()
        print("  ✅ 配置加载: 成功")

        # 测试常量
        from crypt_carry.constant.direction_type import DirectionType
        direction = DirectionType.LONG_SPOT_SHORT_FUTURES
        print(f"  ✅ 常量使用: {direction}")

        # 测试Web应用创建
        from crypt_carry.web.app import create_app
        app = create_app()
        print("  ✅ Web应用创建: 成功")

        return True

    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False

def test_web_api():
    """测试Web API"""
    print("\n🧪 测试Web API...")

    try:
        from crypt_carry.web.app import create_app
        app = create_app()

        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            print(f"  ✅ 主页访问: 状态码 {response.status_code}")

            # 测试API端点
            response = client.get('/api/hedging/strategies')
            print(f"  ✅ 策略API: 状态码 {response.status_code}")

            response = client.get('/api/exchanges/status')
            print(f"  ✅ 交易所状态API: 状态码 {response.status_code}")

        return True

    except Exception as e:
        print(f"  ❌ Web API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始项目重构后的功能测试\n")

    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("Web API", test_web_api),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{'=' * 50}")
        print(f"测试: {test_name}")
        print('=' * 50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")

    # 输出总结
    print(f"\n{'=' * 50}")
    print("📋 测试总结")
    print('=' * 50)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed / total * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目重构成功！")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
