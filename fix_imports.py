#!/usr/bin/env python3
"""
修复项目中的导入路径脚本
将所有 carry. 开头的导入替换为 crypt_carry. 开头的导入
"""
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """修复单个文件中的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 替换导入语句
        # from carry.xxx import yyy -> from crypt_carry.core.xxx import yyy

        # 特殊处理 implementations -> providers
        content = re.sub(
            r'from carry\.exchange\.implementations\.',
            'from crypt_carry.core.providers.',
            content
        )
        content = re.sub(
            r'import carry\.exchange\.implementations\.',
            'import crypt_carry.core.providers.',
            content
        )

        # 修复已经部分修复的路径
        content = re.sub(
            r'from crypt_carry\.core\.exchange\.implementations\.',
            'from crypt_carry.core.providers.',
            content
        )
        content = re.sub(
            r'import crypt_carry\.core\.exchange\.implementations\.',
            'import crypt_carry.core.providers.',
            content
        )

        content = re.sub(
            r'from carry\.exchange\.',
            'from crypt_carry.core.exchange.',
            content
        )
        content = re.sub(
            r'from carry\.trading\.',
            'from crypt_carry.core.trading.',
            content
        )
        content = re.sub(
            r'from carry\.constant\.',
            'from crypt_carry.constant.',
            content
        )
        content = re.sub(
            r'from carry\.config\.',
            'from crypt_carry.config.',
            content
        )
        content = re.sub(
            r'from carry\.utils\.',
            'from crypt_carry.utils.',
            content
        )

        # import carry.xxx -> import crypt_carry.core.xxx
        content = re.sub(
            r'import carry\.exchange\.',
            'import crypt_carry.core.exchange.',
            content
        )
        content = re.sub(
            r'import carry\.trading\.',
            'import crypt_carry.core.trading.',
            content
        )
        content = re.sub(
            r'import carry\.constant\.',
            'import crypt_carry.constant.',
            content
        )
        content = re.sub(
            r'import carry\.config\.',
            'import crypt_carry.config.',
            content
        )
        content = re.sub(
            r'import carry\.utils\.',
            'import crypt_carry.utils.',
            content
        )

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修复: {file_path}")
            return True
        return False

    except Exception as e:
        print(f"修复文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent
    src_dir = project_root / "src" / "crypt_carry"

    # 需要修复的文件扩展名
    extensions = ['.py']

    fixed_count = 0
    total_count = 0

    # 遍历所有Python文件
    for ext in extensions:
        for file_path in src_dir.rglob(f"*{ext}"):
            if file_path.is_file():
                total_count += 1
                if fix_imports_in_file(file_path):
                    fixed_count += 1

    print(f"\n修复完成！")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")

if __name__ == "__main__":
    main()
