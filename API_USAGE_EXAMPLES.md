# API使用示例

## 📍 **新的API URL结构说明**

为了解决URL路径中斜杠的问题，我们将交易对参数拆分为两个独立的参数：
- `symbol`: 基础货币符号 (如: BTC, ETH, SOL)
- `quote`: 计价货币符号 (如: USDT, USDC, BTC)

## 🔧 **API端点列表**

### **交易所API** (`/api/exchanges/`)

#### 1. 获取交易所列表
```bash
GET /api/exchanges
```

**响应示例:**
```json
[
  {
    "id": "binance",
    "name": "币安",
    "spot_enabled": true,
    "futures_enabled": true
  },
  {
    "id": "okx", 
    "name": "OKX",
    "spot_enabled": true,
    "futures_enabled": true
  }
]
```

#### 2. 获取交易对列表
```bash
GET /api/exchanges/{exchange_id}/symbols
```

**示例:**
```bash
curl http://localhost:5001/api/exchanges/binance/symbols
```

#### 3. 获取市场信息 ⭐ **新结构**
```bash
GET /api/exchanges/{exchange_id}/markets/{symbol}/{quote}
```

**示例:**
```bash
# ✅ 新的URL结构 (推荐)
curl http://localhost:5001/api/exchanges/binance/markets/BTC/USDT
curl http://localhost:5001/api/exchanges/okx/markets/ETH/USDC
curl http://localhost:5001/api/exchanges/binance/markets/SOL/BTC
```

**响应示例:**
```json
{
  "exchange_id": "binance",
  "symbol": "BTC",
  "quote": "USDT", 
  "full_symbol": "BTC/USDT",
  "price_precision": 2,
  "amount_precision": 4,
  "min_amount": 0.001,
  "min_cost": 10.0,
  "maker_fee": 0.001,
  "taker_fee": 0.001,
  "last_price": 50000.0
}
```

#### 4. 获取资金费率 ⭐ **新结构**
```bash
GET /api/exchanges/{exchange_id}/funding_rate/{symbol}/{quote}
```

**示例:**
```bash
# ✅ 新的URL结构 (推荐)
curl http://localhost:5001/api/exchanges/binance/funding_rate/BTC/USDT
curl http://localhost:5001/api/exchanges/okx/funding_rate/ETH/USDC
```

**响应示例:**
```json
{
  "exchange_id": "binance",
  "symbol": "BTC",
  "quote": "USDT",
  "full_symbol": "BTC/USDT", 
  "rate": 0.001912,
  "next_funding_time": *************,
  "estimated_rate": 0.002354
}
```

#### 5. 获取所有资金费率
```bash
GET /api/exchanges/{exchange_id}/funding_rates
```

#### 6. 获取账户余额
```bash
GET /api/exchanges/{exchange_id}/balance/{account_type}
```

**示例:**
```bash
curl http://localhost:5001/api/exchanges/binance/balance/spot
curl http://localhost:5001/api/exchanges/binance/balance/futures
```

### **套保策略API** (`/api/hedging/`)

#### 1. 创建套保订单
```bash
POST /api/hedging/orders/hedging
```

#### 2. 创建单独订单
```bash
POST /api/hedging/orders/single
```

#### 3. 获取交易对列表
```bash
GET /api/hedging/config/symbols
```

## 🎯 **新旧API对比**

### **❌ 旧的API结构 (已不支持)**
```bash
# 这些URL会返回404错误
GET /api/exchanges/binance/markets/BTC/USDT     # 斜杠导致路由问题
GET /api/exchanges/binance/markets/BTCUSDT      # 不够灵活
```

### **✅ 新的API结构 (推荐)**
```bash
# 清晰的参数分离
GET /api/exchanges/binance/markets/BTC/USDT     # symbol=BTC, quote=USDT
GET /api/exchanges/binance/markets/ETH/USDC     # symbol=ETH, quote=USDC
GET /api/exchanges/binance/markets/SOL/BTC      # symbol=SOL, quote=BTC
```

## 🌟 **新结构的优势**

### 1. **解决URL路径问题**
- ❌ 旧方式: `/markets/BTC/USDT` → 斜杠导致路由冲突
- ✅ 新方式: `/markets/BTC/USDT` → 清晰的参数分离

### 2. **更好的RESTful设计**
- 符合REST API设计原则
- 参数语义更清晰
- 更容易理解和使用

### 3. **更灵活的交易对支持**
- 支持任意的symbol/quote组合
- 大小写敏感，保持原始格式
- 易于扩展新的交易对

### 4. **更好的开发体验**
- URL结构直观易懂
- 参数验证更简单
- 错误处理更清晰

## 🧪 **测试示例**

### JavaScript/TypeScript
```javascript
// 获取BTC/USDT市场信息
const response = await fetch('/api/exchanges/binance/markets/BTC/USDT');
const marketInfo = await response.json();
console.log(marketInfo.full_symbol); // "BTC/USDT"

// 获取ETH/USDC资金费率
const fundingResponse = await fetch('/api/exchanges/okx/funding_rate/ETH/USDC');
const fundingRate = await fundingResponse.json();
console.log(fundingRate.rate); // 0.001234
```

### Python
```python
import requests

# 获取市场信息
response = requests.get('http://localhost:5001/api/exchanges/binance/markets/BTC/USDT')
market_info = response.json()
print(f"交易对: {market_info['full_symbol']}")
print(f"当前价格: {market_info['last_price']}")

# 获取资金费率
response = requests.get('http://localhost:5001/api/exchanges/binance/funding_rate/BTC/USDT')
funding_rate = response.json()
print(f"资金费率: {funding_rate['rate']}")
```

### cURL
```bash
# 测试不同的交易对
curl http://localhost:5001/api/exchanges/binance/markets/BTC/USDT
curl http://localhost:5001/api/exchanges/okx/markets/ETH/USDC  
curl http://localhost:5001/api/exchanges/binance/markets/SOL/BTC

# 测试大小写
curl http://localhost:5001/api/exchanges/binance/markets/btc/usdt  # 小写
curl http://localhost:5001/api/exchanges/binance/markets/BTC/USDT  # 大写
```

## 📖 **API文档**

完整的API文档可以在以下地址查看：
- **Swagger UI**: http://localhost:5001/api/docs/
- **JSON规范**: http://localhost:5001/api/swagger.json

## 🔧 **开发建议**

1. **使用新的URL结构**: 所有新开发都应使用symbol/quote分离的API
2. **参数验证**: 确保symbol和quote参数的有效性
3. **错误处理**: 处理404和其他HTTP错误状态
4. **缓存策略**: 对于市场数据可以考虑适当的缓存
5. **测试覆盖**: 测试不同的交易对组合和边界情况
