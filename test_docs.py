#!/usr/bin/env python3
"""
测试API文档的简单脚本
"""
import requests

def test_api_docs():
    """测试API文档是否可访问"""
    print("🔍 测试API文档...")
    
    # 测试文档页面
    try:
        response = requests.get("http://localhost:5001/api/docs/")
        print(f"文档页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API文档页面可以正常访问")
            
            # 检查是否包含Swagger内容
            content = response.text
            if 'swagger' in content.lower() or 'api' in content.lower():
                print("✅ 页面包含API文档内容")
            else:
                print("⚠️  页面可能不包含完整的API文档内容")
                
            return True
        else:
            print(f"❌ API文档页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 访问API文档时出错: {e}")
        return False

def test_swagger_json():
    """测试Swagger JSON是否可访问"""
    print("\n🔍 测试Swagger JSON...")
    
    try:
        response = requests.get("http://localhost:5001/api/swagger.json")
        print(f"Swagger JSON状态码: {response.status_code}")
        
        if response.status_code == 200:
            json_data = response.json()
            print("✅ Swagger JSON可以正常访问")
            print(f"API标题: {json_data.get('info', {}).get('title', 'N/A')}")
            print(f"API版本: {json_data.get('info', {}).get('version', 'N/A')}")
            
            # 检查是否有路径定义
            paths = json_data.get('paths', {})
            print(f"定义的API路径数量: {len(paths)}")
            
            if paths:
                print("API路径列表:")
                for path in list(paths.keys())[:5]:  # 只显示前5个
                    print(f"  - {path}")
                if len(paths) > 5:
                    print(f"  ... 还有 {len(paths) - 5} 个路径")
            
            return True
        else:
            print(f"❌ Swagger JSON访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 访问Swagger JSON时出错: {e}")
        return False

def test_api_endpoints():
    """测试主要API端点是否在文档中"""
    print("\n🔍 测试API端点文档...")
    
    try:
        response = requests.get("http://localhost:5001/api/swagger.json")
        if response.status_code != 200:
            print("❌ 无法获取Swagger JSON")
            return False
            
        json_data = response.json()
        paths = json_data.get('paths', {})
        
        # 检查关键端点
        expected_endpoints = [
            '/hedging/orders/hedging',
            '/hedging/orders/single', 
            '/hedging/strategies',
            '/exchanges'
        ]
        
        found_endpoints = []
        missing_endpoints = []
        
        for endpoint in expected_endpoints:
            if endpoint in paths:
                found_endpoints.append(endpoint)
                print(f"✅ 找到端点: {endpoint}")
            else:
                missing_endpoints.append(endpoint)
                print(f"❌ 缺少端点: {endpoint}")
        
        print(f"\n📊 端点统计:")
        print(f"找到: {len(found_endpoints)}/{len(expected_endpoints)}")
        
        if missing_endpoints:
            print(f"缺少的端点: {missing_endpoints}")
            return False
        else:
            print("✅ 所有关键端点都已在文档中定义")
            return True
            
    except Exception as e:
        print(f"❌ 测试API端点时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试API文档...")
    
    tests = [
        ("API文档页面", test_api_docs),
        ("Swagger JSON", test_swagger_json),
        ("API端点文档", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        success = test_func()
        results.append((test_name, success))
    
    print("\n📊 测试结果汇总:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有文档测试通过！")
        print("📖 您可以访问 http://localhost:5001/api/docs/ 查看完整的API文档")
    else:
        print("\n⚠️  部分文档测试失败，请检查配置")

if __name__ == "__main__":
    main()
