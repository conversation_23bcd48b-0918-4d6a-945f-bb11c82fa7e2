def format_basis_rate(rate: float, precision: int = 6) -> str:
    """
    将基差率转换为百分比形式，不使用科学计数法

    Args:
        rate (float): 基差率（小数形式）
        precision (int): 小数点后保留的位数

    Returns:
        str: 格式化后的百分比字符串，例如: "0.123456%"
    """
    return "{:.{}f}%".format(rate * 100, precision)


def calculate_basis_rate(spot_price: float, futures_price: float) -> float:
    """计算基差率

    Args:
        spot_price: 现货价格
        futures_price: 合约价格
            
    Returns:
        float: 基差率
    """
    if spot_price <= 0:
        return 0
    return (futures_price - spot_price) / spot_price


def calculate_arbitrage_with_slippage(
        spot_price, future_price, total_capital, leverage, spot_fee_rate, future_fee_rate,
        funding_rate=0, slippage_rate=0, funding_fee_multiple=1
):
    """
    优化的期现套利收益计算脚本，加入价格滑点（磨损）影响，并计算盈亏平衡基差率
    :param spot_price: 现货价格
    :param future_price: 期货价格
    :param total_capital: 总本金
    :param leverage: 杠杆倍数
    :param spot_fee_rate: 现货交易手续费率（如0.001表示0.1%）
    :param future_fee_rate: 期货交易手续费率（如0.0002表示0.02%）
    :param funding_rate: 资金费率（如0.0001表示0.01%），正值表示资金费收益，负值表示资金费成本
    :param slippage_rate: 滑点比例（如0.001表示0.1%）
    :param funding_fee_multiple: 多次资金费率收益的次数，默认为1
    :return: 打印各项收益和费用明细，并输出盈亏平衡基差率、利润结构可视化建议及套利评价
    """
    # 资金分配（基于杠杆资金模型）
    spot_capital = total_capital * leverage / (leverage + 1)  # 现货可用金额
    future_capital = spot_capital / leverage  # 合约保证金
    print(f"spot_capital: {spot_capital}, future_capital: {future_capital}")

    # 计算交易规模 - 保证现货和期货交易的数量相等，实现完全对冲
    spot_trade_size = spot_capital / spot_price  # 现货买入数量
    future_trade_size = spot_trade_size  # 期货卖空数量与现货数量相等
    future_notional_value = future_trade_size * future_price  # 期货名义价值

    print(f"spot_trade_size: {spot_trade_size}, future_trade_size: {future_trade_size}")
    print(f"spot_notional: {spot_capital}, future_notional: {future_notional_value}")

    # 加入滑点影响后的价格：现货买入价格上升，期货卖出价格下降
    adjusted_spot_price = spot_price * (1 + slippage_rate)
    adjusted_future_price = future_price * (1 - slippage_rate)
    final_price = future_price  # 假设最终价格仍为期货当前价格

    # 收益计算（未排除手续费），现货和期货收益互补
    spot_profit = spot_trade_size * (final_price - adjusted_spot_price)
    future_profit = future_trade_size * (adjusted_future_price - final_price)
    total_profit = spot_profit + future_profit

    # 手续费计算 - 基于实际交易金额
    spot_fee = spot_capital * spot_fee_rate * 2
    future_fee = future_notional_value * future_fee_rate * 2

    # 资金费率计算（多次资金费率收益考虑 funding_fee_multiple）
    funding_income = future_notional_value * funding_rate * funding_fee_multiple if funding_rate > 0 else 0
    funding_cost = future_notional_value * abs(funding_rate) * funding_fee_multiple if funding_rate < 0 else 0

    # 总费用计算
    total_fee = spot_fee + future_fee + funding_cost
    print(
        f"spot_fee: {spot_fee:.2f} USDT, future_fee: {future_fee:.2f} USDT, funding_cost: {funding_cost:.2f} USDT, funding_income: {funding_income:.2f} USDT")

    # 净收益计算：总收益减去总费用，再加上资金费收益
    net_profit = total_profit - total_fee + funding_income

    basis_rate = calculate_basis_rate(spot_price, future_price)
    print(f"总收益（未排除手续费）: {total_profit:.2f} USDT")
    print(f"现货手续费: {spot_fee:.2f} USDT")
    print(f"期货手续费: {future_fee:.2f} USDT")
    if funding_rate > 0:
        print(f"资金费收益: {funding_income:.2f} USDT")
    else:
        print(f"资金费成本: {funding_cost:.2f} USDT")
    print(f"滑点损失（基于总本金）: {slippage_rate * total_capital:.2f} USDT")
    print(f"三项费用综合: {total_fee:.2f} USDT")
    print(f"净收益: {net_profit:.2f} USDT")
    print(f"收益率: {(net_profit / total_capital * 100):.3f}%")
    print(f"基差率: {format_basis_rate(basis_rate)}")

    # 调整后的盈亏平衡计算：
    # 根据公式：breakeven_future_price = [spot_price*(1+slippage_rate) + (total_fee - funding_income)/spot_trade_size] / (1 - slippage_rate)
    breakeven_future_price = (spot_price * (1 + slippage_rate) + (total_fee - funding_income) / spot_trade_size) / (
                1 - slippage_rate)
    breakeven_basis_rate = (breakeven_future_price - spot_price) / spot_price
    print(f"盈亏平衡基差率: {format_basis_rate(breakeven_basis_rate)}")

    # 利润结构可视化建议（打印说明）：
    print("\n【利润结构可视化建议】")
    print("1. 绘制现货与期货交易金额、手续费、资金费和滑点成本的条形图，展示各项费用占总交易金额的比例。")
    print("2. 绘制不同基差率下的净收益变化曲线，确定最佳套利区间。")
    print("3. 绘制盈亏平衡基差率线，帮助判断是否进入套利操作。")

    # 根据计算结果给出是否值得进行套利的评价
    if net_profit > 0:
        print("\n【套利评价】：在当前参数下，套利操作是有正收益的，建议考虑执行套利策略。")
    else:
        print("\n【套利评价】：在当前参数下，套利操作净收益为负，不建议执行套利策略。")
    
    return {
        "total_profit": total_profit,
        "spot_fee": spot_fee,
        "future_fee": future_fee,
        "funding_cost": funding_cost,
        "funding_income": funding_income,
        "total_fee": total_fee,
        "slippage_loss": slippage_rate * total_capital,
        "net_profit": net_profit,
        "net_return_rate": (net_profit / total_capital * 100),
        "basis_rate": basis_rate,
        "breakeven_basis_rate": breakeven_basis_rate,
    }


# 示例参数
spot_price = 1.1072  # 现货价格
future_price = 1.1072  # 期货价格
total_capital = 10000  # 总本金（USDT）
leverage = 3  # 杠杆倍数
spot_fee_rate = 0.001  # 现货交易手续费率（0.1%）
future_fee_rate = 0.0002  # 期货交易手续费率（0.02%）
funding_rate = 0.0001  # 期货资金费率（0.028%），正值表示收益
slippage_rate = 0.001  # 滑点比例（0.1%）
funding_fee_multiple = 6  # 假设套利期间可以吃2次资金费率收益

# 计算收益和费用
result_with_slippage = calculate_arbitrage_with_slippage(
    spot_price, future_price, total_capital, leverage, spot_fee_rate, future_fee_rate,
    funding_rate, slippage_rate, funding_fee_multiple
)