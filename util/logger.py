import logging
import logging.handlers
import os
import sys
from pathlib import Path

import yaml

# 默认配置
DEFAULT_CONFIG = {
    'LOGGING': {
        'LEVEL': 'INFO',
        'FORMAT': '%(asctime)s [%(filename)s:%(lineno)d] - %(levelname)s - %(message)s',
        'CONSOLE': {
            'ENABLED': True
        },
        'FILE': {
            'ENABLED': True,
            'PATH': 'logs/app.log',
            'MAX_BYTES': 10 * 1024 * 1024,  # 10MB
            'BACKUP_COUNT': 5
        },
        'TIMED': {
            'ENABLED': True,
            'PATH': 'logs/app.daily.log',
            'WHEN': 'midnight',
            'INTERVAL': 1,
            'BACKUP_COUNT': 7,
            'UTC': False
        }
    }
}

def setup_logger(strategy_type: str = None):
    """设置日志配置

    Args:
        strategy_type: 策略类型，用于日志文件分类 (hedging, funding_rate, web等)
    """
    # 获取项目根目录
    try:
        from src.crypt_carry.utils.paths import get_project_root
        project_root = str(get_project_root())
    except ImportError:
        # 如果无法导入paths模块，使用原来的方法作为备选
        project_root = str(Path(__file__).parent.parent)

    # 尝试加载配置文件
    config_path = os.path.join(project_root, 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
            log_config = config_data.get('LOGGING', DEFAULT_CONFIG['LOGGING'])
    except (FileNotFoundError, KeyError) as e:
        print(f"未找到日志配置文件 {config_path} 或配置无效: {str(e)}，使用默认配置", file=sys.stderr)
        log_config = DEFAULT_CONFIG['LOGGING']

    # 设置日志级别
    log_level = getattr(logging, log_config.get('LEVEL', 'INFO'))
    log_format = log_config.get('FORMAT', DEFAULT_CONFIG['LOGGING']['FORMAT'])

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除所有现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter(log_format)

    # 添加控制台处理器
    console_config = log_config.get('CONSOLE', DEFAULT_CONFIG['LOGGING']['CONSOLE'])
    if console_config.get('ENABLED', True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # 添加文件处理器
    file_config = log_config.get('FILE', DEFAULT_CONFIG['LOGGING']['FILE'])
    if file_config.get('ENABLED', True):
        # 根据策略类型确定日志路径
        if strategy_type:
            log_path = os.path.join(project_root, f'logs/{strategy_type}/app.log')
        else:
            log_path = os.path.join(project_root, file_config.get('PATH', 'logs/app.log'))

        os.makedirs(os.path.dirname(log_path), exist_ok=True)

        # 创建按大小轮转的文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_path,
            maxBytes=file_config.get('MAX_BYTES', 10 * 1024 * 1024),
            backupCount=file_config.get('BACKUP_COUNT', 5),
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 添加按时间轮转的处理器
    timed_config = log_config.get('TIMED', DEFAULT_CONFIG['LOGGING']['TIMED'])
    if timed_config.get('ENABLED', True):
        # 根据策略类型确定日志路径
        if strategy_type:
            timed_path = os.path.join(project_root, f'logs/{strategy_type}/app.daily.log')
        else:
            timed_path = os.path.join(project_root, timed_config.get('PATH', 'logs/app.daily.log'))

        os.makedirs(os.path.dirname(timed_path), exist_ok=True)

        # 创建按时间轮转的文件处理器
        timed_handler = logging.handlers.TimedRotatingFileHandler(
            filename=timed_path,
            when=timed_config.get('WHEN', 'midnight'),
            interval=timed_config.get('INTERVAL', 1),
            backupCount=timed_config.get('BACKUP_COUNT', 7),
            encoding='utf-8',
            utc=timed_config.get('UTC', False)
        )
        timed_handler.setFormatter(formatter)
        root_logger.addHandler(timed_handler)

    return root_logger

# 创建全局日志记录器
logger = setup_logger()

# 为不同策略类型提供便捷函数
def get_hedging_logger():
    """获取套保策略日志记录器"""
    return setup_logger('hedging')

def get_funding_rate_logger():
    """获取资金费率策略日志记录器"""
    return setup_logger('funding_rate')

def get_web_logger():
    """获取Web服务日志记录器"""
    return setup_logger('web')
