"""
日志频率控制工具 - 用于减少日志输出频率，避免日志过于冗余
"""
import logging
import time
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)


class LogThrottler:
    """
    日志频率控制器，用于控制日志输出频率，避免日志过于冗余
    
    使用方法：
    ```python
    from util.log_throttler import LogThrottler
    
    # 创建日志节流器（默认间隔30分钟）
    log_throttler = LogThrottler.get_instance()
    
    # 在需要控制频率的日志输出处（使用默认间隔）
    if log_throttler.should_log("websocket_status"):
        logger.info("WebSocket未连接，尝试重新连接")
        
    # 为特定日志类型设置不同的间隔（20分钟）
    log_throttler.set_interval("position_monitor", 20 * 60)
    
    # 使用自定义间隔检查
    if log_throttler.should_log("position_monitor"):
        logger.debug("开始执行持仓监控")
    ```
    """

    _instances: Dict[str, 'LogThrottler'] = {}

    @classmethod
    def get_instance(cls, name: str = "default", default_interval_seconds: int = 1800) -> 'LogThrottler':
        """
        获取LogThrottler实例（单例模式）
        
        Args:
            name: 实例名称，用于区分不同的日志控制器
            default_interval_seconds: 默认日志输出间隔（秒），默认30分钟
            
        Returns:
            LogThrottler实例
        """
        if name not in cls._instances:
            cls._instances[name] = LogThrottler(default_interval_seconds)
        return cls._instances[name]

    def __init__(self, default_interval_seconds: int = 1800):
        """
        初始化日志频率控制器
        
        Args:
            default_interval_seconds: 默认日志输出间隔（秒），默认30分钟
        """
        self.default_interval_seconds = default_interval_seconds
        self.last_log_times: Dict[str, float] = {}
        self.intervals: Dict[str, int] = {}

    def set_interval(self, key: str, interval_seconds: int) -> None:
        """
        为特定日志键设置自定义间隔时间
        
        Args:
            key: 日志标识
            interval_seconds: 该日志的输出间隔（秒）
        """
        self.intervals[key] = interval_seconds

    def get_interval(self, key: str) -> int:
        """
        获取特定日志键的间隔时间
        
        Args:
            key: 日志标识
            
        Returns:
            该日志的输出间隔（秒）
        """
        return self.intervals.get(key, self.default_interval_seconds)

    def should_log(self, key: str, interval_seconds: Optional[int] = None) -> bool:
        """
        判断指定key的日志是否应该输出
        
        Args:
            key: 日志标识，用于区分不同类型的日志
            interval_seconds: 可选，为本次检查指定特定的间隔时间（不会修改存储的间隔）
            
        Returns:
            是否应该输出日志
        """
        current_time = time.time()
        last_time = self.last_log_times.get(key, 0)

        # 确定使用哪个间隔
        if interval_seconds is not None:
            # 使用传入的间隔（一次性）
            interval = interval_seconds
        else:
            # 使用为该key设置的间隔，如果没有则使用默认间隔
            interval = self.get_interval(key)

        # 如果距离上次日志输出时间超过了设定的间隔，则允许输出
        if current_time - last_time > interval:
            self.last_log_times[key] = current_time
            return True
        return False

    def is_allowed(self, logger_obj: Any, key: str, interval_seconds: Optional[int] = None) -> bool:
        """
        判断是否允许输出日志（不会自动输出日志）
        
        Args:
            logger_obj: 日志对象（用于调试）
            key: 日志标识
            interval_seconds: 可选，为本次检查指定特定的间隔时间
            
        Returns:
            是否允许输出日志
        """
        return self.should_log(key, interval_seconds)

    def log_if_allowed(self, logger_obj: Any, level: str = 'info', key: str = '', message: str = '',
                       interval_seconds: Optional[int] = None, *args, **kwargs) -> None:
        """
        如果允许日志输出，则输出日志
        
        Args:
            logger_obj: 日志对象
            level: 日志级别，如'info', 'warning', 'error'等
            key: 日志标识
            message: 日志消息
            interval_seconds: 可选，为本次检查指定特定的间隔时间
            *args, **kwargs: 传递给日志方法的其他参数
        """
        if self.is_allowed(logger_obj, key, interval_seconds):
            log_method = getattr(logger_obj, level.lower())
            log_method(message, *args, **kwargs)

    def reset(self, key: Optional[str] = None) -> None:
        """
        重置日志输出时间
        
        Args:
            key: 要重置的日志标识，如果为None则重置所有
        """
        if key:
            self.last_log_times.pop(key, None)
        else:
            self.last_log_times.clear()
