def format_funding_rate(rate: float, precision: int = 6) -> str:
    """
    将资金费率转换为百分比形式，不使用科学计数法
    
    Args:
        rate (float): 资金费率（小数形式）
        precision (int): 小数点后保留的位数
    
    Returns:
        str: 格式化后的百分比字符串，例如: "0.123456%"
    """
    return "{:.{}f}%".format(rate * 100, precision)


def format_basis_rate(rate: float, precision: int = 6) -> str:
    """
    将基差率转换为百分比形式，不使用科学计数法
    
    Args:
        rate (float): 基差率（小数形式）
        precision (int): 小数点后保留的位数
    
    Returns:
        str: 格式化后的百分比字符串，例如: "0.123456%"
    """
    return "{:.{}f}%".format(rate * 100, precision)
