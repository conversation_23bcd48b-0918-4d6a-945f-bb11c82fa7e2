#!/bin/bash

# Docker Compose 管理脚本
# 用于管理 Crypt Carry 项目的 Docker 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Docker Compose 文件路径
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yml"

log_info "Docker Compose 管理脚本"
log_info "项目根目录: $PROJECT_ROOT"
log_info "Compose 文件: $COMPOSE_FILE"

# 检查 Docker 和 Docker Compose
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 创建必要的目录和文件
setup_environment() {
    log_info "设置 Docker 环境..."
    
    # 创建目录
    mkdir -p "$SCRIPT_DIR/redis/data"
    mkdir -p "$SCRIPT_DIR/redis/config"
    mkdir -p "$SCRIPT_DIR/redis/logs"
    
    # 创建 Redis 配置文件
    local redis_config="$SCRIPT_DIR/redis/config/redis.conf"
    if [ ! -f "$redis_config" ]; then
        log_info "创建 Redis 配置文件..."
        cat > "$redis_config" << 'EOF'
# Redis 配置文件 - Docker 环境

# 网络配置
bind 0.0.0.0
port 6379
timeout 300

# 安全配置
requirepass crypt_carry_redis_2024

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF 配置
appendonly yes
appendfsync everysec

# 日志配置
loglevel notice
logfile /var/log/redis/redis.log

# 其他配置
tcp-keepalive 300
databases 16

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_crypt_carry_redis_2024"
EOF
        log_success "Redis 配置文件创建完成"
    fi
    
    # 创建环境变量文件
    local env_file="$SCRIPT_DIR/.env"
    if [ ! -f "$env_file" ]; then
        log_info "创建环境变量文件..."
        cat > "$env_file" << 'EOF'
# Redis 配置
REDIS_PASSWORD=crypt_carry_redis_2024

# Redis Commander 配置
HTTP_USER=admin
HTTP_PASSWORD=crypt_carry_admin_2024

# 网络配置
REDIS_PORT=6379
REDIS_COMMANDER_PORT=8081
REDIS_INSIGHT_PORT=8001
EOF
        log_success "环境变量文件创建完成"
    fi
    
    log_success "Docker 环境设置完成"
}

# 获取 Docker Compose 命令
get_compose_cmd() {
    if command -v docker-compose >/dev/null 2>&1; then
        echo "docker-compose"
    else
        echo "docker compose"
    fi
}

# 启动服务
start_services() {
    local profile="${1:-}"
    
    log_info "启动 Docker 服务..."
    
    check_docker
    setup_environment
    
    cd "$SCRIPT_DIR"
    
    local compose_cmd=$(get_compose_cmd)
    
    if [ -n "$profile" ]; then
        log_info "启动服务 (包含 $profile 配置)..."
        $compose_cmd --profile "$profile" up -d
    else
        log_info "启动基础服务..."
        $compose_cmd up -d redis
    fi
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    log_info "检查服务状态..."
    $compose_cmd ps
    
    # 测试 Redis 连接
    if docker exec crypt_carry_redis redis-cli -a "crypt_carry_redis_2024" ping >/dev/null 2>&1; then
        log_success "Redis 服务启动成功"
        
        log_info ""
        log_info "🔗 服务地址:"
        log_info "  Redis: localhost:6379"
        log_info "  密码: crypt_carry_redis_2024"
        
        if [ "$profile" = "monitoring" ]; then
            log_info "  Redis Commander: http://localhost:8081"
            log_info "  Redis Insight: http://localhost:8001"
            log_info "  管理员账号: admin / crypt_carry_admin_2024"
        fi
        
        log_info ""
        log_info "📋 管理命令:"
        log_info "  查看状态: $0 status"
        log_info "  查看日志: $0 logs"
        log_info "  连接Redis: $0 cli"
        log_info "  停止服务: $0 stop"
    else
        log_error "Redis 服务启动失败"
        $compose_cmd logs redis
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止 Docker 服务..."
    
    cd "$SCRIPT_DIR"
    local compose_cmd=$(get_compose_cmd)
    
    $compose_cmd down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    local profile="${1:-}"
    log_info "重启 Docker 服务..."
    stop_services
    sleep 2
    start_services "$profile"
}

# 查看服务状态
check_status() {
    log_info "检查 Docker 服务状态..."
    
    cd "$SCRIPT_DIR"
    local compose_cmd=$(get_compose_cmd)
    
    echo ""
    echo "=== 容器状态 ==="
    $compose_cmd ps
    
    echo ""
    echo "=== 容器统计 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker ps --filter "label=com.docker.compose.project=docker" --format "{{.Names}}" 2>/dev/null || echo "")
    
    # 检查 Redis 健康状态
    if docker ps --filter "name=crypt_carry_redis" --filter "status=running" | grep -q crypt_carry_redis; then
        echo ""
        echo "=== Redis 信息 ==="
        docker exec crypt_carry_redis redis-cli -a "crypt_carry_redis_2024" info server 2>/dev/null | grep -E "redis_version|uptime_in_seconds|connected_clients" || true
    fi
}

# 查看日志
show_logs() {
    local service="${1:-}"
    
    cd "$SCRIPT_DIR"
    local compose_cmd=$(get_compose_cmd)
    
    if [ -n "$service" ]; then
        log_info "显示 $service 服务日志 (按 Ctrl+C 退出):"
        $compose_cmd logs -f "$service"
    else
        log_info "显示所有服务日志 (按 Ctrl+C 退出):"
        $compose_cmd logs -f
    fi
}

# 连接到 Redis CLI
connect_cli() {
    if docker ps --filter "name=crypt_carry_redis" --filter "status=running" | grep -q crypt_carry_redis; then
        log_info "连接到 Redis CLI (输入 'exit' 退出):"
        docker exec -it crypt_carry_redis redis-cli -a "crypt_carry_redis_2024"
    else
        log_error "Redis 容器未运行"
    fi
}

# 清理所有数据
cleanup_all() {
    log_warning "这将删除所有容器、卷和数据，是否继续? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "清理所有 Docker 资源..."
        
        cd "$SCRIPT_DIR"
        local compose_cmd=$(get_compose_cmd)
        
        # 停止并删除容器、网络、卷
        $compose_cmd down -v --remove-orphans
        
        # 删除数据目录
        rm -rf "$SCRIPT_DIR/redis/data"/*
        rm -rf "$SCRIPT_DIR/redis/logs"/*
        
        log_success "清理完成"
    else
        log_info "操作已取消"
    fi
}

# 显示环境配置
show_env_config() {
    log_info "环境变量配置 (添加到项目 .env 文件):"
    echo ""
    echo "# Redis Docker 配置"
    echo "REDIS_HOST=localhost"
    echo "REDIS_PORT=6379"
    echo "REDIS_PASSWORD=crypt_carry_redis_2024"
    echo "REDIS_DB=0"
    echo ""
    echo "# Celery 配置"
    echo "CELERY_BROKER_URL=redis://:crypt_carry_redis_2024@localhost:6379/0"
    echo "CELERY_RESULT_BACKEND=redis://:crypt_carry_redis_2024@localhost:6379/0"
}

# 更新服务
update_services() {
    log_info "更新 Docker 服务..."
    
    cd "$SCRIPT_DIR"
    local compose_cmd=$(get_compose_cmd)
    
    # 拉取最新镜像
    $compose_cmd pull
    
    # 重新创建容器
    $compose_cmd up -d --force-recreate
    
    log_success "服务更新完成"
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_services "${2:-}"
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services "${2:-}"
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs "${2:-}"
        ;;
    "cli")
        connect_cli
        ;;
    "cleanup")
        cleanup_all
        ;;
    "env")
        show_env_config
        ;;
    "update")
        update_services
        ;;
    "monitoring")
        start_services "monitoring"
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令] [选项]"
        echo ""
        echo "命令:"
        echo "  start [profile]  启动服务 (默认)"
        echo "  stop             停止服务"
        echo "  restart [profile] 重启服务"
        echo "  status           查看服务状态"
        echo "  logs [service]   查看日志"
        echo "  cli              连接到 Redis CLI"
        echo "  cleanup          清理所有数据"
        echo "  env              显示环境变量配置"
        echo "  update           更新服务"
        echo "  monitoring       启动包含监控工具的服务"
        echo "  help             显示此帮助信息"
        echo ""
        echo "Profile 选项:"
        echo "  monitoring       包含 Redis Commander 和 Redis Insight"
        echo ""
        echo "示例:"
        echo "  $0 start                    # 启动基础服务"
        echo "  $0 start monitoring         # 启动包含监控的服务"
        echo "  $0 logs redis              # 查看 Redis 日志"
        echo "  $0 cli                     # 连接 Redis"
        echo "  $0 monitoring              # 启动监控服务"
        ;;
    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
