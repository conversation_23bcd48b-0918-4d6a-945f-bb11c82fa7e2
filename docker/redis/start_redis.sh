#!/bin/bash

# Redis Docker 容器管理脚本
# 用于在 Docker 中运行 Redis 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Docker 配置
CONTAINER_NAME="crypt_carry_redis"
REDIS_IMAGE="redis:7-alpine"
REDIS_PORT="6379"
REDIS_PASSWORD="crypt_carry_redis_2024"
DATA_DIR="$PROJECT_ROOT/docker/redis/data"
CONFIG_DIR="$PROJECT_ROOT/docker/redis/config"
LOG_DIR="$PROJECT_ROOT/docker/redis/logs"

log_info "Redis Docker 容器管理脚本"
log_info "容器名称: $CONTAINER_NAME"
log_info "Redis 镜像: $REDIS_IMAGE"
log_info "数据目录: $DATA_DIR"

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装，请先安装 Docker"
        log_info "macOS: brew install --cask docker"
        log_info "Ubuntu: sudo apt install docker.io"
        log_info "或访问: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    mkdir -p "$DATA_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置目录权限
    chmod 755 "$DATA_DIR" "$CONFIG_DIR" "$LOG_DIR"
    log_success "目录创建完成"
}

# 创建 Redis 配置文件
create_redis_config() {
    local config_file="$CONFIG_DIR/redis.conf"
    
    if [ ! -f "$config_file" ]; then
        log_info "创建 Redis 配置文件..."
        cat > "$config_file" << EOF
# Redis 配置文件 - Crypt Carry 项目

# 网络配置
bind 0.0.0.0
port 6379
timeout 300

# 安全配置
requirepass $REDIS_PASSWORD

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF 配置
appendonly yes
appendfsync everysec

# 日志配置
loglevel notice
logfile /var/log/redis/redis.log

# 其他配置
tcp-keepalive 300
databases 16

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_$REDIS_PASSWORD"
EOF
        log_success "Redis 配置文件创建完成: $config_file"
    else
        log_info "Redis 配置文件已存在: $config_file"
    fi
}

# 启动 Redis 容器
start_redis() {
    log_info "启动 Redis Docker 容器..."
    
    # 检查环境
    check_docker
    create_directories
    create_redis_config
    
    # 检查容器是否已存在
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "容器 $CONTAINER_NAME 已存在"
        
        # 检查容器是否正在运行
        if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            log_warning "容器 $CONTAINER_NAME 已在运行"
            return 0
        else
            log_info "启动现有容器..."
            docker start "$CONTAINER_NAME"
        fi
    else
        log_info "创建并启动新容器..."
        
        # 拉取 Redis 镜像
        log_info "拉取 Redis 镜像: $REDIS_IMAGE"
        docker pull "$REDIS_IMAGE"
        
        # 启动容器
        docker run -d \
            --name "$CONTAINER_NAME" \
            --restart unless-stopped \
            -p "$REDIS_PORT:6379" \
            -v "$DATA_DIR:/data" \
            -v "$CONFIG_DIR:/usr/local/etc/redis" \
            -v "$LOG_DIR:/var/log/redis" \
            "$REDIS_IMAGE" \
            redis-server /usr/local/etc/redis/redis.conf
    fi
    
    # 等待容器启动
    sleep 3
    
    # 验证容器状态
    if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_success "Redis 容器启动成功"
        
        # 测试连接
        if docker exec "$CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
            log_success "Redis 服务连接正常"
        else
            log_warning "Redis 服务连接测试失败"
        fi
        
        # 显示连接信息
        log_info ""
        log_info "🔗 Redis 连接信息:"
        log_info "  主机: localhost"
        log_info "  端口: $REDIS_PORT"
        log_info "  密码: $REDIS_PASSWORD"
        log_info ""
        log_info "📁 数据目录: $DATA_DIR"
        log_info "⚙️  配置文件: $CONFIG_DIR/redis.conf"
        log_info "📋 日志目录: $LOG_DIR"
        log_info ""
        log_info "🔧 管理命令:"
        log_info "  查看状态: $0 status"
        log_info "  查看日志: $0 logs"
        log_info "  连接Redis: $0 cli"
        log_info "  停止服务: $0 stop"
    else
        log_error "Redis 容器启动失败"
        exit 1
    fi
}

# 停止 Redis 容器
stop_redis() {
    log_info "停止 Redis Docker 容器..."
    
    if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop "$CONTAINER_NAME"
        log_success "Redis 容器已停止"
    else
        log_warning "Redis 容器未运行"
    fi
}

# 重启 Redis 容器
restart_redis() {
    log_info "重启 Redis Docker 容器..."
    stop_redis
    sleep 2
    start_redis
}

# 查看容器状态
check_status() {
    log_info "检查 Redis 容器状态..."
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        echo ""
        echo "=== 容器信息 ==="
        docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            echo ""
            echo "=== 容器统计 ==="
            docker stats "$CONTAINER_NAME" --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
            
            echo ""
            echo "=== Redis 信息 ==="
            docker exec "$CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info server 2>/dev/null | grep -E "redis_version|uptime_in_seconds|connected_clients" || true
        fi
    else
        log_warning "容器 $CONTAINER_NAME 不存在"
    fi
}

# 查看日志
show_logs() {
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "显示 Redis 容器日志 (按 Ctrl+C 退出):"
        docker logs -f "$CONTAINER_NAME"
    else
        log_error "容器 $CONTAINER_NAME 不存在"
    fi
}

# 连接到 Redis CLI
connect_cli() {
    if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "连接到 Redis CLI (输入 'exit' 退出):"
        docker exec -it "$CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD"
    else
        log_error "容器 $CONTAINER_NAME 未运行"
    fi
}

# 删除容器和数据
remove_all() {
    log_warning "这将删除 Redis 容器和所有数据，是否继续? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "删除 Redis 容器和数据..."
        
        # 停止并删除容器
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        
        # 删除数据目录
        rm -rf "$DATA_DIR" "$LOG_DIR"
        
        log_success "Redis 容器和数据已删除"
    else
        log_info "操作已取消"
    fi
}

# 显示环境变量配置
show_env_config() {
    log_info "环境变量配置 (添加到 .env 文件):"
    echo ""
    echo "# Redis Docker 配置"
    echo "REDIS_HOST=localhost"
    echo "REDIS_PORT=$REDIS_PORT"
    echo "REDIS_PASSWORD=$REDIS_PASSWORD"
    echo "REDIS_DB=0"
    echo ""
    echo "# Celery 配置"
    echo "CELERY_BROKER_URL=redis://:$REDIS_PASSWORD@localhost:$REDIS_PORT/0"
    echo "CELERY_RESULT_BACKEND=redis://:$REDIS_PASSWORD@localhost:$REDIS_PORT/0"
}

# 解析命令行参数
case "${1:-start}" in
    "start")
        start_redis
        ;;
    "stop")
        stop_redis
        ;;
    "restart")
        restart_redis
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs
        ;;
    "cli")
        connect_cli
        ;;
    "remove")
        remove_all
        ;;
    "env")
        show_env_config
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start      启动 Redis 容器 (默认)"
        echo "  stop       停止 Redis 容器"
        echo "  restart    重启 Redis 容器"
        echo "  status     查看容器状态"
        echo "  logs       查看容器日志"
        echo "  cli        连接到 Redis CLI"
        echo "  remove     删除容器和数据"
        echo "  env        显示环境变量配置"
        echo "  help       显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start       # 启动 Redis"
        echo "  $0 status      # 查看状态"
        echo "  $0 cli         # 连接 Redis"
        echo "  $0 logs        # 查看日志"
        ;;
    *)
        log_error "未知命令: $1"
        log_info "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
