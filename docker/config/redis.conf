# Redis 配置文件 - Crypt Carry 项目
# 针对消息队列场景优化的 Redis 配置

# ==================== 网络配置 ====================
# 绑定地址 (Docker 环境中绑定所有接口)
bind 0.0.0.0

# 端口配置
port 6379

# 客户端连接超时时间 (秒)
timeout 300

# TCP keepalive 时间
tcp-keepalive 300

# ==================== 安全配置 ====================
# 密码认证 (从环境变量获取)
requirepass crypt_carry_redis_2024

# 禁用危险命令 (生产环境安全措施)
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_crypt_carry_redis_2024"
rename-command SHUTDOWN "SHUTDOWN_crypt_carry_redis_2024"

# ==================== 内存配置 ====================
# 最大内存限制 (适合消息队列使用)
maxmemory 512mb

# 内存淘汰策略 (LRU 算法)
maxmemory-policy allkeys-lru

# ==================== 持久化配置 ====================
# RDB 快照配置
# 900秒内至少1个key变化时保存
save 900 1
# 300秒内至少10个key变化时保存
save 300 10
# 60秒内至少10000个key变化时保存
save 60 10000

# RDB 文件名
dbfilename dump.rdb

# 工作目录
dir /data

# RDB 文件压缩
rdbcompression yes

# RDB 文件校验
rdbchecksum yes

# ==================== AOF 配置 ====================
# 启用 AOF 持久化 (更安全的持久化方式)
appendonly yes

# AOF 文件名
appendfilename "appendonly.aof"

# AOF 同步策略 (每秒同步一次，平衡性能和安全)
appendfsync everysec

# 重写时不进行 AOF 同步
no-appendfsync-on-rewrite no

# AOF 重写触发条件
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# ==================== 日志配置 ====================
# 日志级别 (notice 适合生产环境)
loglevel notice

# 日志文件路径
logfile /var/log/redis/redis.log

# 是否记录系统日志
syslog-enabled no

# ==================== 数据库配置 ====================
# 数据库数量 (消息队列通常使用多个数据库分离不同类型的数据)
databases 16

# ==================== 客户端配置 ====================
# 最大客户端连接数
maxclients 10000

# ==================== 慢查询配置 ====================
# 慢查询阈值 (微秒)
slowlog-log-slower-than 10000

# 慢查询日志长度
slowlog-max-len 128

# ==================== 内存优化配置 ====================
# 哈希表优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表优化
list-max-ziplist-size -2
list-compress-depth 0

# 集合优化
set-max-intset-entries 512

# 有序集合优化
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# ==================== 高级配置 ====================
# 启用 VM (虚拟内存) - 在内存不足时使用
# vm-enabled no

# 后台保存出错时停止写入
stop-writes-on-bgsave-error yes

# 主从复制配置 (如果需要)
# replicaof <masterip> <masterport>
# masterauth <master-password>

# ==================== Lua 脚本配置 ====================
# Lua 脚本超时时间 (毫秒)
lua-time-limit 5000

# ==================== 通知配置 ====================
# 键空间通知 (用于监控键的变化)
notify-keyspace-events ""

# ==================== 模块配置 ====================
# 如果需要加载 Redis 模块，在这里配置
# loadmodule /path/to/module.so

# ==================== 集群配置 ====================
# 如果需要 Redis 集群，取消注释以下配置
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000

# ==================== 监控配置 ====================
# 启用延迟监控
latency-monitor-threshold 100

# ==================== 性能优化 ====================
# 禁用 protected mode (Docker 环境中)
protected-mode no

# TCP backlog 设置
tcp-backlog 511

# 启用 SO_REUSEPORT (Linux 3.9+)
# reuseport yes

# ==================== 调试配置 ====================
# 仅在调试时启用
# loglevel debug
# syslog-enabled yes
# syslog-ident redis
