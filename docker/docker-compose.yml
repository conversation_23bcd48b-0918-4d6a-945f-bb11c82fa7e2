# Crypt Carry 项目 Docker Compose 配置
# 专注于 Redis 服务和可选的监控工具

services:
  # Redis 服务 - 核心消息队列
  redis:
    image: redis:7-alpine
    container_name: crypt_carry_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-crypt_carry_redis_2024}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-crypt_carry_redis_2024}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - crypt_carry_network
    labels:
      - "com.crypt_carry.service=redis"
      - "com.crypt_carry.description=Redis message queue for Crypt Carry"

  # Redis Commander - Web 管理界面 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: crypt_carry_redis_commander
    restart: unless-stopped
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD:-crypt_carry_redis_2024}
      - HTTP_USER=${HTTP_USER:-admin}
      - HTTP_PASSWORD=${HTTP_PASSWORD:-crypt_carry_admin_2024}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crypt_carry_network
    profiles:
      - monitoring
    labels:
      - "com.crypt_carry.service=redis-commander"
      - "com.crypt_carry.description=Redis web management interface"

  # Redis Insight - 官方管理工具 (可选)
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: crypt_carry_redis_insight
    restart: unless-stopped
    ports:
      - "${REDIS_INSIGHT_PORT:-8001}:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crypt_carry_network
    profiles:
      - monitoring
    labels:
      - "com.crypt_carry.service=redis-insight"
      - "com.crypt_carry.description=Redis official management tool"

# 数据卷配置
volumes:
  # Redis 数据持久化
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data

  # Redis Insight 数据
  redis_insight_data:
    driver: local

# 网络配置
networks:
  crypt_carry_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    labels:
      - "com.crypt_carry.network=main"
