version: '3.8'

# Crypt Carry 项目 Docker Compose 配置
# 包含 Redis 和可选的监控服务

services:
  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: crypt_carry_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./redis/logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=crypt_carry_redis_2024
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "crypt_carry_redis_2024", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - crypt_carry_network

  # Redis Commander (可选的 Web 管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: crypt_carry_redis_commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:crypt_carry_redis_2024
      - HTTP_USER=admin
      - HTTP_PASSWORD=crypt_carry_admin_2024
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crypt_carry_network
    profiles:
      - monitoring

  # Redis Insight (可选的官方管理工具)
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: crypt_carry_redis_insight
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crypt_carry_network
    profiles:
      - monitoring

# 数据卷
volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./redis/data
  redis_insight_data:
    driver: local

# 网络
networks:
  crypt_carry_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
