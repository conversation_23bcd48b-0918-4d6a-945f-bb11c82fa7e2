# 路径管理系统

## 概述

为了解决项目中频繁使用复杂路径操作（如 `Path(os.path.dirname(os.path.dirname(__file__)))`）的问题，我们引入了统一的路径管理系统。

## 新的路径管理模块

### 位置

`src/crypt_carry/utils/paths.py`

### 主要功能

#### 1. 预定义的路径常量

```python
PROJECT_ROOT = Path(...)  # 项目根目录
SRC_DIR = PROJECT_ROOT / 'src'
PACKAGE_DIR = SRC_DIR / 'crypt_carry'
CONFIG_DIR = PACKAGE_DIR / 'config'
DATA_DIR = PROJECT_ROOT / 'data'
LOGS_DIR = PROJECT_ROOT / 'logs'
WEB_DATA_DIR = WEB_DIR / 'data'
```

#### 2. 路径获取函数

```python
def get_project_root() -> Path:
    """获取项目根目录"""

def get_config_dir() -> Path:
    """获取配置目录"""

def get_data_dir() -> Path:
    """获取数据目录"""

def get_logs_dir() -> Path:
    """获取日志目录"""

def get_web_data_dir() -> Path:
    """获取Web数据目录"""
```

#### 3. 工具函数

```python
def ensure_dir_exists(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建"""

def get_relative_path_from_project_root(target_path: Union[str, Path]) -> str:
    """获取相对于项目根目录的相对路径"""
```

## 使用示例

### 旧的方式（复杂）

```python
# 复杂且容易出错的路径操作
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
config_dir = os.path.join(project_root, 'src', 'crypt_carry', 'config')
data_dir = os.path.join(project_root, 'data')
```

### 新的方式（简洁）

```python
from crypt_carry.utils.paths import get_project_root, get_config_dir, get_data_dir

project_root = get_project_root()
config_dir = get_config_dir()
data_dir = get_data_dir()
```

## 已更新的文件

以下文件已经更新为使用新的路径管理系统：

1. **`src/crypt_carry/utils/data_directory.py`**
    - 添加了缺失的 `ConfigLoader` 导入
    - 使用 `get_project_root()` 替换复杂的路径操作

2. **`util/logger.py`**
    - 使用新的路径管理系统获取项目根目录
    - 提供向后兼容的备选方案

3. **`src/crypt_carry/web/config.py`**
    - 使用新的路径管理系统定义基础目录
    - 提供向后兼容的备选方案

4. **`src/crypt_carry/web/api/hedging/routes.py`**
    - 使用 `get_web_data_dir()` 获取配置文件路径

5. **`src/crypt_carry/web/api/hedging.py`**
    - 使用 `get_web_data_dir()` 获取存储路径

## 向后兼容性

所有更新都包含了向后兼容的处理：

```python
try:
    from crypt_carry.utils.paths import get_project_root
    project_root = str(get_project_root())
except ImportError:
    # 如果无法导入paths模块，使用原来的方法作为备选
    project_root = str(Path(__file__).parent.parent)
```

## 优势

1. **统一管理**: 所有路径定义集中在一个模块中
2. **易于维护**: 修改路径结构只需要更新一个文件
3. **类型安全**: 使用 `pathlib.Path` 对象，提供更好的类型提示
4. **自动创建**: `ensure_dir_exists` 函数自动创建必要的目录
5. **代码简洁**: 减少重复的路径计算代码
6. **易于测试**: 路径逻辑集中，便于单元测试

## 测试

运行以下命令测试路径管理系统：

```bash
python examples/paths_usage_example.py
```

这将展示新旧路径管理方式的对比，并验证所有功能正常工作。

## 未来扩展

可以根据需要在 `paths.py` 中添加更多路径常量和工具函数，例如：

- 临时文件目录
- 缓存目录
- 备份目录
- 特定功能的数据目录

所有新增的路径都应该遵循统一的命名规范和文档标准。
