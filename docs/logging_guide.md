# 统一日志配置使用指南

## 📋 概述

本项目已实现统一的日志配置系统，支持按策略类型自动分类日志文件，提供一致的日志格式和管理方式。

## 🗂️ 日志目录结构

```
logs/
├── hedging/           # 套保策略日志
│   ├── app.log        # 按大小轮转的日志文件
│   └── app.daily.log  # 按时间轮转的日志文件
├── funding_rate/      # 资金费率策略日志
│   ├── app.log
│   └── app.daily.log
├── web/              # Web服务日志
│   ├── app.log
│   └── app.daily.log
└── app.log           # 通用日志文件
```

## 🔧 使用方法

### 1. 套保策略模块

```python
from crypt_carry.utils.logger_config import get_hedging_strategy_logger

# 获取套保策略日志记录器
logger = get_hedging_strategy_logger(__name__)

# 使用日志
logger.info("套保策略启动")
logger.warning("检测到风险")
logger.error("执行失败")
```

### 2. 资金费率策略模块

```python
from crypt_carry.utils.logger_config import get_funding_rate_strategy_logger

# 获取资金费率策略日志记录器
logger = get_funding_rate_strategy_logger(__name__)

# 使用日志
logger.info("资金费率监控启动")
logger.debug("费率数据更新")
```

### 3. Web服务模块

```python
from crypt_carry.utils.logger_config import get_web_service_logger

# 获取Web服务日志记录器
logger = get_web_service_logger(__name__)

# 使用日志
logger.info("Web服务启动")
logger.error("API请求失败")
```

### 4. 通用方式

```python
from crypt_carry.utils.logger_config import get_strategy_logger

# 指定策略类型
logger = get_strategy_logger('hedging', __name__)
logger = get_strategy_logger('funding_rate', __name__)
logger = get_strategy_logger('web', __name__)
```

### 5. LoggerManager方式

```python
from crypt_carry.utils.logger_config import LoggerManager

# 获取不同类型的日志记录器
hedging_logger = LoggerManager.get_hedging_logger(__name__)
web_logger = LoggerManager.get_web_logger(__name__)
```

## 📝 日志格式

统一的日志格式：
```
2025-06-02 15:43:12,616 [filename.py:32] - INFO - 日志消息内容
```

格式说明：
- `2025-06-02 15:43:12,616`: 时间戳（精确到毫秒）
- `[filename.py:32]`: 文件名和行号
- `INFO`: 日志级别
- `日志消息内容`: 实际的日志内容

## 🔄 日志轮转

### 按大小轮转 (app.log)
- 最大文件大小: 10MB
- 备份文件数量: 5个
- 文件命名: `app.log`, `app.log.1`, `app.log.2`, ...

### 按时间轮转 (app.daily.log)
- 轮转时间: 每天午夜
- 备份文件数量: 7个
- 文件命名: `app.daily.log`, `app.daily.log.2025-06-01`, ...

## 🚀 启动脚本集成

### 分离的启动脚本

现在提供了三个启动脚本：

1. **后端服务**: `./src/crypt_carry/web/start_backend.sh`
2. **前端服务**: `./src/crypt_carry/web/start_frontend.sh`
3. **统一启动**: `./src/crypt_carry/web/start_web_services.sh`

### 使用方法

```bash
# 只启动后端服务
./src/crypt_carry/web/start_backend.sh start

# 只启动前端服务
./src/crypt_carry/web/start_frontend.sh start

# 同时启动前后端服务
./src/crypt_carry/web/start_web_services.sh start

# 查看后端日志
./src/crypt_carry/web/start_backend.sh logs

# 查看前端日志
./src/crypt_carry/web/start_frontend.sh logs

# 检查服务状态
./src/crypt_carry/web/start_web_services.sh status
```

### 日志文件位置

启动脚本会将日志文件保存到项目根目录下：

- **Web服务日志**: `logs/web/backend.log`, `logs/web/frontend.log`
- **套保策略日志**: `logs/hedging/app.log`, `logs/hedging/app.daily.log`
- **资金费率日志**: `logs/funding_rate/app.log`, `logs/funding_rate/app.daily.log`

## 🔧 配置自定义

### 环境变量

启动脚本支持以下环境变量：

```bash
# 设置日志目录
export HEDGING_LOG_DIR="/path/to/hedging/logs"
export FUNDING_RATE_LOG_DIR="/path/to/funding_rate/logs"
export WEB_LOG_DIR="/path/to/web/logs"

# 设置后端端口
export FLASK_PORT=5001
```

### 配置文件

可以通过 `config.yaml` 文件自定义日志配置：

```yaml
LOGGING:
  LEVEL: INFO
  FORMAT: '%(asctime)s [%(filename)s:%(lineno)d] - %(levelname)s - %(message)s'
  CONSOLE:
    ENABLED: true
  FILE:
    ENABLED: true
    MAX_BYTES: 10485760  # 10MB
    BACKUP_COUNT: 5
  TIMED:
    ENABLED: true
    WHEN: midnight
    INTERVAL: 1
    BACKUP_COUNT: 7
```

## 📊 日志级别

支持的日志级别（从低到高）：
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误

## 🔍 故障排除

### 常见问题

1. **日志文件未创建**
   - 检查目录权限
   - 确保项目根目录可写

2. **日志格式不统一**
   - 确保使用统一的日志配置模块
   - 检查是否有直接使用 `logging.getLogger()` 的地方

3. **日志文件过大**
   - 检查日志轮转配置
   - 调整 `MAX_BYTES` 和 `BACKUP_COUNT` 参数

### 调试方法

```python
# 检查当前日志配置
import logging
print(f"当前日志级别: {logging.getLogger().level}")
print(f"日志处理器数量: {len(logging.getLogger().handlers)}")

# 查看日志处理器详情
for handler in logging.getLogger().handlers:
    print(f"处理器类型: {type(handler)}")
    if hasattr(handler, 'baseFilename'):
        print(f"日志文件: {handler.baseFilename}")
```

## 📈 最佳实践

1. **使用模块名称**: 总是传入 `__name__` 作为模块名称
2. **选择合适的日志级别**: 根据信息重要性选择合适的级别
3. **避免敏感信息**: 不要在日志中记录密码、API密钥等敏感信息
4. **结构化日志**: 使用一致的日志格式，便于后续分析
5. **定期清理**: 定期检查和清理旧的日志文件

## 🔗 相关文件

- `util/logger.py`: 核心日志配置
- `src/crypt_carry/utils/logger_config.py`: 策略类型日志配置
- `src/crypt_carry/web/start_backend.sh`: 后端启动脚本
- `src/crypt_carry/web/start_frontend.sh`: 前端启动脚本
- `src/crypt_carry/web/start_web_services.sh`: 统一启动脚本
