# crypt_carry

## 项目结构

```
├── main.py                      # 主程序入口文件
├── carry.sh                     # 启动脚本，支持start/stop/restart命令
├── config.yaml                  # 主配置文件
├── requirements.txt             # Python依赖包列表
├── README.md                    # 项目说明文档
├── exchange_schedule.md         # 交易所时间表文档
├── new_dic.md                   # 新的项目结构规划文档
├── __init__.py                  # 根目录包初始化文件
├── carry/                       # 核心业务逻辑包
│   ├── __init__.py
│   ├── config/                  # 配置管理模块
│   │   ├── __init__.py
│   │   ├── base_config.yaml     # 基础配置文件
│   │   ├── config_loader.py     # 配置加载器
│   │   ├── ding_talk_config.yaml # 钉钉通知配置
│   │   └── trading_config.yaml  # 交易策略配置
│   ├── constant/                # 常量定义模块
│   │   ├── direction_type.py    # 交易方向类型定义
│   │   ├── notice_type.py       # 通知类型定义
│   │   ├── order_type.py        # 订单类型定义
│   │   └── trade_type.py        # 交易类型定义
│   ├── data/                    # 数据存储目录
│   │   ├── cache/              # 缓存数据
│   │   └── market_data/        # 市场数据存储
│   │       ├── arbitrage/      # 套利机会数据
│   │       ├── funding_rates/  # 资金费率数据
│   │       ├── historical_scores/ # 历史得分数据
│   │       └── market_price/   # 市场价格数据
│   ├── exchange/                 # 交易所相关模块
│   │   ├── __init__.py
│   │   ├── account/             # 账户管理模块
│   │   │   ├── __init__.py
│   │   │   ├── account_interface.py  # 账户接口定义
│   │   │   ├── account_manager.py    # 账户管理器，统一的账户管理入口
│   │   │   ├── balance_manager.py    # 余额管理器，管理各交易所账户余额
│   │   │   └── order_manager.py      # 订单管理器，管理各交易所订单操作
│   │   ├── base/                # 基础接口和配置
│   │   │   ├── exchange_client.py    # 交易所客户端基类，定义统一接口
│   │   │   └── exchange_config.py    # 交易所配置管理，API密钥和交易参数
│   │   ├── factory/             # 工厂模式创建交易所客户端
│   │   │   ├── __init__.py
│   │   │   └── exchange_factory.py   # 交易所工厂类，创建不同交易所客户端
│   │   ├── funding/             # 资金费率管理模块
│   │   │   ├── funding_rate_cache.py     # 资金费率缓存，减少重复查询
│   │   │   ├── funding_rate_manager.py   # 资金费率管理器，单例模式统一管理
│   │   │   ├── funding_rate_scheduler.py # 资金费率定时更新调度器
│   │   │   └── funding_rate_storage.py   # 资金费率持久化存储
│   │   ├── implementations/     # 具体交易所实现
│   │   │   ├── binance/        # 币安交易所实现
│   │   │   │   ├── binance_client.py         # 币安客户端，API交互和WebSocket连接
│   │   │   │   ├── binance_funding.py        # 币安资金费率管理
│   │   │   │   ├── binance_market.py         # 币安市场数据管理
│   │   │   │   ├── binance_order_client.py   # 币安订单客户端
│   │   │   │   ├── binance_position_rest.py  # 币安持仓信息REST API
│   │   │   │   ├── binance_position_utils.py # 币安持仓工具函数
│   │   │   │   ├── binance_position_ws.py    # 币安持仓信息WebSocket
│   │   │   │   ├── binance_symbol_utils.py   # 币安交易对工具函数
│   │   │   │   └── binance_ws.py             # 币安WebSocket客户端
│   │   │   └── okx/            # OKX交易所实现
│   │   │       ├── okx_client.py             # OKX客户端，API交互和WebSocket连接
│   │   │       ├── okx_funding.py            # OKX资金费率管理
│   │   │       ├── okx_market.py             # OKX市场数据管理
│   │   │       ├── okx_order_client.py       # OKX订单客户端
│   │   │       ├── okx_position_rest.py      # OKX持仓信息REST API
│   │   │       ├── okx_position_utils.py     # OKX持仓工具函数
│   │   │       ├── okx_position_ws.py        # OKX持仓信息WebSocket
│   │   │       ├── okx_symbol_utils.py       # OKX交易对工具函数
│   │   │       └── okx_ws.py                 # OKX WebSocket客户端
│   │   ├── market/              # 市场数据管理模块
│   │   │   ├── market_data_manager.py    # 市场数据管理器，获取和管理市场数据
│   │   │   └── market_limits.py          # 市场限制管理器，管理交易所限制参数
│   │   └── order/               # 订单管理模块
│   │       ├── constants.py              # 订单相关常量定义
│   │       ├── exceptions.py             # 订单异常定义
│   │       ├── order_cleaner.py          # 订单清理器，清理过期订单
│   │       ├── order_creator.py          # 订单创建器，创建各类订单
│   │       ├── order_error_handler.py    # 订单错误处理器，处理订单错误
│   │       ├── order_executor.py         # 订单执行器，执行订单操作
│   │       ├── order_helper.py           # 订单辅助工具函数
│   │       ├── order_monitor.py          # 订单监控器，监控订单状态
│   │       ├── order_state.py            # 订单状态定义
│   │       └── order_state_manager.py    # 订单状态管理器，管理订单状态变化
│   ├── trading/                  # 交易策略和执行模块
│   │   ├── monitor/             # 监控模块
│   │   │   ├── __init__.py
│   │   │   ├── order_cooldown_manager.py     # 订单冷却管理器，管理订单冷却时间
│   │   │   ├── pair_data_loader.py           # 交易对数据加载器，加载交易对数据
│   │   │   ├── position_force_checker.py     # 强制持仓检查器，强制检查持仓
│   │   │   ├── position_monitor.py           # 持仓监控器，监控持仓变化和异常
│   │   │   └── position_risk_checker.py      # 持仓风险检查器，检查持仓风险
│   │   ├── opportunity/         # 套利机会检测模块
│   │   │   ├── __init__.py
│   │   │   └── opportunity_detector.py       # 套利机会检测器，检测和评估套利机会
│   │   ├── position/            # 持仓管理模块
│   │   │   ├── position_adjuster.py          # 持仓调整器，调整持仓和计算调整数量
│   │   │   └── position_manager.py           # 持仓管理器，管理持仓和计算持仓价值
│   │   ├── scoring/             # 评分模块
│   │   │   ├── __init__.py
│   │   │   └── opportunity_scorer.py         # 机会评分器，对套利机会进行评分
│   │   ├── strategy/            # 策略模块
│   │   │   ├── __init__.py
│   │   │   ├── base_strategy.py              # 基础策略类，定义策略通用接口
│   │   │   ├── exchange_funding_strategy.py  # 资金费率套利策略，基于资金费率差异套利
│   │   │   └── strategy_manager.py           # 策略管理器，管理和协调多个策略
│   │   ├── task/                # 任务管理模块
│   │   │   ├── __init__.py
│   │   │   ├── pending_tasks_manager.py      # 待处理任务管理器，单例模式管理建仓平仓任务
│   │   │   └── task_processor.py             # 任务处理器，处理具体任务
│   │   └── trade/               # 交易执行模块
│   │       ├── __init__.py
│   │       └── trade_executor.py             # 交易执行器，执行具体交易操作
│   └── utils/                   # 工具函数模块
│       ├── arbitrage_manager.py          # 套利管理器，管理套利相关操作
│       ├── calc.py                       # 计算工具函数，提供各种计算方法
│       ├── data_directory.py             # 数据目录管理器，单例模式管理数据目录
│       ├── ding_talk.py                  # 钉钉消息发送基础功能
│       ├── ding_talk_manager.py          # 钉钉消息管理器，单例模式统一消息发送
│       ├── retry.py                      # 重试装饰器，提供重试机制
│       └── test_ding.py                  # 钉钉功能测试文件
├── util/                        # 根目录工具模块（待整合到carry/utils）
│   ├── calculate_price_earnings_util.py  # 价格收益计算工具
│   ├── formatter.py                      # 格式化工具
│   ├── log_throttler.py                  # 日志限流器
│   └── logger.py                         # 日志配置器
├── tests/                       # 测试文件目录
│   ├── okx_Vol_Test.py                   # OKX交易量测试
│   ├── testRate.py                       # 费率测试
│   ├── test_basis.py                     # 基差测试
│   ├── test_binance_perp_ws.py           # 币安永续合约WebSocket测试
│   └── test_binance_spot_ws.py           # 币安现货WebSocket测试
├── examples/                    # 示例代码目录
│   ├── analysis_arbitrage.py             # 套利分析示例
│   ├── binance_spot_test.py              # 币安现货测试示例
│   ├── okx_spot_test.py                  # OKX现货测试示例
│   └── sum_arbitrage.py                  # 套利汇总示例
├── docs/                        # 文档目录
│   └── carry_dic.md                      # 项目结构说明文档
└── logs/                        # 日志文件目录
    ├── app.log                           # 应用主日志
    ├── app.daily.log                     # 每日日志
    └── app.daily.log.YYYY-MM-DD          # 按日期归档的日志文件
```

## 核心模块功能详解

### 配置管理模块 (carry/config/)

- **config_loader.py**: 负责加载和解析所有配置文件，支持多环境配置
- **base_config.yaml**: 基础配置，包含日志级别、数据存储路径、API URLs、WebSocket配置等
- **trading_config.yaml**: 交易策略配置，包含交易所特定配置、策略参数、评分权重等
- **ding_talk_config.yaml**: 钉钉通知配置，用于系统告警和通知

### 常量定义模块 (carry/constant/)

- **direction_type.py**: 定义交易方向类型（做多/做空）
- **notice_type.py**: 定义通知类型（告警级别等）
- **order_type.py**: 定义订单类型（市价单/限价单等）
- **trade_type.py**: 定义交易类型（开仓/平仓等）

### 交易所模块 (carry/exchange/)

#### 基础接口 (base/)

- **exchange_client.py**: 交易所客户端基类，定义统一的交易所接口
- **exchange_config.py**: 交易所配置管理，处理API凭证和交易参数

#### 工厂模式 (factory/)

- **exchange_factory.py**: 交易所工厂类，根据配置创建不同的交易所客户端实例

#### 账户管理 (account/)

- **account_manager.py**: 账户管理器，作为统一的账户管理入口
- **balance_manager.py**: 余额管理器，获取和管理各交易所的账户余额
- **order_manager.py**: 订单管理器，统一的订单操作接口

#### 资金费率管理 (funding/)

- **funding_rate_manager.py**: 单例模式的资金费率管理器，统一管理多交易所资金费率
- **funding_rate_scheduler.py**: 资金费率定时更新调度器，支持批量更新
- **funding_rate_cache.py**: 资金费率缓存，减少重复API调用
- **funding_rate_storage.py**: 资金费率持久化存储

#### 市场数据管理 (market/)

- **market_data_manager.py**: 市场数据管理器，获取深度、K线等市场数据
- **market_limits.py**: 市场限制管理器，管理交易所的交易限制参数

#### 订单管理 (order/)

- **order_executor.py**: 订单执行器，执行具体的订单操作
- **order_creator.py**: 订单创建器，创建各类订单（现货/合约、市价/限价）
- **order_monitor.py**: 订单监控器，实时监控订单状态变化
- **order_state_manager.py**: 订单状态管理器，管理订单生命周期
- **order_error_handler.py**: 订单错误处理器，处理订单失败和重试
- **order_cleaner.py**: 订单清理器，清理过期和无效订单

### 交易策略模块 (carry/trading/)

#### 监控模块 (monitor/)

- **position_monitor.py**: 持仓监控器，监控持仓变化和风险
- **position_risk_checker.py**: 持仓风险检查器，评估和处理高风险持仓
- **position_force_checker.py**: 强制持仓检查器，强制触发持仓检查
- **order_cooldown_manager.py**: 订单冷却管理器，防止频繁下单
- **pair_data_loader.py**: 交易对数据加载器，加载和解析交易对信息

#### 机会检测模块 (opportunity/)

- **opportunity_detector.py**: 套利机会检测器，检测和评估套利机会

#### 评分模块 (scoring/)

- **opportunity_scorer.py**: 机会评分器，对套利机会进行量化评分

#### 持仓管理模块 (position/)

- **position_manager.py**: 持仓管理器，管理持仓信息和计算持仓价值
- **position_adjuster.py**: 持仓调整器，计算和执行持仓调整

#### 策略模块 (strategy/)

- **base_strategy.py**: 基础策略类，定义策略的通用接口和生命周期
- **exchange_funding_strategy.py**: 资金费率套利策略，基于资金费率差异进行套利
- **strategy_manager.py**: 策略管理器，管理和协调多个策略的执行

#### 任务管理模块 (task/)

- **pending_tasks_manager.py**: 待处理任务管理器，单例模式管理建仓和平仓任务
- **task_processor.py**: 任务处理器，处理具体的交易任务

#### 交易执行模块 (trade/)

- **trade_executor.py**: 交易执行器，执行具体的交易操作和结果处理

### 工具函数模块 (carry/utils/)

- **data_directory.py**: 数据目录管理器，单例模式管理数据存储路径
- **ding_talk_manager.py**: 钉钉消息管理器，单例模式统一消息发送
- **calc.py**: 计算工具函数，提供价值差值计算等数学工具
- **arbitrage_manager.py**: 套利管理器，管理套利相关操作
- **retry.py**: 重试装饰器，为网络请求等操作提供重试机制

## 项目特色功能

### 1. 单例模式设计

- 资金费率管理器、任务管理器、钉钉消息管理器等关键组件采用单例模式
- 确保全局状态一致性和资源的有效利用

### 2. 异步处理架构

- 全面采用异步编程模式，支持高并发的市场数据处理
- WebSocket连接管理，实时获取市场数据和持仓变化

### 3. 多交易所支持

- 统一的交易所接口设计，支持币安、OKX等多个交易所
- 工厂模式创建交易所客户端，易于扩展新的交易所

### 4. 风险控制机制

- 多层次的风险检查：持仓风险、保证金风险、订单冷却等
- 自动平仓机制，在极高风险时自动执行平仓操作

### 5. 智能任务管理

- 按交易所隔离的任务管理，支持建仓和平仓任务的独立管理
- 任务恢复机制，处理订单失败、超时等异常情况

### 6. 实时监控和告警

- 持仓监控、订单监控、风险监控等多维度监控
- 钉钉集成，实时发送告警和状态通知

## 最近更新

### WebSocket 处理优化
- 改进了 OKX WebSocket 客户端，支持安全的重复订阅和动态更新订阅列表
- 添加了互斥锁以防止 WebSocket 消息处理中的并发访问问题
- 增强了错误处理和日志记录

### 资金费率管理优化
- 修复了资金费率调度器中的协程处理问题，确保正确等待异步任务完成
- 优化了资金费率更新逻辑，支持按交易对批量更新
- 改进了资金费率更新的时间点判断逻辑

### 系统关闭流程优化
- 增强了系统关闭时的资源释放流程，确保所有异步任务正确关闭
- 修复了策略停止过程中的协程等待问题
- 添加了对 CCXT 交易所实例的显式关闭，防止资源泄漏

### 项目结构优化

- 新增了scoring模块，独立处理套利机会评分逻辑
- 完善了constant模块，统一管理各类常量定义
- 优化了factory模式的实现，提高了代码的可维护性