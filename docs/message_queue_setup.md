# 消息队列架构部署指南

## 📋 **概述**

本指南介绍如何部署基于 Redis + Celery 的消息队列架构，解决 Web API 服务和套保策略服务之间的通信问题。

## 🏗️ **架构图**

```
┌─────────────────┐    HTTP API     ┌─────────────────┐
│   Web 前端服务   │ ──────────────→ │   Web 后端服务   │
│ (React/TypeScript)│                │   (Flask API)   │
└─────────────────┘                └─────────────────┘
                                           │
                                           │ Redis MQ
                                           ▼
                                   ┌─────────────────┐
                                   │   消息队列服务   │
                                   │    (Redis)     │
                                   └─────────────────┘
                                           │
                                           │ Celery Worker
                                           ▼
                                   ┌─────────────────┐
                                   │   套保策略服务   │
                                   │   (独立进程)    │
                                   │  - 策略管理器    │
                                   │  - 任务执行器    │
                                   │  - 风险管理      │
                                   │  - 持仓管理      │
                                   └─────────────────┘
```

## 🚀 **快速部署**

### **第一步：安装 Redis**

#### **macOS (使用 Homebrew)**
```bash
# 安装 Redis
brew install redis

# 启动 Redis 服务
brew services start redis

# 验证安装
redis-cli ping
# 应该返回: PONG
```

#### **Ubuntu/Debian**
```bash
# 更新包列表
sudo apt update

# 安装 Redis
sudo apt install redis-server

# 启动 Redis 服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 验证安装
redis-cli ping
```

#### **CentOS/RHEL**
```bash
# 安装 EPEL 仓库
sudo yum install epel-release

# 安装 Redis
sudo yum install redis

# 启动 Redis 服务
sudo systemctl start redis
sudo systemctl enable redis

# 验证安装
redis-cli ping
```

### **第二步：安装 Python 依赖**

```bash
# 激活 conda 环境
conda activate crypt_carry312

# 安装 Celery 和 Redis 客户端
pip install celery[redis] redis

# 验证安装
python -c "import celery, redis; print('依赖安装成功')"
```

### **第三步：配置环境变量**

创建环境配置文件 `.env`：

```bash
# 在项目根目录创建 .env 文件
cat > .env << 'EOF'
# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Celery 配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 日志配置
CELERY_LOG_LEVEL=INFO
EOF
```

### **第四步：启动服务**

#### **1. 启动 Redis 服务**
```bash
# 如果使用 Homebrew (macOS)
brew services start redis

# 如果使用 systemd (Linux)
sudo systemctl start redis
```

#### **2. 启动套保策略 Worker**
```bash
# 激活 conda 环境
conda activate crypt_carry312

# 启动 Worker
cd /path/to/crypt_carry
chmod +x src/scripts/hedging/start_hedging_worker.sh
src/scripts/hedging/start_hedging_worker.sh start
```

#### **3. 启动 Web 服务**
```bash
# 启动前后端服务
cd src/crypt_carry/web
./start_web_services.sh start
```

## 🔧 **详细配置**

### **Redis 配置优化**

编辑 Redis 配置文件（通常在 `/etc/redis/redis.conf` 或 `/usr/local/etc/redis.conf`）：

```conf
# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
bind 127.0.0.1
port 6379
timeout 300

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

### **Celery Worker 配置**

Worker 配置在 `src/crypt_carry/strategies/hedging/tasks/celery_app.py` 中：

```python
# 主要配置项
celery_app.conf.update(
    # 消息代理
    broker_url=REDIS_URL,
    result_backend=REDIS_URL,
    
    # 任务配置
    task_serializer='json',
    result_serializer='json',
    timezone='UTC',
    
    # 性能配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    
    # 超时配置
    task_soft_time_limit=300,  # 5分钟
    task_time_limit=600,       # 10分钟
    
    # 重试配置
    task_default_retry_delay=60,
    task_max_retries=3,
)
```

## 📊 **监控和管理**

### **查看服务状态**

```bash
# 查看 Redis 状态
redis-cli info server

# 查看 Worker 状态
src/scripts/hedging/start_hedging_worker.sh status

# 查看 Web 服务状态
src/crypt_carry/web/start_web_services.sh status
```

### **查看日志**

```bash
# Worker 日志
src/scripts/hedging/start_hedging_worker.sh logs

# Web 服务日志
src/crypt_carry/web/start_web_services.sh logs

# Redis 日志
tail -f /var/log/redis/redis-server.log
```

### **API 监控**

```bash
# 检查 Worker 状态
curl http://localhost:5001/api/hedging/worker/status

# 检查套保状态
curl http://localhost:5001/api/hedging/orders/hedging/status

# 查看任务状态
curl http://localhost:5001/api/hedging/tasks/{task_id}/status
```

## 🔍 **故障排除**

### **常见问题**

#### **1. Redis 连接失败**
```bash
# 检查 Redis 是否运行
redis-cli ping

# 检查端口是否被占用
lsof -i :6379

# 重启 Redis
brew services restart redis  # macOS
sudo systemctl restart redis  # Linux
```

#### **2. Worker 启动失败**
```bash
# 检查 conda 环境
conda list | grep celery

# 检查 Python 路径
echo $PYTHONPATH

# 手动启动 Worker 进行调试
cd /path/to/crypt_carry
export PYTHONPATH="$PWD/src:$PYTHONPATH"
celery -A crypt_carry.strategies.hedging.tasks.celery_app worker --loglevel=DEBUG
```

#### **3. 任务执行失败**
```bash
# 查看详细错误日志
tail -f logs/hedging_worker.log

# 检查任务队列
redis-cli LLEN hedging_orders
redis-cli LLEN single_orders

# 清空失败的任务
redis-cli FLUSHDB
```

## 🚀 **生产环境部署**

### **使用 Supervisor 管理进程**

安装 Supervisor：
```bash
pip install supervisor
```

创建配置文件 `/etc/supervisor/conf.d/crypt_carry.conf`：
```ini
[program:crypt_carry_worker]
command=/path/to/conda/envs/crypt_carry312/bin/celery -A crypt_carry.strategies.hedging.tasks.celery_app worker --loglevel=INFO
directory=/path/to/crypt_carry
user=your_user
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/path/to/crypt_carry/logs/worker_supervisor.log

[program:crypt_carry_web]
command=/path/to/conda/envs/crypt_carry312/bin/python app.py
directory=/path/to/crypt_carry/src/crypt_carry/web
user=your_user
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/path/to/crypt_carry/logs/web_supervisor.log
```

启动服务：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start all
```

### **使用 Docker 部署**

创建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  worker:
    build: .
    command: celery -A crypt_carry.strategies.hedging.tasks.celery_app worker --loglevel=INFO
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
    volumes:
      - ./logs:/app/logs

  web:
    build: .
    command: python app.py
    ports:
      - "5001:5001"
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis

volumes:
  redis_data:
```

## 📈 **性能优化**

### **Redis 优化**
- 调整 `maxmemory` 根据可用内存
- 使用 `redis-benchmark` 测试性能
- 监控内存使用和命中率

### **Celery 优化**
- 调整 `worker_prefetch_multiplier`
- 使用多个 Worker 进程
- 根据任务类型分配不同队列

### **监控指标**
- 任务执行时间
- 队列长度
- Worker 负载
- 内存使用率

## 🔐 **安全配置**

### **Redis 安全**
```conf
# 设置密码
requirepass your_strong_password

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG ""
```

### **网络安全**
- 使用防火墙限制 Redis 访问
- 配置 SSL/TLS 加密
- 使用 VPN 或私有网络

这个消息队列架构解决了您提到的问题，实现了 Web API 服务和套保策略服务的解耦，提供了可靠的异步任务处理能力。
