import json
import os

import pandas as pd


def count_mixed_exchange_opportunities(data):
    # 初始化计数器
    mixed_exchange_count = 0
    
    # 遍历数据
    for opportunity in data:
        # 检查现货和期货交易所是否不同
        if (opportunity['spot']['exchange'] == 'OKX' and opportunity['futures']['exchange'] == 'Binance') or \
           (opportunity['spot']['exchange'] == 'Binance' and opportunity['futures']['exchange'] == 'OKX'):
            mixed_exchange_count += 1
    
    return mixed_exchange_count


def filter_high_basis_rate(data, threshold=0.4):
    return [
        opportunity for opportunity in data 
        if opportunity['basis_rate'] > threshold
    ]

def export_to_json(opportunities, filename='high_basis_opportunities.json'):
    with open(filename, 'w') as f:
        json.dump(opportunities, f, indent=2)
    print(f"已导出到 {filename}")

def export_to_excel(opportunities, filename='arbitrage_opportunities.xlsx'):
    # 准备数据
    export_data = []
    for opportunity in opportunities:
        export_data.append({
            '交易对': opportunity['pair'],
            '现货价格': opportunity['spot']['price'],
            '现货交易所': opportunity['spot']['exchange'],
            '期货价格': opportunity['futures']['price'],
            '期货交易所': opportunity['futures']['exchange'],
            'Basis Rate': opportunity['basis_rate'],
            '时间戳': opportunity['timestamp']
        })
    
    # 创建DataFrame
    df = pd.DataFrame(export_data)
    
    # 导出到Excel
    df.to_excel(filename, index=False, engine='openpyxl')
    print(f"已导出到 {filename}")


# 文件名数组
filenames = ['arbitrage_opportunities_20241128_14.json',
             'arbitrage_opportunities_20241128_15.json',
             'arbitrage_opportunities_20241128_16.json',
             'arbitrage_opportunities_20241128_17.json',
             'arbitrage_opportunities_20241128_18.json',
             'arbitrage_opportunities_20241128_19.json',
             'arbitrage_opportunities_20241128_20.json',
             ]

# 存储所有机会的列表
all_arbitrage_data = []

# 批量读取文件
for filename in filenames:
    try:
        # 使用完整路径读取文件
        full_path = os.path.join('remote_arbitrage', filename)
        with open(full_path, 'r') as file:
            arbitrage_data = json.load(file)
            all_arbitrage_data.extend(arbitrage_data)
    except FileNotFoundError:
        print(f"文件 {filename} 未找到")
    except json.JSONDecodeError:
        print(f"文件 {filename} JSON解析错误")

# 计算混合交易所的套利机会数量
result = count_mixed_exchange_opportunities(all_arbitrage_data)
print(f"混合交易所(OKX和Binance)的套利机会数量：{result}")

# 筛选basis_rate大于0.3的机会
result = filter_high_basis_rate(all_arbitrage_data, threshold=0.4)

# 导出到Excel
export_to_excel(result)

# 导出到JSON
export_to_json(result)
