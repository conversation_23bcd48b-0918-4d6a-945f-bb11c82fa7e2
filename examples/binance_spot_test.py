import asyncio
import json
import logging
from pathlib import Path

from carry.config.config_loader import ConfigLoader

# 计算项目根目录
project_root = Path(__file__).parent.parent

# 加载配置
config = ConfigLoader.get_base_config()

# 配置日志
logging.basicConfig(
    level=logging.DEBUG if config.get('LOGGER_LEVEL', 'INFO') == "DEBUG" else logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler('binance_spot_test.log')  # 同时保存到文件
    ]
)
logger = logging.getLogger(__name__)


class BinanceSpotTest:
    def __init__(self):
        self.ws_url = config.get('BINANCE_SPOT_WS_URL', '')
        self.session = None
        self.ws = None

    async def connect(self):
        """建立WebSocket连接"""
        try:
            # 设置代理
            if config.get('PROXY', {}).get('enabled', False):
                connector = aiohttp.TCPConnector(ssl=False)
                self.session = aiohttp.ClientSession(connector=connector)
                logger.info(f"使用代理: {config.get('PROXY', {}).get('http', '')}")
                self.ws = await self.session.ws_connect(
                    self.ws_url,
                    proxy=config.get('PROXY', {}).get('http', ''),
                    heartbeat=30
                )
            else:
                self.session = aiohttp.ClientSession()
                self.ws = await self.session.ws_connect(
                    self.ws_url,
                    heartbeat=30
                )

            logger.info(f"WebSocket连接建立成功: {self.ws_url}")

            # 订阅BTC/USDT行情
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [
                    "btcusdt@bookTicker"
                ],
                "id": 1
            }
            await self.ws.send_json(subscribe_msg)
            logger.info(f"已发送订阅消息: {json.dumps(subscribe_msg, indent=2)}")

        except Exception as e:
            logger.error(f"连接失败: {str(e)}", exc_info=True)
            raise

    async def listen(self):
        """监听WebSocket消息"""
        message_count = 0
        try:
            while True:
                msg = await self.ws.receive()

                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)

                    # 处理订阅确认消息
                    if "result" in data:
                        logger.info(f"订阅确认: {json.dumps(data, indent=2)}")
                        continue

                    # bookTicker消息包含 u(更新ID)和s(交易对)字段
                    if "u" in data and "s" in data:
                        # 计算中间价
                        best_bid = float(data["b"])
                        best_ask = float(data["a"])
                        mid_price = (best_bid + best_ask) / 2

                        message_count += 1
                        if message_count % 100 == 0:  # 每100条消息打印一次详细信息
                            logger.debug(f"原始消息: {json.dumps(data, indent=2)}")

                        logger.info(f"BTC/USDT - 更新ID: {data['u']} - 买一: {best_bid:.2f}({data['B']}) - "
                                    f"卖一: {best_ask:.2f}({data['A']}) - 中间价: {mid_price:.2f} - "
                                    f"消息计数: {message_count}")
                    else:
                        logger.debug(f"收到其他类型消息: {json.dumps(data, indent=2)}")

                elif msg.type == aiohttp.WSMsgType.CLOSED:
                    logger.error("WebSocket连接已关闭")
                    break
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f"WebSocket错误: {msg.data}")
                    break

        except Exception as e:
            logger.error(f"监听错误: {str(e)}", exc_info=True)
            raise

    async def close(self):
        """关闭连接"""
        try:
            if self.ws:
                await self.ws.close()
            if self.session:
                await self.session.close()
            logger.info("连接已正常关闭")
        except Exception as e:
            logger.error(f"关闭连接时发生错误: {str(e)}", exc_info=True)

    async def run(self):
        """运行测试"""
        try:
            await self.connect()
            await self.listen()
        except Exception as e:
            logger.error(f"运行错误: {str(e)}", exc_info=True)
        finally:
            await self.close()


async def main():
    client = BinanceSpotTest()
    try:
        logger.info("启动Binance现货WebSocket测试...")
        await client.run()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        await client.close()
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
        await client.close()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序正常退出")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
