import json
import pandas as pd
from datetime import datetime, timedelta
import sys
sys.path.append('.')  # 确保可以导入util模块
from util.calculate_price_earnings_util import calculate_arbitrage_with_slippage

def process_arbitrage_opportunities(
    json_file, 
    time_window=5, 
    capital=10000, 
    leverage=3, 
    slippage_rate=0.001  # 默认滑点率0.1%
):
    # 读取JSON文件
    with open(json_file, 'r') as f:
        opportunities = json.load(f)
    
    # 存储结果的列表
    processed_results = []
    
    # 用于去重的字典
    processed_pairs = {}
    
    # 遍历机会
    for opportunity in opportunities:
        pair = opportunity['pair']
        timestamp = opportunity['timestamp']
        spot_price = opportunity['spot']['price']
        future_price = opportunity['futures']['price']
        
        # 转换时间戳
        current_time = datetime.fromtimestamp(timestamp / 1000)
        
        # 检查是否已经处理过该币种
        if pair in processed_pairs:
            last_processed_time = processed_pairs[pair]
            # 如果在5分钟内已处理过，跳过
            if (current_time - last_processed_time).total_seconds() < time_window * 60:
                continue
        
        # 设置固定的费率参数
        spot_fee_rate = 0.001  # 0.1%
        future_fee_rate = 0.0002  # 0.02%
        funding_rate = 0.0001  # 0.01%
        
        try:
            # 计算收益（使用新的带滑点的函数）
            result = calculate_arbitrage_with_slippage(
                spot_price, 
                future_price, 
                capital, 
                leverage, 
                spot_fee_rate, 
                future_fee_rate, 
                funding_rate,
                slippage_rate  # 新增滑点参数
            )
            
            # 添加额外信息（调整顺序）
            result['pair'] = pair
            result['timestamp'] = timestamp
            result['spot_price'] = spot_price
            result['future_price'] = future_price
            
            processed_results.append(result)
            
            # 更新处理时间
            processed_pairs[pair] = current_time
        
        except Exception as e:
            print(f"处理 {pair} 时出错: {e}")
    
    # 转换为DataFrame并导出
    df = pd.DataFrame(processed_results)
    
    # 重新排序列
    columns_order = [
        'pair', 'timestamp', 'spot_price', 'future_price', 
        'total_profit', 'spot_fee', 'future_fee', 'funding_cost', 
        'total_fee', 'slippage_loss', 'net_profit', 'net_return_rate'
    ]
    df = df[columns_order]
    
    # 按净收益降序排序
    df_sorted = df.sort_values('net_profit', ascending=False)
    
    # 导出Excel
    output_file = 'arbitrage_earnings_analysis.xlsx'
    df_sorted.to_excel(output_file, index=False)
    
    print(f"分析结果已导出到 {output_file}")
    print(f"总处理机会数: {len(processed_results)}")
    
    return df_sorted

# 执行处理
result = process_arbitrage_opportunities(
    'high_basis_opportunities.json', 
    slippage_rate=0.001  # 可以根据需要调整滑点率
)

# 打印一些统计信息
print("\n收益统计:")
print(f"平均净收益: {result['net_profit'].mean():.2f} USDT")
print(f"最大净收益: {result['net_profit'].max():.2f} USDT")
print(f"最小净收益: {result['net_profit'].min():.2f} USDT")