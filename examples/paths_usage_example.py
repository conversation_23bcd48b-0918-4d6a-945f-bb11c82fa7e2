#!/usr/bin/env python3
"""
新的路径管理系统使用示例
展示如何使用 paths.py 模块来简化路径操作
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('..'))


def old_way_example():
    """展示旧的复杂路径操作方式"""
    print("=" * 60)
    print("旧的路径操作方式（复杂且容易出错）")
    print("=" * 60)

    # 旧的方式：复杂的路径操作
    current_file = __file__
    project_root = os.path.abspath(os.path.join(os.path.dirname(current_file), '..'))
    config_dir = os.path.join(project_root, 'src', 'crypt_carry', 'config')
    data_dir = os.path.join(project_root, 'data')
    logs_dir = os.path.join(project_root, 'logs')
    web_data_dir = os.path.join(project_root, 'src', 'crypt_carry', 'web', 'data')

    print(f"❌ 项目根目录: {project_root}")
    print(f"❌ 配置目录: {config_dir}")
    print(f"❌ 数据目录: {data_dir}")
    print(f"❌ 日志目录: {logs_dir}")
    print(f"❌ Web数据目录: {web_data_dir}")

    print("\n问题:")
    print("- 代码冗长且重复")
    print("- 容易出错（路径层级计算错误）")
    print("- 难以维护（路径硬编码）")
    print("- 不同文件中需要重复相同的路径计算逻辑")


def new_way_example():
    """展示新的简洁路径操作方式"""
    print("\n" + "=" * 60)
    print("新的路径操作方式（简洁且可靠）")
    print("=" * 60)

    # 新的方式：使用paths模块
    from src.crypt_carry.utils.paths import (
        get_project_root, get_config_dir, get_data_dir,
        get_logs_dir, get_web_data_dir
    )

    print(f"✅ 项目根目录: {get_project_root()}")
    print(f"✅ 配置目录: {get_config_dir()}")
    print(f"✅ 数据目录: {get_data_dir()}")
    print(f"✅ 日志目录: {get_logs_dir()}")
    print(f"✅ Web数据目录: {get_web_data_dir()}")

    print("\n优势:")
    print("- 代码简洁明了")
    print("- 统一的路径管理")
    print("- 类型提示支持")
    print("- 自动目录创建功能")
    print("- 易于测试和维护")


def practical_example():
    """实际使用示例"""
    print("\n" + "=" * 60)
    print("实际使用示例")
    print("=" * 60)

    from src.crypt_carry.utils.paths import (
        get_data_dir, get_logs_dir, ensure_dir_exists,
        get_relative_path_from_project_root
    )

    # 创建一个示例数据文件
    example_data_dir = get_data_dir() / 'examples'
    ensure_dir_exists(example_data_dir)

    example_file = example_data_dir / 'sample_data.txt'
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write("这是一个示例数据文件\n")
        f.write("使用新的路径管理系统创建\n")

    print(f"✅ 创建示例文件: {example_file}")
    print(f"✅ 相对路径: {get_relative_path_from_project_root(example_file)}")

    # 创建日志目录示例
    log_file = get_logs_dir() / 'example.log'
    ensure_dir_exists(get_logs_dir())

    with open(log_file, 'w', encoding='utf-8') as f:
        f.write("示例日志文件\n")

    print(f"✅ 创建日志文件: {log_file}")
    print(f"✅ 相对路径: {get_relative_path_from_project_root(log_file)}")


def migration_benefits():
    """展示迁移的好处"""
    print("\n" + "=" * 60)
    print("迁移到新路径管理系统的好处")
    print("=" * 60)

    benefits = [
        "🎯 统一路径管理：所有路径定义集中在一个模块中",
        "🔧 易于维护：修改路径结构只需要更新一个文件",
        "🛡️ 类型安全：使用 pathlib.Path 对象，提供更好的类型提示",
        "📁 自动创建：ensure_dir_exists 函数自动创建必要的目录",
        "🔄 向后兼容：提供 ImportError 处理，确保平滑迁移",
        "🧪 易于测试：路径逻辑集中，便于单元测试",
        "📖 代码可读性：函数名清晰表达意图",
        "🚀 开发效率：减少重复代码，提高开发速度"
    ]

    for benefit in benefits:
        print(benefit)


def main():
    """主函数"""
    print("新的路径管理系统使用示例")
    print("=" * 60)

    old_way_example()
    new_way_example()
    practical_example()
    migration_benefits()

    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("✅ 新的路径管理系统已成功实现")
    print("✅ 提供了更简洁、可靠的路径操作方式")
    print("✅ 支持向后兼容，确保平滑迁移")
    print("✅ 提高了代码的可维护性和可读性")


if __name__ == "__main__":
    main()
