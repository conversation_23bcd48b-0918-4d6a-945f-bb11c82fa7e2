#!/usr/bin/env python3
"""
消息队列使用示例

演示如何使用新的消息队列架构进行套保操作
"""
import asyncio
import json
import requests
from datetime import datetime

# API 基础地址
API_BASE_URL = "http://localhost:5001"

def test_api_connection():
    """测试 API 连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Web API 服务连接正常")
            return True
        else:
            print(f"❌ Web API 服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到 Web API 服务: {str(e)}")
        return False

def check_worker_status():
    """检查 Worker 状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/hedging/worker/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                worker_status = data.get('worker_status', {})
                worker_count = worker_status.get('worker_count', 0)
                if worker_count > 0:
                    print(f"✅ 套保策略 Worker 正常运行 ({worker_count} 个 Worker)")
                    return True
                else:
                    print("❌ 套保策略 Worker 未运行")
                    return False
            else:
                print(f"❌ Worker 状态查询失败: {data.get('error')}")
                return False
        else:
            print(f"❌ Worker 状态查询失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Worker 状态查询异常: {str(e)}")
        return False

def create_hedging_order_example():
    """创建套保订单示例"""
    print("\n=== 创建套保订单示例 ===")
    
    # 套保订单数据
    order_data = {
        "conditions": [
            {
                "enabled": True,
                "condition_type": "price_difference",
                "trigger_value": 50.0,
                "comparison_operator": ">",
                "description": "价差超过50 USDT时触发"
            }
        ],
        "long_exchange": "binance",
        "long_symbol": "BTC/USDT",
        "long_is_spot": True,
        "short_exchange": "okx", 
        "short_symbol": "BTC/USDT",
        "short_is_spot": False,
        "amount": 1000.0,
        "amount_currency": "USDT",
        "priority": 1
    }
    
    try:
        print("📤 提交套保订单...")
        response = requests.post(
            f"{API_BASE_URL}/api/hedging/orders/hedging",
            json=order_data,
            timeout=30
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print(f"✅ 套保订单创建成功")
                print(f"   订单ID: {result.get('order_id')}")
                print(f"   执行类型: {result.get('execution_type')}")
                print(f"   消息: {result.get('message')}")
                
                # 如果有任务ID，查询任务状态
                conditions = result.get('conditions', [])
                if conditions and 'task_id' in conditions[0]:
                    task_id = conditions[0]['task_id']
                    print(f"   任务ID: {task_id}")
                    
                    # 查询任务状态
                    check_task_status(task_id)
                
                return result
            else:
                print(f"❌ 套保订单创建失败: {result.get('error')}")
                return None
        else:
            print(f"❌ 套保订单创建失败: HTTP {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 套保订单创建异常: {str(e)}")
        return None

def create_single_order_example():
    """创建单独订单示例"""
    print("\n=== 创建单独订单示例 ===")
    
    # 单独订单数据
    order_data = {
        "exchange": "binance",
        "symbol": "BTC/USDT",
        "side": "buy",
        "order_type": "market",
        "amount": 100.0,
        "amount_currency": "USDT",
        "is_spot": True
    }
    
    try:
        print("📤 提交单独订单...")
        response = requests.post(
            f"{API_BASE_URL}/api/hedging/orders/single",
            json=order_data,
            timeout=30
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print(f"✅ 单独订单创建成功")
                print(f"   消息: {result.get('message')}")
                
                order_info = result.get('order', {})
                print(f"   订单ID: {order_info.get('order_id')}")
                print(f"   交易所: {order_info.get('exchange')}")
                print(f"   交易对: {order_info.get('symbol')}")
                print(f"   方向: {order_info.get('side')}")
                print(f"   类型: {order_info.get('order_type')}")
                
                return result
            else:
                print(f"❌ 单独订单创建失败: {result.get('error')}")
                return None
        else:
            print(f"❌ 单独订单创建失败: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 单独订单创建异常: {str(e)}")
        return None

def check_task_status(task_id):
    """查询任务状态"""
    print(f"\n=== 查询任务状态: {task_id} ===")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/api/hedging/tasks/{task_id}/status",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_status = result.get('task_status', {})
                print(f"✅ 任务状态查询成功")
                print(f"   任务ID: {task_status.get('task_id')}")
                print(f"   状态: {task_status.get('status')}")
                print(f"   是否完成: {task_status.get('ready')}")
                print(f"   是否成功: {task_status.get('successful')}")
                
                if task_status.get('ready') and task_status.get('result'):
                    result_data = task_status.get('result')
                    print(f"   执行结果: {result_data}")
                
                return task_status
            else:
                print(f"❌ 任务状态查询失败: {result.get('error')}")
                return None
        else:
            print(f"❌ 任务状态查询失败: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 任务状态查询异常: {str(e)}")
        return None

def get_hedging_status():
    """获取套保策略状态"""
    print("\n=== 获取套保策略状态 ===")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/api/hedging/orders/hedging/status",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 套保策略状态查询成功")
                print(f"   是否运行: {result.get('is_running')}")
                print(f"   条件总数: {result.get('total_conditions')}")
                print(f"   活跃条件: {result.get('active_conditions')}")
                
                conditions = result.get('conditions', [])
                if conditions:
                    print(f"   条件列表:")
                    for condition in conditions:
                        print(f"     - {condition.get('condition_id')}: {condition.get('status')}")
                
                return result
            else:
                print(f"❌ 套保策略状态查询失败: {result.get('error')}")
                return None
        else:
            print(f"❌ 套保策略状态查询失败: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 套保策略状态查询异常: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 消息队列架构使用示例")
    print("=" * 50)
    
    # 1. 测试 API 连接
    if not test_api_connection():
        print("\n❌ 请先启动 Web API 服务:")
        print("   cd src/crypt_carry/web")
        print("   ./start_web_services.sh start")
        return
    
    # 2. 检查 Worker 状态
    if not check_worker_status():
        print("\n❌ 请先启动套保策略 Worker:")
        print("   src/scripts/hedging/start_hedging_worker.sh start")
        print("\n💡 或者使用统一启动脚本:")
        print("   src/scripts/start_all_services.sh start")
        return
    
    # 3. 获取当前套保状态
    get_hedging_status()
    
    # 4. 创建套保订单示例
    hedging_result = create_hedging_order_example()
    
    # 5. 创建单独订单示例
    single_result = create_single_order_example()
    
    # 6. 再次查看套保状态
    get_hedging_status()
    
    print("\n✅ 示例执行完成!")
    print("\n📚 更多信息:")
    print("   - API文档: http://localhost:5001/api/docs")
    print("   - 前端界面: http://localhost:3000")
    print("   - 部署指南: docs/message_queue_setup.md")

if __name__ == "__main__":
    main()
