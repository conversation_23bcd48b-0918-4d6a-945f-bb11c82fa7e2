import asyncio
import json

from carry.config.config_loader import Config<PERSON>oa<PERSON>
from carry.exchange.okx_client import OkxClient
import logging

# 创建logger，使用模块名作为logger名称
logger = logging.getLogger(__name__)


class OkxSpotTest:
    def __init__(self):
        self.config = ConfigLoader.get_base_config()
        self.exchange = OkxClient(
            api_key=self.config.get('OKX', {}).get('API_KEY', ''),
            api_secret=self.config.get('OKX', {}).get('API_SECRET', ''),
            passphrase=self.config.get('OKX', {}).get('PASSPHRASE', ''),
            proxy=self.config.get('PROXY')
        )

        # 设置代理
        if self.config.get('PROXY', {}).get('enabled', False):
            self.exchange.httpProxy = self.config.get('PROXY', {}).get('http', '')
            self.exchange.wsProxy = self.config.get('PROXY', {}).get('http', '')
            logger.info(f"使用代理: {self.config.get('PROXY', {}).get('http', '')}")

        self.spot_symbol = 'BTC/USDT'
        self.futures_symbol = 'BTC/USDT:USDT'  # 永续合约符号

    async def connect(self):
        """连接并加载市场数据"""
        try:
            await self.exchange.load_markets()
            logger.info("市场数据加载成功")
        except Exception as e:
            logger.error(f"加载市场数据失败: {str(e)}")
            raise

    async def watch_spot_ticker(self):
        """监听现货行情"""
        message_count = 0
        try:
            while True:
                ticker = await self.exchange.watch_ticker(self.spot_symbol)
                message_count += 1

                # 提取关键信息
                price = float(ticker['last'])  # 最新成交价
                bid = float(ticker['bid'])  # 买一价
                ask = float(ticker['ask'])  # 卖一价
                mid_price = (bid + ask) / 2  # 中间价

                if message_count % 100 == 0:  # 每100条消息打印一次详细信息
                    logger.debug(f"现货原始消息: {json.dumps(ticker, indent=2)}")

                logger.info(f"[现货] Symbol: {self.spot_symbol} - "
                            f"最新价: {price:.2f} - "
                            f"买一: {bid:.2f}({ticker['bidVolume']}) - "
                            f"卖一: {ask:.2f}({ticker['askVolume']}) - "
                            f"中间价: {mid_price:.2f} - "
                            f"消息计数: {message_count}")

        except Exception as e:
            logger.error(f"现货监听错误: {str(e)}")
            raise

    async def watch_futures_ticker(self):
        """监听合约行情"""
        message_count = 0
        try:
            while True:
                ticker = await self.exchange.watch_ticker(self.futures_symbol)
                message_count += 1

                # 提取关键信息
                price = float(ticker['last'])  # 最新成交价
                bid = float(ticker['bid'])  # 买一价
                ask = float(ticker['ask'])  # 卖一价
                mid_price = (bid + ask) / 2  # 中间价

                if message_count % 100 == 0:  # 每100条消息打印一次详细信息
                    logger.debug(f"合约原始消息: {json.dumps(ticker, indent=2)}")

                logger.info(f"[合约] Symbol: {self.futures_symbol} - "
                            f"最新价: {price:.2f} - "
                            f"买一: {bid:.2f}({ticker['bidVolume']}) - "
                            f"卖一: {ask:.2f}({ticker['askVolume']}) - "
                            f"中间价: {mid_price:.2f} - "
                            f"消息计数: {message_count}")

        except Exception as e:
            logger.error(f"合约监听错误: {str(e)}")
            raise

    async def close(self):
        """关闭连接"""
        try:
            await self.exchange.close()
            logger.info("连接已关闭")
        except Exception as e:
            logger.error(f"关闭连接时发生错误: {str(e)}")

    async def run(self):
        """运行测试"""
        try:
            await self.connect()
            # 创建两个监听任务
            spot_task = asyncio.create_task(self.watch_spot_ticker())
            futures_task = asyncio.create_task(self.watch_futures_ticker())

            # 等待任务完成
            await asyncio.gather(spot_task, futures_task)

        except Exception as e:
            logger.error(f"运行错误: {str(e)}", exc_info=True)
        finally:
            await self.close()


async def main():
    client = OkxSpotTest()
    try:
        logger.info("启动OKX WebSocket测试...")
        await client.run()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        await client.close()
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
        await client.close()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序正常退出")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
