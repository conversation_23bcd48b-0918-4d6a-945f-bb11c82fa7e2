import aiohttp
import asyncio
import json


async def funding_rate_subscription():
    url = "wss://fstream.binance.com/ws"  # Binance U本位合约 WebSocket 地址
    subscription_message = {
        "method": "SUBSCRIBE",
        "params": ["btcusdt@fundingRate", "ethusdt@fundingRate"],  # 订阅资金费率的交易对
        "id": 1
    }

    # 配置代理
    proxy = "http://127.0.0.1:7890"  # 替换为你的代理地址和端口

    async with aiohttp.ClientSession() as session:
        async with session.ws_connect(url, proxy=proxy) as ws:
            # 发送订阅请求
            await ws.send_json(subscription_message)
            print("订阅资金费率成功，等待数据...")

            # 持续接收数据
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    # 处理推送的资金费率数据
                    if "e" in data and data["e"] == "fundingRate":
                        print("资金费率数据:", data)
                    else:
                        print("收到其他数据:", data)
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    print("WebSocket 错误:", msg.data)
                    break


# 运行事件循环
if __name__ == "__main__":
    try:
        asyncio.run(funding_rate_subscription())
    except KeyboardInterrupt:
        print("退出程序")
