import aiohttp
import asyncio


async def subscribe_book_ticker():
    url = "wss://stream.binance.com:9443/ws/btcusdt@bookTicker"
    # 配置代理
    proxy = "http://127.0.0.1:7890"  # 替换为你的代理地址和端口

    async with aiohttp.ClientSession() as session:
        async with session.ws_connect(url, proxy=proxy) as ws:
            print("连接成功，开始接收数据...")
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    # 解析接收到的消息
                    data = msg.json()
                    print(f"买价: {data['b']}, 买量: {data['B']}, 卖价: {data['a']}, 卖量: {data['A']}")
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    print("WebSocket 连接错误")
                    break


if __name__ == "__main__":
    try:
        asyncio.run(subscribe_book_ticker())
    except KeyboardInterrupt:
        print("程序已停止")
