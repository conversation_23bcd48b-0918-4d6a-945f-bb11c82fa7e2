# 基差计算测试示例

## 📋 **测试概述**

这个测试文件展示了如何正确使用 `src/crypt_carry/utils/calc.py` 中的计算函数来进行基差率、价值差异、预期收益等计算。

## 🔧 **修复的问题**

### **原问题**
- 测试文件试图导入不存在的 `BasisCalculator` 类
- 依赖了复杂的配置和套利管理器
- 测试逻辑不清晰，没有实际验证计算结果

### **解决方案**
- ✅ 直接使用 `calc.py` 中的计算函数
- ✅ 简化测试设置，移除不必要的依赖
- ✅ 添加详细的断言验证计算结果
- ✅ 增加边界情况测试

## 🧪 **测试用例**

### **1. 基础基差计算测试**
```python
def test_basis_calculation(self):
    # Binance: 现货50000, 期货50500
    binance_basis_rate = calculate_basis_rate(50000.0, 50500.0)
    # 预期: (50500 - 50000) / 50000 = 0.01 = 1%
    
    # OKX: 现货49800, 期货50200  
    okx_basis_rate = calculate_basis_rate(49800.0, 50200.0)
    # 预期: (50200 - 49800) / 49800 ≈ 0.008032 = 0.8032%
```

### **2. 跨交易所基差对比测试**
```python
def test_cross_exchange_basis(self):
    # 极端价差情况
    # Binance: 现货50000, 期货60000 (20%基差率)
    # OKX: 现货60000, 期货51000 (-15%基差率)
    
    # 测试价差套利机会
    # 现货价差: 60000 - 50000 = 10000 USDT (20%)
    # 期货价差: 60000 - 51000 = 9000 USDT (17.65%)
```

### **3. 重新平衡计算测试**
```python
def test_rebalance_calculation(self):
    # 不平衡持仓: 1.0 BTC现货 vs 0.8 BTC期货
    # 价值差异: 50000 - 40400 = 9600 USDT (19.2%)
    # 建议: 买入0.190099 BTC期货来平衡
```

### **4. 边界情况测试**
```python
def test_edge_cases(self):
    # 零价格、负价格、相同价格
    # 负基差率情况
    # 零收益率和高收益率计算
```

## 📊 **测试结果**

```
开始执行基差计算测试...

✅ Binance基差率计算: 0.010000 (1.0000%)
✅ OKX基差率计算: 0.008032 (0.8032%)
✅ Binance发现套利机会: 基差500.0 USDT, 基差率1.0000%

✅ 跨交易所基差对比:
   - Binance基差率: 20.00% (极高)
   - OKX基差率: -15.00% (负基差)
   - 现货价差: 10000.0 USDT (20.00%)
   - 期货价差: 9000.0 USDT (17.65%)

✅ 重新平衡计算:
   - 持仓不平衡: 19.2%价值差异
   - 建议: 买入0.190099 BTC期货

✅ 边界情况测试:
   - 零价格基差率: 0
   - 负基差率: -2.00%
   - 高收益率: 2.1000%

----------------------------------------------------------------------
Ran 4 tests in 0.000s
OK
```

## 🎯 **使用的计算函数**

### **1. calculate_basis_rate(spot_price, futures_price)**
```python
# 计算基差率
basis_rate = calculate_basis_rate(50000.0, 50500.0)
# 返回: 0.01 (1%)
```

### **2. calculate_value_delta(spot_value, futures_value)**
```python
# 计算价值差异
delta_value, delta_percent = calculate_value_delta(50000.0, 40400.0)
# 返回: (9600.0, 0.192) - 差值9600, 差值百分比19.2%
```

### **3. calculate_expected_profit(funding_rate, basis_rate)**
```python
# 计算预期收益率
profit = calculate_expected_profit(0.001, 0.02)
# 返回: 0.021 (2.1%) = |0.1%| + |2%|
```

### **4. calculate_rebalance_amount(spot_amount, futures_amount, spot_price, futures_price)**
```python
# 计算重新平衡金额
delta_amount, side, action = calculate_rebalance_amount(1.0, 0.8, 50000.0, 50500.0)
# 返回: (0.190099, 'futures', 'buy') - 买入0.19 BTC期货
```

## 🌟 **测试优势**

### **1. 直接测试计算逻辑**
- 不依赖复杂的外部组件
- 专注于核心计算函数的正确性
- 易于理解和维护

### **2. 全面的验证**
- 精确的数值断言验证
- 边界情况覆盖
- 实际业务场景模拟

### **3. 清晰的输出**
- 详细的计算过程展示
- 百分比和绝对值双重显示
- 套利机会识别提示

### **4. 可扩展性**
- 易于添加新的测试用例
- 可以测试不同的市场条件
- 支持多种计算场景

## 🔧 **运行测试**

```bash
# 在tests目录下运行
cd tests
python test_basis.py

# 或者使用unittest
python -m unittest test_basis.py -v
```

## 📝 **注意事项**

1. **路径设置**: 测试文件自动添加了正确的Python路径
2. **依赖最小化**: 只依赖核心计算函数，不需要配置文件
3. **数值精度**: 使用 `assertAlmostEqual` 进行浮点数比较
4. **实际应用**: 测试用例模拟了真实的交易场景

这个修复后的测试文件现在可以正确地验证基差计算、价值差异计算、预期收益计算等核心功能，为项目的数学计算提供了可靠的测试保障。
