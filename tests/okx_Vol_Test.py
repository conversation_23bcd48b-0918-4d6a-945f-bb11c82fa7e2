import requests
import json

url = "https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP"
response = requests.get(url)
data = response.json()

# 直接查看 API 返回的数据
print(data)
print(json.dumps(data))

vol = "3813046.31"
usdt_volume = float(data["data"][0]["vol24h"]) * float(data["data"][0]["last"])
print("24 小时 USDT 交易量:", usdt_volume)
usdt_volume = float(vol) * float(data["data"][0]["last"])
print("24 小时 USDT 交易量:", usdt_volume)
