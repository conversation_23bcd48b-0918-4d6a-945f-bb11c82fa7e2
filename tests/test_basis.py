import unittest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

from crypt_carry.utils.calc import (
    calculate_basis_rate,
    calculate_value_delta,
    calculate_expected_profit,
    calculate_rebalance_amount
)


class TestBasisCalculation(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        # 简化设置，只测试计算函数
        pass

    def test_basis_calculation(self):
        """测试基差计算逻辑"""
        # 测试数据 - Binance交易所的现货和期货价格
        binance_spot_price = 50000.0
        binance_futures_price = 50500.0

        # 使用calc.py中的函数计算基差率
        binance_basis_rate = calculate_basis_rate(binance_spot_price, binance_futures_price)

        # 验证计算结果
        expected_basis_rate = (50500 - 50000) / 50000  # 0.01 = 1%
        self.assertAlmostEqual(binance_basis_rate, expected_basis_rate, places=6)

        print(f"Binance基差率计算: {binance_basis_rate:.6f} ({binance_basis_rate*100:.4f}%)")

        # 测试OKX交易所
        okx_spot_price = 49800.0
        okx_futures_price = 50200.0

        okx_basis_rate = calculate_basis_rate(okx_spot_price, okx_futures_price)
        expected_okx_basis_rate = (50200 - 49800) / 49800  # 约0.008032 = 0.8032%
        self.assertAlmostEqual(okx_basis_rate, expected_okx_basis_rate, places=6)

        print(f"OKX基差率计算: {okx_basis_rate:.6f} ({okx_basis_rate*100:.4f}%)")

        # 测试套利机会识别
        if abs(binance_basis_rate) > 0.005:  # 基差率大于0.5%
            print("Binance发现套利机会:")
            print(f"  基差: {binance_futures_price - binance_spot_price} USDT")
            print(f"  基差率: {binance_basis_rate*100:.4f}%")
            print("  建议: 可以考虑套利操作")

        print("基差计算测试完成")

    def test_cross_exchange_basis(self):
        """测试跨交易所基差计算和套利机会识别"""
        # 测试数据 - 模拟极端价差情况
        binance_spot_price = 50000.0
        binance_futures_price = 60000.0  # 极高的期货价格

        okx_spot_price = 60000.0  # 极高的现货价格
        okx_futures_price = 51000.0

        # 计算各交易所的基差率
        binance_basis_rate = calculate_basis_rate(binance_spot_price, binance_futures_price)
        okx_basis_rate = calculate_basis_rate(okx_spot_price, okx_futures_price)

        print(f"Binance基差率: {binance_basis_rate:.6f} ({binance_basis_rate*100:.2f}%)")
        print(f"OKX基差率: {okx_basis_rate:.6f} ({okx_basis_rate*100:.2f}%)")

        # 验证计算结果
        expected_binance_basis = (60000 - 50000) / 50000  # 0.2 = 20%
        expected_okx_basis = (51000 - 60000) / 60000  # -0.15 = -15%

        self.assertAlmostEqual(binance_basis_rate, expected_binance_basis, places=6)
        self.assertAlmostEqual(okx_basis_rate, expected_okx_basis, places=6)

        # 测试跨交易所价差套利机会
        # 场景1: Binance现货 vs OKX现货 (50000 vs 60000)
        spot_price_diff = okx_spot_price - binance_spot_price
        spot_price_diff_rate = spot_price_diff / binance_spot_price
        print(f"现货价差: {spot_price_diff} USDT ({spot_price_diff_rate*100:.2f}%)")

        # 场景2: Binance期货 vs OKX期货 (60000 vs 51000)
        futures_price_diff = binance_futures_price - okx_futures_price
        futures_price_diff_rate = futures_price_diff / okx_futures_price
        print(f"期货价差: {futures_price_diff} USDT ({futures_price_diff_rate*100:.2f}%)")

        # 测试价值差异计算
        spot_value_1 = 1.0 * binance_spot_price  # 1 BTC现货价值
        futures_value_1 = 1.0 * binance_futures_price  # 1 BTC期货价值

        delta_value, delta_percent = calculate_value_delta(spot_value_1, futures_value_1)
        print(f"Binance价值差异: {delta_value} USDT ({delta_percent*100:.2f}%)")

        # 测试预期收益计算（假设资金费率）
        funding_rate = 0.001  # 0.1%资金费率
        expected_profit_binance = calculate_expected_profit(funding_rate, binance_basis_rate)
        expected_profit_okx = calculate_expected_profit(funding_rate, okx_basis_rate)

        print(f"Binance预期收益率: {expected_profit_binance*100:.4f}%")
        print(f"OKX预期收益率: {expected_profit_okx*100:.4f}%")

        # 识别高基差率的套利机会
        threshold = 0.05  # 5%阈值

        if abs(binance_basis_rate) > threshold:
            print("Binance发现高基差套利机会:")
            print(f"  基差: {binance_futures_price - binance_spot_price} USDT")
            print(f"  基差率: {binance_basis_rate*100:.2f}%")
            print(f"  预期收益率: {expected_profit_binance*100:.4f}%")

        if abs(okx_basis_rate) > threshold:
            print("OKX发现高基差套利机会:")
            print(f"  基差: {okx_futures_price - okx_spot_price} USDT")
            print(f"  基差率: {okx_basis_rate*100:.2f}%")
            print(f"  预期收益率: {expected_profit_okx*100:.4f}%")

        print("跨交易所基差计算测试完成")

    def test_rebalance_calculation(self):
        """测试重新平衡计算"""
        # 测试场景：现货和期货持仓不平衡
        spot_amount = 1.0  # 1 BTC现货
        futures_amount = 0.8  # 0.8 BTC期货
        spot_price = 50000.0
        futures_price = 50500.0

        # 计算需要重新平衡的金额
        delta_amount, adjustment_side, action = calculate_rebalance_amount(
            spot_amount, futures_amount, spot_price, futures_price, threshold=0.02
        )

        print(f"持仓情况:")
        print(f"  现货: {spot_amount} BTC @ {spot_price} USDT = {spot_amount * spot_price} USDT")
        print(f"  期货: {futures_amount} BTC @ {futures_price} USDT = {futures_amount * futures_price} USDT")
        print(f"重新平衡建议:")
        print(f"  调整数量: {delta_amount:.6f} BTC")
        print(f"  调整方向: {adjustment_side}")
        print(f"  操作类型: {action}")

        # 验证计算逻辑
        spot_value = spot_amount * spot_price  # 50000
        futures_value = futures_amount * futures_price  # 40400
        value_diff = spot_value - futures_value  # 9600
        value_diff_percent = value_diff / max(spot_value, futures_value)  # 9600/50000 = 0.192

        # 由于差异19.2%大于阈值2%，应该需要调整
        self.assertGreater(abs(value_diff_percent), 0.02)
        self.assertGreater(delta_amount, 0)
        self.assertEqual(adjustment_side, 'futures')  # 应该增加期货持仓
        self.assertEqual(action, 'buy')

        # 测试平衡情况
        balanced_spot = 1.0
        balanced_futures = 1.0
        delta_balanced, side_balanced, action_balanced = calculate_rebalance_amount(
            balanced_spot, balanced_futures, spot_price, futures_price, threshold=0.02
        )

        print(f"\n平衡持仓测试:")
        print(f"  调整数量: {delta_balanced}")
        print(f"  调整方向: {side_balanced}")
        print(f"  操作类型: {action_balanced}")

        # 平衡情况下不应该需要调整
        self.assertEqual(delta_balanced, 0)
        self.assertEqual(side_balanced, '')
        self.assertEqual(action_balanced, '')

        print("重新平衡计算测试完成")

    def test_edge_cases(self):
        """测试边界情况"""
        print("测试边界情况:")

        # 测试零价格情况
        zero_basis = calculate_basis_rate(0, 50000)
        self.assertEqual(zero_basis, 0)
        print(f"  零现货价格基差率: {zero_basis}")

        # 测试负价格情况（理论上不应该发生，但测试健壮性）
        negative_basis = calculate_basis_rate(-100, 50000)
        self.assertEqual(negative_basis, 0)
        print(f"  负现货价格基差率: {negative_basis}")

        # 测试相同价格情况
        same_price_basis = calculate_basis_rate(50000, 50000)
        self.assertEqual(same_price_basis, 0)
        print(f"  相同价格基差率: {same_price_basis}")

        # 测试期货价格低于现货价格（负基差）
        negative_spread_basis = calculate_basis_rate(50000, 49000)
        expected_negative = (49000 - 50000) / 50000  # -0.02 = -2%
        self.assertAlmostEqual(negative_spread_basis, expected_negative, places=6)
        print(f"  负基差率: {negative_spread_basis:.6f} ({negative_spread_basis*100:.2f}%)")

        # 测试价值差异计算的边界情况
        zero_delta, zero_percent = calculate_value_delta(0, 0)
        self.assertEqual(zero_delta, 0)
        self.assertEqual(zero_percent, 0)
        print(f"  零价值差异: {zero_delta}, {zero_percent}")

        # 测试预期收益计算
        zero_profit = calculate_expected_profit(0, 0)
        self.assertEqual(zero_profit, 0)
        print(f"  零收益率: {zero_profit}")

        high_profit = calculate_expected_profit(0.001, -0.02)  # 资金费率0.1%，基差率-2%
        expected_high = abs(0.001) + abs(-0.02)  # 0.021 = 2.1%
        self.assertAlmostEqual(high_profit, expected_high, places=6)
        print(f"  高收益率: {high_profit:.6f} ({high_profit*100:.4f}%)")

        print("边界情况测试完成")


def main():
    print("开始执行基差计算测试...")

    try:
        unittest.main()
        print("所有测试通过！")
    except Exception as e:
        print(f"测试失败：{e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
