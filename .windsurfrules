1. 优先阅读项目文档‌.当超过 24 小时未沟通时，必须优先阅读 README.md 和 docs/carry_dic.md 以确认项目目标。

2. 文档与代码同步‌.修改文件结构或逻辑时，必须检查并更新 README.md 和 docs/carry_dic.md 中的相关描述。

3. 全局逻辑分析原则‌.分析文件时必须扫描全局逻辑结构，标记所有依赖关系，禁止仅聚焦局部代码。

4. 代码变更规范‌.所有代码修改必须通过 edit_file 工具展示变更，严禁直接覆盖原始文件。

5. 多交易所兼容性‌.针对 okx/binance 等交易所的代码修改时，必须检查其他交易所的同类型接口并同步更新。

6. 代码体积控制‌.单个 Python 文件超过 600 行必须拆分（紧急情况除外），目标模块大小控制在 300 行以内。

7. 模块化开发规范‌.功能模块必须按单一职责拆分到独立文件，目录结构需符合 src/{core,utils,config} 层级标准。

8. 命名与复用规则‌.包/模块命名需通过 pycodestyle 检查，可复用代码必须封装到 utils/ 或 common/ 目录。

9. 代码风格强制要求‌.所有 Python 代码必须通过 flake8 检查（PEP 8 合规），mypy 静态类型验证覆盖率不低于 90%。
