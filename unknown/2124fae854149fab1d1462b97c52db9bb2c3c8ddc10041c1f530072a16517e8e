#!/bin/bash
# 一键安装开发模式脚本 (适用于conda环境)

echo "===== 开始在conda环境中安装 crypt_carry 开发环境 ====="

# 检查Python版本
python_version=$(python --version 2>&1 | awk '{print $2}')
echo "检测到Python版本: $python_version"

# 提取主版本和次版本
major_version=$(echo $python_version | cut -d. -f1)
minor_version=$(echo $python_version | cut -d. -f2)

# 检查版本是否至少为3.10
if [[ $major_version -lt 3 || ($major_version -eq 3 && $minor_version -lt 10) ]]; then
    echo "警告: 推荐使用Python 3.10以上版本，当前版本为 $python_version"
    read -p "是否继续安装? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
fi

# 确认当前是否在conda环境中
if [[ -z "$CONDA_PREFIX" ]]; then
    echo "警告: 未检测到激活的conda环境"
    read -p "建议先激活conda环境再运行此脚本。是否继续? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
else
    echo "在conda环境中安装: $CONDA_PREFIX"
fi

# 升级pip
echo "正在升级pip..."
pip install --upgrade pip

# 安装依赖
echo "正在安装项目依赖..."
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 安装开发模式
echo "正在安装开发模式依赖..."
pip install -e ".[dev]"

# 验证安装
echo "验证安装..."
pip list | grep crypt_carry

echo "===== crypt_carry 开发环境安装完成 ====="
echo "启动应用: python -m scripts.run_carry"
echo "启动Web服务: python -m crypt_carry.web.app"
